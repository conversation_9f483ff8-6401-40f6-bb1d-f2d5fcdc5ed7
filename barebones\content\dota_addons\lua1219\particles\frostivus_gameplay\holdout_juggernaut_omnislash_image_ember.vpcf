<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 128
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderTrails_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_Decay_0,
		&C_OP_FadeInSimple_0,
		&C_OP_RampScalarLinear_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_VectorNoise_0,
		&C_OP_OscillateVector_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_CreateFromParentParticles_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_RandomTrailLength_0,
		&C_INIT_RandomColor_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		&C_OP_TwistAroundAxis_0
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderTrails C_OP_RenderTrails_0
{
	float m_flSelfIllumAmount = 2.000000
	string m_hTexture = "materials\\particle\\sparks\\sparks.vtex"
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float(3) m_Gravity = ( 0.000000, 0.000000, 400.000000 )
	float m_fDrag = 0.075000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 0.250000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
}

C_OP_RampScalarLinear C_OP_RampScalarLinear_0
{
	string m_Notes = ""
	float m_RateMax = 40.000000
	float m_RateMin = -40.000000
	int m_nField = 4
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	int(4) m_ColorFade = ( 64, 0, 0, 255 )
	string m_Notes = ""
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	float m_fl4NoiseScale = 0.300000
	bool m_bAdditive = true
	float(3) m_vecOutputMax = ( 16.000000, 16.000000, 8.000000 )
	float(3) m_vecOutputMin = ( -16.000000, -16.000000, -8.000000 )
	int m_nFieldOutput = 0
	string m_Notes = ""
}

C_OP_OscillateVector C_OP_OscillateVector_0
{
	float(3) m_FrequencyMax = ( 2.000000, 2.000000, 2.000000 )
	float(3) m_FrequencyMin = ( 0.100000, 0.100000, 0.100000 )
	float(3) m_RateMax = ( 44.000000, 44.000000, 44.000000 )
	float(3) m_RateMin = ( -44.000000, -44.000000, -44.000000 )
	bool m_bOffset = true
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMax = 1.000000
	float m_fLifetimeMin = 0.250000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMin = 2.000000
	string m_Notes = ""
	float m_flRadiusMax = 9.000000
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	float m_flNoiseScale = 0.250000
	string m_Notes = ""
	float(3) m_vecOutputMin = ( -214.000000, -214.000000, 0.000000 )
	float(3) m_vecOutputMax = ( 214.000000, 214.000000, 200.000000 )
	float m_flNoiseScaleLoc = 3.000000
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMax = ( 32.000000, -42.000000, 128.000000 )
	float(3) m_OffsetMin = ( -32.000000, -12.000000, 40.000000 )
	bool m_bLocalCoords = true
}

C_INIT_CreateFromParentParticles C_INIT_CreateFromParentParticles_0
{
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 7
	int m_nSequenceMin = 4
	string m_Notes = ""
}

C_INIT_RandomTrailLength C_INIT_RandomTrailLength_0
{
	float m_flMinLength = 0.050000
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 236, 183, 20, 255 )
	int(4) m_ColorMax = ( 255, 65, 14, 255 )
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 264.000000
}

C_OP_TwistAroundAxis C_OP_TwistAroundAxis_0
{
	float m_fForceAmount = 140.000000
	string m_Notes = ""
}