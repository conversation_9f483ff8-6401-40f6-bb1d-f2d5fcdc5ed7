// Tutorial how to use #base:
// https://moddota.com/tools/combining-kv-files-using-base
#base "units/npc_dota_custom_dummy_unit.txt"
#base "units/npc_dota_custom_illusion_base.txt"
#base "units/npc_dota_miniboss_custom.txt"

"DOTAUnits"
{
    "attack_tower"
    {
        // General
        //----------------------------------------------------------------
        "BaseClass"		"npc_dota_creature"
        "Model"			"models/heroes/abaddon/abaddon.vmdl"
        "ModelScale"		"1.2"
        "Level"			"1"
        "HealthBarOffset"	"140"
        "HasInventory"		"0"
     
        // Abilities
        //----------------------------------------------------------------
        "Ability1"		""
        "Ability2"		""
     
        // Armor
        //----------------------------------------------------------------
        "ArmorPhysical"		"2"
        "MagicalResistance"	"0"
     
        // Attack
        //----------------------------------------------------------------
        "AttackCapabilities"	"DOTA_UNIT_CAP_MELEE_ATTACK"
        "AttackDamageType"	"DAMAGE_TYPE_ArmorPhysical"
        "AttackDamageMin"	"12.0"
        "AttackDamageMax"	"13.0"
        "AttackRate"		"1.35"
        "AttackAnimationPoint"	"0.5"
        "AttackAcquisitionRange" "500"
        "AttackRange"		"90"
     
        // Bounty
        //----------------------------------------------------------------
        "BountyGoldMin"		"26.0"
        "BountyGoldMax"		"38.0"
     
        // Bounds
        //----------------------------------------------------------------
        "BoundsHullName"	"100"
        "RingRadius"		"100"
     
        // Movement
        //----------------------------------------------------------------
        "MovementCapabilities"	"DOTA_UNIT_CAP_MOVE_GROUND"
        "MovementSpeed"		"270"
        "MovementTurnRate"	"0.6"
     
        // Status
        //----------------------------------------------------------------
        "StatusHealth"		"420"
        "StatusHealthRegen"	"0.25"
        "StatusMana"		"0"
        "StatusManaRegen"	"0"
     
        // Vision
        //----------------------------------------------------------------
        "VisionDaytimeRange"	"1400"
        "VisionNighttimeRange"	"800"
     
        // Team
        //----------------------------------------------------------------
        "TeamName"		"DOTA_TEAM_NEUTRALS"
        "CombatClassAttack"	"DOTA_COMBAT_CLASS_ATTACK_BASIC"
        "CombatClassDefend"	"DOTA_COMBAT_CLASS_DEFEND_STRONG"
        "UnitRelationShipClass"	"DOTA_NPC_UNIT_RELATIONSHIP_TYPE_DEFAULT"
     
        // Creature Data
        //----------------------------------------------------------------
        "Creature"
        {
            "DisableClumpingBehavior"	"1"
            "AttachWearables"
            {
                "Wearable1" { "ItemDef" "63" }
                "Wearable2" { "ItemDef" "64" }
                "Wearable3" { "ItemDef" "65" }
                "Wearable4" { "ItemDef" "66" }
                "Wearable5" { "ItemDef" "67" }
                "Wearable6" { "ItemDef" "68" }
            }
        }
    }
}
