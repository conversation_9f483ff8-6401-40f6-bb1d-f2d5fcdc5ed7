<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 32
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 36.000000
	float m_flConstantLifespan = 0.400000
	int(4) m_ConstantColor = ( 124, 250, 207, 155 )
	float m_flMaxRecreationTime = -1.000000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderRopes_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_FadeInSimple_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_PositionLock_0,
		&C_OP_VectorNoise_0,
		&C_OP_RemapParticleCountOnScalarEndCap_0,
		&C_OP_RemapParticleCountOnScalarEndCap_2
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_CreationNoise_0,
		&C_INIT_RemapCPtoVector_0,
		&C_INIT_RemapScalar_0,
		&C_INIT_RemapParticleCountToScalar_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/frostivus_spirit_trail_c.vpcf"
		}
	]
}

C_OP_RenderRopes C_OP_RenderRopes_0
{
	float m_flSelfIllumAmount = 1.000000
	string m_hTexture = "materials\\particle\\beam_plasma_05.vtex"
	float m_flTextureVScrollRate = -1.250000
	string m_Notes = ""
	float m_flTextureVWorldSize = 1428.571533
	int m_nMaxTesselation = 3
	int m_nMinTesselation = 3
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.750000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flEndScale = 0.100000
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	float m_flStartTime_max = -10.000000
	float m_flStartTime_min = -10.000000
	string m_Notes = ""
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	bool m_bAdditive = true
	float(3) m_vecOutputMax = ( 16.000000, 16.000000, 16.000000 )
	float(3) m_vecOutputMin = ( -16.000000, -16.000000, -16.000000 )
	int m_nFieldOutput = 0
	float m_fl4NoiseScale = 0.080000
	string m_Notes = ""
}

C_OP_RemapParticleCountOnScalarEndCap C_OP_RemapParticleCountOnScalarEndCap_0
{
	int m_nFieldOutput = 16
	int m_nInputMax = 10
	bool m_bBackwards = true
	string m_Notes = ""
}

C_OP_RemapParticleCountOnScalarEndCap C_OP_RemapParticleCountOnScalarEndCap_2
{
	bool m_bScaleCurrent = true
	int m_nInputMax = 25
	bool m_bBackwards = true
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
}

C_INIT_CreationNoise C_INIT_CreationNoise_0
{
	float m_flOutputMax = 0.400000
	int m_nFieldOutput = 7
	string m_Notes = ""
}

C_INIT_RemapCPtoVector C_INIT_RemapCPtoVector_0
{
	int m_nOpScaleCP = 16
	string m_Notes = ""
	int m_nCPInput = 15
	float(3) m_vInputMax = ( 255.000000, 255.000000, 255.000000 )
	int m_nFieldOutput = 6
	float(3) m_vOutputMax = ( 1.000000, 1.000000, 1.000000 )
}

C_INIT_RemapScalar C_INIT_RemapScalar_0
{
	float m_flOutputMax = 0.000000
	float m_flOutputMin = 1.000000
	int m_nFieldOutput = 16
	float m_flInputMax = 0.350000
	string m_Notes = ""
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_0
{
	bool m_bActiveRange = true
	bool m_bScaleInitialRange = true
	int m_nFieldOutput = 7
	int m_nInputMax = 3
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmissionDuration = 0.350000
	float m_flEmitRate = 32.000000
	string m_Notes = ""
}