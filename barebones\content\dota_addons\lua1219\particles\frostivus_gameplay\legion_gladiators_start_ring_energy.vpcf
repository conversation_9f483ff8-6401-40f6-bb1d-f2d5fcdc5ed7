<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 128
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 207, 181, 168, 255 )
	float m_flNoDrawTimeToGoToSleep = 0.100000
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_FadeInSimple_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_BasicMovement_0,
		&C_OP_RampScalarLinearSimple_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_SequenceLifeTime_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RingWave_0,
		&C_INIT_PositionOffset_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\smoke\\steam\\steam.vtex"
	string m_Notes = ""
	bool m_bAnimateInFPS = true
	float m_flAnimationRate = 10.000000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	bool m_bEaseInAndOut = true
	float m_flBias = 0.860000
	float m_flEndScale = 1.500000
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	float m_flFadeStartTime = 0.350000
	int(4) m_ColorFade = ( 255, 203, 79, 255 )
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.100000
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.350000
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float(3) m_Gravity = ( 0.000000, 0.000000, 200.000000 )
	float m_fDrag = 0.050000
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	int m_nOpEndCapState = 1
	string m_Notes = ""
	int m_nField = 7
	float m_Rate = -5.000000
	float m_flEndTime = 1000000000.000000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMin = 35.000000
	float m_flRadiusMax = 50.000000
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
	int m_nAlphaMin = 200
}

C_INIT_SequenceLifeTime C_INIT_SequenceLifeTime_0
{
	string m_Notes = ""
	float m_flFramerate = 24.000000
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	string m_Notes = ""
	int m_nSequenceMax = 10
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	bool m_bDisableOperator = true
	string m_Notes = ""
	float(3) m_vecOutputMax = ( 26.000000, 26.000000, 34.000000 )
	float(3) m_vecOutputMin = ( -26.000000, -26.000000, -16.000000 )
	float m_flNoiseScaleLoc = 0.050000
	float m_flNoiseScale = 0.100000
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 255, 250, 218, 255 )
	int(4) m_ColorMax = ( 255, 238, 188, 255 )
}

C_INIT_RingWave C_INIT_RingWave_0
{
	float m_flInitialRadius = 625.000000
	string m_Notes = ""
	int m_nControlPointNumber = 7
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 5.000000 )
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 20.000000 )
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmissionDuration = 15.000000
	float m_flEmitRate = 150.000000
	string m_Notes = ""
}