<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 9
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 300.000000
	int(4) m_ConstantColor = ( 255, 198, 0, 255 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderRopes_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_RemapDirectionToCPToVector_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RemapParticleCountToScalar_0,
		&C_INIT_RemapParticleCountToScalar_2,
		&C_INIT_RemapParticleCountToScalar_4,
		&C_INIT_RemapParticleCountToScalar_6,
		&C_INIT_PositionOffset_0,
		&C_INIT_RingWave_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_fs_bladeswipe_flash.vpcf"
		}
	]
}

C_OP_RenderRopes C_OP_RenderRopes_0
{
	float m_flSelfIllumAmount = 1.000000
	int m_nOrientationType = 3
	string m_hTexture = "materials\\particle\\beam_plasma_06.vtex"
	float m_flTextureVScrollRate = -1.000000
	string m_Notes = ""
	float m_flTextureVWorldSize = 666.666687
	int m_nMaxTesselation = 3
	int m_nMinTesselation = 3
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	float m_flOpEndFadeInTime = 0.500000
	float m_flOpStartFadeInTime = 0.500000
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flBias = 0.350000
	float m_flEndScale = 0.000000
	string m_Notes = ""
	float m_flStartScale = 0.500000
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.650000
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 0.200000
	string m_Notes = ""
}

C_OP_RemapDirectionToCPToVector C_OP_RemapDirectionToCPToVector_0
{
	int m_nFieldOutput = 21
	bool m_bNormalize = true
	float(3) m_vecOffsetAxis = ( 0.000000, 0.000000, 1.000000 )
	float m_flOffsetRot = -70.000000
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 0.180000
	float m_fLifetimeMin = 0.180000
	string m_Notes = ""
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_0
{
	int m_nInputMax = 8
	float m_flOutputMax = 300.000000
	float m_flOutputMin = 100.000000
	string m_Notes = ""
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_2
{
	int m_nInputMax = 5
	int m_nFieldOutput = 7
	float m_flOutputMax = 0.250000
	bool m_bActiveRange = true
	string m_Notes = ""
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_4
{
	int m_nInputMin = 6
	int m_nInputMax = 8
	int m_nFieldOutput = 7
	float m_flOutputMin = 0.250000
	float m_flOutputMax = 0.000000
	bool m_bActiveRange = true
	string m_Notes = ""
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_6
{
	float m_flOutputMin = 0.075000
	int m_nInputMax = 8
	int m_nFieldOutput = 1
	float m_flOutputMax = 0.150000
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	float(3) m_OffsetMin = ( 20.000000, 0.000000, 80.000000 )
	float(3) m_OffsetMax = ( 20.000000, 0.000000, 80.000000 )
	bool m_bLocalCoords = true
	string m_Notes = ""
	int m_nControlPointNumber = 3
}

C_INIT_RingWave C_INIT_RingWave_0
{
	float m_flInitialRadius = 150.000000
	bool m_bEvenDistribution = true
	float m_flParticlesPerOrbit = 15.000000
	float m_flYaw = 160.000000
	string m_Notes = ""
	int m_nControlPointNumber = 3
	float m_flRoll = -35.000000
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 60.000000
	float m_flEmissionDuration = 0.180000
	string m_Notes = ""
	float m_flStartTime = 0.350000
}