#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MagicTest项目解密工具
用于解密被AES加密的Lua文件
使用Lua脚本直接调用原始解密函数
"""

import os
import re
import shutil
import subprocess
import tempfile

def create_lua_decrypt_script():
    """创建Lua解密脚本"""
    lua_script = '''
-- 引入AES解密库
package.path = package.path .. ";MagicTest/game/scripts/vscripts/lib/?.lua"
local aeslua = require("aeslua")

-- 十六进制字符串转换函数
function string.fromhex(str)
    return (str:gsub('..', function (cc)
        return string.char(tonumber(cc, 16))
    end))
end

-- 解密函数（从addon_game_mode.lua复制）
local function decrypt(code, key_type)
    -- 使用备用密钥，因为我们无法获取服务器密钥
    local key = "QLk4icW5aHJB2m9GPqLvypcd1gzMbEcfrvczlqPF"
    local text = string.fromhex(code)

    local plain = aeslua.decrypt(key, text, aeslua.AES128, aeslua.CBCMODE)
    return plain
end

-- 从命令行参数获取加密的十六进制字符串
local encrypted_hex = arg[1]
if not encrypted_hex then
    print("ERROR: No encrypted hex string provided")
    os.exit(1)
end

-- 解密并输出结果
local success, result = pcall(decrypt, encrypted_hex, 0)
if success then
    print(result)
else
    print("ERROR: " .. tostring(result))
    os.exit(1)
end
'''
    return lua_script

def decrypt_with_lua(encrypted_hex):
    """使用Lua脚本解密"""
    # 创建临时Lua脚本文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.lua', delete=False, encoding='utf-8') as f:
        f.write(create_lua_decrypt_script())
        lua_script_path = f.name

    try:
        # 运行Lua脚本
        result = subprocess.run(
            ['lua', lua_script_path, encrypted_hex],
            capture_output=True,
            text=True,
            encoding='utf-8'
        )

        if result.returncode == 0:
            return result.stdout
        else:
            print(f"Lua解密错误: {result.stderr}")
            return None
    finally:
        # 清理临时文件
        os.unlink(lua_script_path)

def is_encrypted_file(filepath):
    """检查文件是否被加密"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            return first_line.startswith('return decrypt(')
    except:
        return False

def decrypt_file(input_path, output_path):
    """解密单个文件"""
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 提取加密的十六进制字符串
        match = re.search(r'return decrypt\("([^"]+)"', content)
        if not match:
            print(f"文件格式不正确: {input_path}")
            return False

        encrypted_hex = match.group(1)

        # 使用Lua解密
        decrypted_content = decrypt_with_lua(encrypted_hex)
        if decrypted_content is None:
            print(f"解密失败: {input_path}")
            return False

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 写入解密后的内容
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(decrypted_content)

        print(f"成功解密: {input_path} -> {output_path}")
        return True

    except Exception as e:
        print(f"解密失败: {input_path} - {str(e)}")
        return False

def process_directory(input_dir, output_dir):
    """递归处理目录"""
    if not os.path.exists(input_dir):
        print(f"输入目录不存在: {input_dir}")
        return

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    for root, dirs, files in os.walk(input_dir):
        for file in files:
            if file.endswith('.lua'):
                input_path = os.path.join(root, file)
                # 计算相对路径
                rel_path = os.path.relpath(input_path, input_dir)
                output_path = os.path.join(output_dir, rel_path)

                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                if is_encrypted_file(input_path):
                    decrypt_file(input_path, output_path)
                else:
                    # 复制未加密的文件
                    shutil.copy2(input_path, output_path)
                    print(f"复制未加密文件: {input_path}")

def main():
    """主函数"""
    input_dir = "MagicTest/game/scripts/vscripts"
    output_dir = "MagicTest_Decrypted/game/scripts/vscripts"

    print("开始解密MagicTest项目...")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")

    process_directory(input_dir, output_dir)

    # 同时复制其他重要文件
    other_files = [
        "MagicTest/game/addoninfo.txt",
        "MagicTest/game/scripts/custom.gameevents",
        "MagicTest/game/scripts/custom_net_tables.txt",
    ]

    for file_path in other_files:
        if os.path.exists(file_path):
            rel_path = os.path.relpath(file_path, "MagicTest")
            output_path = os.path.join("MagicTest_Decrypted", rel_path)
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            shutil.copy2(file_path, output_path)
            print(f"复制配置文件: {file_path}")

    print("解密完成！")

if __name__ == "__main__":
    main()
