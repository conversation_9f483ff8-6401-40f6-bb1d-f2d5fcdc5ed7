/*Styles shared across all UI */

@define baseText: #A5ADA2ff;
@define baseBorder: #444444ff;
@define offWhite: #ccccccff;
@define selectedTextBackgroundColor: #66666633;
@define goldColor: #ffcc33;

@define dotalevelgradient: gradient( linear, 0% 0%, 0% 100%, from( #BBCAEA ), color-stop( 0.5, #ffffff), color-stop( 0.5, #D9DFEC), to( #BBCAEA ) );
@define dotaplaytimegradient: gradient( linear, 0% 0%, 0% 100%, from( #3db451 ), color-stop( 0.5, #beffc7), color-stop( 0.5, #91d69a), to( #3db451 ) );

@define HeroSlot0Color: #3375FFFF;
@define HeroSlot1Color: #66FFBFFF;
@define HeroSlot2Color: #BF00BFFF;
@define HeroSlot3Color: #F3F00BFF;
@define HeroSlot4Color: #FF6B00FF;
@define HeroSlot5Color: #FE86C2FF;
@define HeroSlot6Color: #A1B447FF;
@define HeroSlot7Color: #65D9F7FF;
@define HeroSlot8Color: #008321FF;
@define HeroSlot9Color: #A46900FF;

@define HeroSlot0ColorHalf: #3375FF33;
@define HeroSlot1ColorHalf: #66FFBF44;
@define HeroSlot2ColorHalf: #BF00BF88;
@define HeroSlot3ColorHalf: #F3F00B33;
@define HeroSlot4ColorHalf: #FF6B0033;
@define HeroSlot5ColorHalf: #FE86C244;
@define HeroSlot6ColorHalf: #A1B44766;
@define HeroSlot7ColorHalf: #65D9F733;
@define HeroSlot8ColorHalf: #00832133;
@define HeroSlot9ColorHalf: #A4690033;

@define RuneDDColor: #82CAFF;
@define RuneHasteColor: #F62817;
@define RuneIllusionColor: #FFD700;
@define RuneInvisColor: #8B008B;
@define RuneRegenColor: #7FFF00;
@define RuneArcaneColor: #FD3AFB;
@define RuneBountyColor: #FF7800;

@define PreConnectedTransitionDuration: 1s;

@define winnerGreen: #88ff88;
@define loserRed: #ff4433;

@define dotaPlusGold: #B78946;
@define dotaPlusLightGold: #DCD086;
@define dotaPlusBrown: #433620;
@define dotaPlusGoldGradient: gradient( linear, 0% 0%, 100% 0%, from( #F8E8B9 ), color-stop( 0.5, #E4C269), to( #C79123 ) );
@define dotaPlusGoldGradientHover: gradient( linear, 0% 0%, 0% 100%, from( #d8a253 ), to( #ecdf90 ) );
@define dotaPlusGoldGradientActive: gradient( linear, 0% 0%, 0% 100%, from( #ca974d ), to( #dfd388 ) );
@define dotaPlusBgGradient: gradient( linear, 0% 0%, 0% 100%, from( dotaPlusBrown ), color-stop( 0.5, #141b21EF), to( #141b21EF) );

@define dotaShardCurrencyBgGradient: gradient( linear, 0% 0%, 0% 100%, from( #141b21 ), to( #141b21EF) );

@define dotaPlusCurrencyIcon: url("s2r://panorama/images/dota_plus/currency_icon_png.vtex");
@define dotaPlusCurrencySmallIcon: url("s2r://panorama/images/dota_plus/currency_icon_small_png.vtex");
@define dotaPlusLogo: url("s2r://panorama/images/dota_plus/dotaplus_logo_png.vtex");
@define dotaPlusLogoSmall: url("s2r://panorama/images/dota_plus/dotaplus_logo_small_png.vtex");
@define dotaPlusChallengeIcon: url("s2r://panorama/images/dota_plus/challanges_icon_png.vtex");
@define dotaPlusChatWheelIcon: url("s2r://panorama/images/dota_plus/icon_chatwheel_plus_reward_png.vtex");
@define dotaPlusRelicIcon: url("s2r://panorama/images/dota_plus/relic_icon_png.vtex");

@define proVerifiedColor: #ffcc33;

/* Guild Primary Colors */
@define guildColor0: #859B9A;
@define guildColor1: #DB251A;
@define guildColor2: #FF7A00;
@define guildColor3: #ECD146;
@define guildColor4: #99DB1A;
@define guildColor5: #0BB24E;
@define guildColor6: #4CC3BA;
@define guildColor7: #3375FF;
@define guildColor8: #986DFF;
@define guildColor9: #DB77A9;

@define guildColor10: #6A7A7A;
@define guildColor11: #C98893;
@define guildColor12: #907C9C;
@define guildColor13: #6669C1;
@define guildColor14: #4B90AB;
@define guildColor15: #1E9E79;
@define guildColor16: #79A053;
@define guildColor17: #A5966B;
@define guildColor18: #B97935;
@define guildColor19: #B8513B;

/* Guild Secondary Colors*/
@define guildSecondaryColor0: #30343D;
@define guildSecondaryColor1: #540A0C;
@define guildSecondaryColor2: #66260F;
@define guildSecondaryColor3: #7A5900;
@define guildSecondaryColor4: #3E580C;
@define guildSecondaryColor5: #02461D;
@define guildSecondaryColor6: #004244;
@define guildSecondaryColor7: #162D5E;
@define guildSecondaryColor8: #372561;
@define guildSecondaryColor9: #6C1A43;

@define guildSecondaryColor10: #506968;
@define guildSecondaryColor11: #A5416D;
@define guildSecondaryColor12: #5B44AE;
@define guildSecondaryColor13: #324695;
@define guildSecondaryColor14: #378A82;
@define guildSecondaryColor15: #2A793C;
@define guildSecondaryColor16: #718603;
@define guildSecondaryColor17: #B28931;
@define guildSecondaryColor18: #AA4825;
@define guildSecondaryColor19: #8A1614;

/* Guild Patterns */
@define guildPattern0: url("s2r://panorama/images/guild_patterns/guild_pattern_0_psd.vtex");
@define guildPattern1: url("s2r://panorama/images/guild_patterns/guild_pattern_1_psd.vtex");
@define guildPattern2: url("s2r://panorama/images/guild_patterns/guild_pattern_2_psd.vtex");
@define guildPattern3: url("s2r://panorama/images/guild_patterns/guild_pattern_3_psd.vtex");
@define guildPattern4: url("s2r://panorama/images/guild_patterns/guild_pattern_4_psd.vtex");
@define guildPattern5: url("s2r://panorama/images/guild_patterns/guild_pattern_5_psd.vtex");
@define guildPattern6: url("s2r://panorama/images/guild_patterns/guild_pattern_6_psd.vtex");
@define guildPattern7: url("s2r://panorama/images/guild_patterns/guild_pattern_7_psd.vtex");
@define guildPattern8: url("s2r://panorama/images/guild_patterns/guild_pattern_8_psd.vtex");
@define guildPattern9: url("s2r://panorama/images/guild_patterns/guild_pattern_9_psd.vtex");
@define guildPattern10: url("s2r://panorama/images/guild_patterns/guild_pattern_10_psd.vtex");
@define guildPattern11: url("s2r://panorama/images/guild_patterns/guild_pattern_11_psd.vtex");

/* Use the GuildImageColorAdjusted class if you have an image that you want to rotate to the guild color original image must be blue for this to work */
@define guildColorHueRotation0: 290deg;
@define guildColorSaturation0: 0.25;

@define guildColorHueRotation1: 132deg;
@define guildColorSaturation1: 1;

@define guildColorHueRotation2: 150deg;
@define guildColorSaturation2: 1;

@define guildColorHueRotation3: 170deg;
@define guildColorSaturation3: 1;

@define guildColorHueRotation4: 220deg;
@define guildColorSaturation4: 1;

@define guildColorHueRotation5: 265deg;
@define guildColorSaturation5: 1;

@define guildColorHueRotation6: 310deg;
@define guildColorSaturation6: 1;

@define guildColorHueRotation7: 0deg;
@define guildColorSaturation7: 1;

@define guildColorHueRotation8: 25deg;
@define guildColorSaturation8: 1;

@define guildColorHueRotation9: 100deg;
@define guildColorSaturation9: .9;

@define guildColorHueRotation10: 300deg;
@define guildColorSaturation10: .2;

@define guildColorHueRotation11: 110deg;
@define guildColorSaturation11: .8;

@define guildColorHueRotation12: 60deg;
@define guildColorSaturation12: .75;

@define guildColorHueRotation13: 10deg;
@define guildColorSaturation13: .8;

@define guildColorHueRotation14: 350deg;
@define guildColorSaturation14: .8;

@define guildColorHueRotation15: 295deg;
@define guildColorSaturation15: .9;

@define guildColorHueRotation16: 230deg;
@define guildColorSaturation16: .8;

@define guildColorHueRotation17: 170deg;
@define guildColorSaturation17: .5;

@define guildColorHueRotation18: 160deg;
@define guildColorSaturation18: .9;

@define guildColorHueRotation19: 140deg;
@define guildColorSaturation19: .9;

/* Guild Tier Colors */
@define guildTierColor0: gradient( linear, 0% 100%, 0% 0%, from( #AA6552 ), color-stop( .3, #BF7E65), to( #FFAC8F ) );
@define guildTierColor1: gradient( linear, 0% 100%, 0% 0%, from( #82736A ), color-stop( .3, #978A86), to( #CCC8C7 ) );
@define guildTierColor2: gradient( linear, 0% 100%, 0% 0%, from( #A17649 ), color-stop( .5, #FAF6C6), to( #C9B778 ) );
@define guildTierColor3: gradient( linear, 0% 100%, 0% 0%, from( #76B2C3 ), color-stop( .3, #9DDADB), to( #DBFAFF ) );

@define guildTierImage0Small: url("s2r://panorama/images/guilds/tiers/tier_0_small_png.vtex");
@define guildTierImage1Small: url("s2r://panorama/images/guilds/tiers/tier_1_small_png.vtex");
@define guildTierImage2Small: url("s2r://panorama/images/guilds/tiers/tier_2_small_png.vtex");
@define guildTierImage3Small: url("s2r://panorama/images/guilds/tiers/tier_3_small_png.vtex");

@define guildTierFrame0: url("s2r://panorama/images/guilds/tiers/guild_tier0_icon_frame_psd.vtex");
@define guildTierFrame1: url("s2r://panorama/images/guilds/tiers/guild_tier1_icon_frame_psd.vtex");
@define guildTierFrame2: url("s2r://panorama/images/guilds/tiers/guild_tier2_icon_frame_psd.vtex");
@define guildTierFrame3: url("s2r://panorama/images/guilds/tiers/guild_tier3_icon_frame_psd.vtex");


.GuildPrimaryColor0 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation0; saturation: guildColorSaturation0; }
.GuildPrimaryColor1 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation1; saturation: guildColorSaturation1; }
.GuildPrimaryColor2 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation2; saturation: guildColorSaturation2; }
.GuildPrimaryColor3 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation3; saturation: guildColorSaturation3; }
.GuildPrimaryColor4 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation4; saturation: guildColorSaturation4; }
.GuildPrimaryColor5 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation5; saturation: guildColorSaturation5; }
.GuildPrimaryColor6 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation6; saturation: guildColorSaturation6; }
.GuildPrimaryColor7 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation7; saturation: guildColorSaturation7; }
.GuildPrimaryColor8 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation8; saturation: guildColorSaturation8; }
.GuildPrimaryColor9 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation9; saturation: guildColorSaturation9; }
.GuildPrimaryColor10 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation10; saturation: guildColorSaturation10; }
.GuildPrimaryColor11 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation11; saturation: guildColorSaturation11; }
.GuildPrimaryColor12 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation12; saturation: guildColorSaturation12; }
.GuildPrimaryColor13 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation13; saturation: guildColorSaturation13; }
.GuildPrimaryColor14 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation14; saturation: guildColorSaturation14; }
.GuildPrimaryColor15 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation15; saturation: guildColorSaturation15; }
.GuildPrimaryColor16 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation16; saturation: guildColorSaturation16; }
.GuildPrimaryColor17 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation17; saturation: guildColorSaturation17; }
.GuildPrimaryColor18 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation18; saturation: guildColorSaturation18; }
.GuildPrimaryColor19 .GuildImageColorAdjusted { hue-rotation: guildColorHueRotation19; saturation: guildColorSaturation19; }

.GuildTier0 .GuildTierColorAdjusted { color: guildTierColor0; }
.GuildTier1 .GuildTierColorAdjusted { color: guildTierColor1; }
.GuildTier2 .GuildTierColorAdjusted { color: guildTierColor2; }
.GuildTier3 .GuildTierColorAdjusted { color: guildTierColor3; }

.GuildTier0.GuildTierColorAdjusted { color: guildTierColor0; }
.GuildTier1.GuildTierColorAdjusted { color: guildTierColor1; }
.GuildTier2.GuildTierColorAdjusted { color: guildTierColor2; }
.GuildTier3.GuildTierColorAdjusted { color: guildTierColor3; }

.GuildTier0 #GuildLogoFrame { background-image: guildTierFrame0; }
.GuildTier1 #GuildLogoFrame { background-image: guildTierFrame1; }
.GuildTier2 #GuildLogoFrame { background-image: guildTierFrame2; }
.GuildTier3 #GuildLogoFrame { background-image: guildTierFrame3; }


/* ContextMenu - used in code -- dont rename */
@define ContextMenuFadoutTime: 0.2s;

/* Z layers */
@define contextmenu_zindex:	   	10000100;

@define mousepanningcursorsize: 64px;

/* top bar small text labels (e.g. X PLAYERS IN GAME NOW) */
@define colorHomeStatLineText: #999999;
@define colorHomeStatLineTextBright: #dedede;

/* ------------------------------------------------------------------------- */
/*  Fonts                                                                    */
/*                                                                           */
/*  When specifying fonts, you should almost always use either one of the    */
/*	defines below, or use the classes. If you manually specify a single      */
/*	font-family then it probably won't work correctly for other languages.   */
/* ------------------------------------------------------------------------- */

/* Default sans-serif font, with fallbacks for other languages */
@define defaultFont: Radiance,FZLanTingHei-R-GBK,TH Sarabun New,YDYGO 540,Gulim,MingLiU;

/* Default sans-serif font, but with monospace numbers. Useful for things like countdowns or numbers that need to line up in columns. */
@define monospaceNumbersFont: RadianceM,Radiance,FZLanTingHei-R-GBK,TH Sarabun New,YDYGO 540,Gulim,MingLiU;

/* Default serif font, with fallbacks for other languages */
@define titleFont: Reaver,Goudy Trajan Medium,FZKai-Z03,TH Sarabun New,YDYGO 540;

/* Full monospace font. Used mostly for debugging things. */
@define monospaceFont: Courier New,Courier;

/* TEMP Diretide Font */
@define diretideFont: Creepster, Valve Radus, Radiance, FZKai-Z03,TH Sarabun New,YDYGO 540;

Label, TextEntry
{
	font-family: defaultFont;
}
Label.MonoNumbersFont, TextEntry.MonoNumbersFont
{
	font-family: monospaceNumbersFont;
}
Label.TitleFont, TextEntry.TitleFont
{
	font-family: titleFont;
}
Label.MonoFont, TextEntry.MonoFont
{
	font-family: monospaceFont;
}


.LabelLink
{
	text-decoration: underline;
}

/* ------------------------------------------------------------------------- */
/*  Simple Text Styles                                                       */
/* ------------------------------------------------------------------------- */
.Headline1
{
	color: white;
	font-size: 54px;
	font-weight: Bold;
	letter-spacing: 1px;
	text-transform: uppercase;
	margin-bottom: -12px;
	margin-top: -10px;
}

.Headline2
{
	font-size: 32px;
	color: offWhite;
	letter-spacing: 1px;
	//font-weight: medium;
}

.Capital
{
	text-transform: uppercase;
}


/* ------------------------------------------------------------------------- */
/*  DropDown                                                                 */
/* ------------------------------------------------------------------------- */
DropDown, DOTASettingsEnumDropDown
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #292e2e ), to( #191e1e ) );

	background-image: url("s2r://panorama/images/control_icons/arrow_dropdown_png.vtex");

	background-repeat: no-repeat;
	background-position: right 38px 35%;
	background-size: 32px 32px;
	box-shadow: #00000055 -2px -2px 1px 4px;

	border: 2px solid #5e686966;

	font-size: 24px;
	width: 320px;
	height: 41px;
	padding: 4px 8px;
	z-index: 1;

	transition-property: background-color, border;
	transition-duration: 0.15s;
	transition-timing-function: linear;
}

DropDown:enabled:hover, DOTASettingsEnumDropDown:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #393e3e ), to( #292e2e ) );
}

DropDown:focus .TickBox, DOTASettingsEnumDropDown:focus .TickBox
{
	text-overflow: clip;
	white-space: nowrap;
}

DropDown:focus, DOTASettingsEnumDropDown:focus
{

}

DropDown:disabled
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #292e2e ), to( #191e1e ) );
	border: 2px solid #3b414266;
	background-image: url("s2r://panorama/images/control_icons/arrow_dropdown_disabled_png.vtex");
}

DropDown.DropDownMenuVisible:hover, DOTASettingsEnumDropDown.DropDownMenuVisible:hover
{

}

DropDown.DropDownMenuVisible:focus, DOTASettingsEnumDropDown.DropDownMenuVisible:focus
{
	animation-name: none;
}

DropDown Label, DOTASettingsEnumDropDown Label
{
	width: 100%;
	margin: 3px 29px 0px 6px;
	font-size: 18px;

	transition-property: color;
	transition-duration: 0.15s;
	transition-timing-function: linear;
}


DropDown:focus Label, DOTASettingsEnumDropDown:focus Label
{
	color: white;
}

DropDown:disabled Label
{
	color: #444444;
}

DropDown.DropDownMenuVisible:focus Label, DOTASettingsEnumDropDown.DropDownMenuVisible:focus Label
{
	color: white;
}

DropDownMenu
{
	width: 320px;
	flow-children: down;
	background-color: #3d4448;
	color: #e1e1e1;
	font-size: 28px;
	overflow: squish scroll;
	z-index: 0;
	transform: translateY(-40px);
	opacity: 0.0;

	transition-property: opacity, transform;
	transition-duration: 0.15s;
	transition-timing-function: ease-in;

	box-shadow: fill #00000066 -3px -3px 6px 6px;
}

DropDownMenu Label
{
	width: 100%;
	padding: 6px 0px 2px 0px;
	margin-top: 0px 0px;
	margin-right: 0px;
	margin-left: 0px;
	padding-left: 16px;

	color: #ffffff99;
	background-color: #3d4448;

	font-size: 18px;
	font-weight: normal;
	z-index: 0;
	border-top: 1px solid #00000066;
	border-bottom: 1px solid #00000066;

	transition-property: background-color;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
}

DropDownMenu Label:hover
{
	background-color: #585e62;
}

DropDownMenu.DropDownMenuVisible
{
	opacity: 1.0;

	max-height: 444px;
	transition-property: box-shadow;
	transform: translateY(0px);
}

DropDown.ThinDropDown
{
	height: 32px;

	background-size: 24px 24px;
	background-position: right 32px 35%;
}

.ThinDropDown Label
{
	font-size: 18px;
}

/* Scroll Bars */
VerticalScrollBar, HTMLVerticalScrollBar
{
	width: 8px;
	height: 100%;
	horizontal-align: right;
	layout-position: fixed;

	transition-property: transform;
}

.GuildPage VerticalScrollBar, HTMLVerticalScrollBar
{
	width: 6px;
	background-color: #ffffff02;
	margin-right: 14px;
}

VerticalScrollBar .ScrollThumb, HTMLVerticalScrollBar .ScrollThumb
{
	width: 8px;
	min-height: 32px;  // need to get the scrollthumb to hit the bottom of the scrollbar
	background-color: #566767;
	border-top: 1px solid #aaaaaa22;
	border-right: 1px solid #000000aa;
	transition-property: position;
	/*background-color: gradient( linear, 0% 0%, 0% 100%, from( #3e4446 ), to( #33383a ) );*/
	transition-property: background-color, transform, position;
	transition-duration: 0.1s;
	transition-timing-function: linear;
}

.GuildPage VerticalScrollBar .ScrollThumb, HTMLVerticalScrollBar .ScrollThumb
{
	background-color: #c4c4c433;
}

VerticalScrollBar:hover .ScrollThumb, HTMLVerticalScrollBar:hover .ScrollThumb
{
	background-color: #566767;
}


VerticalScrollBar:active .ScrollThumb, HTMLVerticalScrollBar:active .ScrollThumb
{
	background-color: #566767;
}

.GuildPage VerticalScrollBar:hover .ScrollThumb, HTMLVerticalScrollBar:hover .ScrollThumb
{
	background-color: #c4c4c499;
}


.GuildPage VerticalScrollBar:active .ScrollThumb, HTMLVerticalScrollBar:active .ScrollThumb
{
	background-color: #c4c4c4;
}

HorizontalScrollBar, HTMLHorizontalScrollBar
{
	height: 12px;
	width: 100%;
	vertical-align: bottom;
	box-shadow: fill #00000055 0px -3px 3px 4px;
	layout-position: fixed;

	transition-property: transform;
}

HorizontalScrollBar .ScrollThumb, HTMLHorizontalScrollBar .ScrollThumb
{
	height: 12px;
	min-width: 32px;
	border-top: 1px solid #aaaaaa22;
	border-bottom: 1px solid #000000aa;
	background-color: #566767;
	transition-property: background-color, transform, position;
	transition-duration: 0.1s;
	transition-timing-function: linear;
}

HorizontalScrollBar:hover .ScrollThumb, HTMLHorizontalScrollBar:hover .ScrollThumb
{
	background-color: #566767;
}
HorizontalScrollBar:active .ScrollThumb, HTMLHorizontalScrollBar:active .ScrollThumb
{
	background-color: #566767;
}

VerticalScrollBar,
VerticalScrollBar .ScrollThumb,
HorizontalScrollBar,
HorizontalScrollBar .ScrollThumb
HTMLHorizontalScrollBar,
HTMLHorizontalScrollBar .ScrollThumb,
HTMLVerticalScrollBar,
HTMLVerticalScrollBar .ScrollThumb
{
	/* The scrollbar transform transition determins how quickly contents scroll when using the mousewheel.
	   So make sure it's non-zero if you want smooth scrolling. */
	transition-duration: 0.20s;
	transition-timing-function: ease-in-out;
}

VerticalScrollBar.MouseDown,
VerticalScrollBar.MouseDown .ScrollThumb,
HorizontalScrollBar.MouseDown,
HorizontalScrollBar.MouseDown .ScrollThumb
HTMLHorizontalScrollBar.MouseDown,
HTMLHorizontalScrollBar.MouseDown .ScrollThumb,
HTMLVerticalScrollBar.MouseDown,
HTMLVerticalScrollBar.MouseDown .ScrollThumb
{
	/* Disable the transition when the mouse is dragging the scroll thumb. Note that this must match
	   when IUIScrollBar::IsMouseDown returns true, or else the content will be out of sync */
	transition-duration: 0.0s;
}

//
// HTTP styles used by labels
//

a
{
	//color: blue;
}

a:hover
{
	text-decoration: underline;
}

b
{
	font-weight: bold;
}

i
{
	font-style: italics;
}

strong
{
	font-weight: bold;
}

em
{
	font-style: italics;
}

h1
{
	font-weight: bold;
}

h2
{
	font-weight: bold;
}

pre
{
	font-family: monospaceFont;
}

//
// ContextMenu
//
.ContextMenu
{
	width: 100%;
	height: 100%;
	transition-property: opacity, transform;
	transition-duration: ContextMenuFadoutTime;
	transition-timing-function: ease-in-out;
	opacity: 1.0;

	z-index: contextmenu_zindex;


	visibility: visible;
}

.ContextMenu.Destructing
{
	transform: translatex( 0px );
	opacity: 0.0;
}


//
// Simple Convenience styles
//

.LeftRightFlow
{
	flow-children: right;
}

.TopBottomFlow
{
	flow-children: down;
}

.NoFlow
{
	flow-children: none;
}

.Hide
{
	visibility: collapse;
}

.Show
{
	visibility: visible;
}

.HorizontalCenter
{
	horizontal-align: center;
}

.VerticalCenter
{
	vertical-align: center;
}


.FullWidth
{
	width: 100%;
}

.FullHeight
{
	height: 100%;
}

.FullWidthHeight
{
	width: 100%;
	height: 100%;
}

.Center
{
	horizontal-align: center;
}


//
// Label
//
Label
{
	color: baseText;
	font-size: 18px;
}

//
//  TOGGLE BUTTON / CHECK BOX
//

ToggleButton, DOTASettingsCheckbox
{
	flow-children: right;
	padding: 1px 4px;
	color: white;

	transition-property: background-color, box-shadow, color;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
}

ToggleButton .TickBox, DOTASettingsCheckbox .TickBox
{
	width: 18px;
	height: 18px;
	vertical-align: center;
	background-color: black;
	border: 2px solid #5e686966;
	box-shadow: #00000055 -2px -2px 1px 4px;

	transition-property: background-color, box-shadow, color, border, background-image, wash-color, pre-transform-scale2d;
	transition-duration: 0.20s;
	transition-timing-function: ease-in-out;

}

ToggleButton:selected .TickBox, DOTASettingsCheckbox:selected .TickBox
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #e7f6f5 ), to( #a0d6d7 ) );
	border: 3px solid #000000;
	box-shadow: #5b62bf77 -4px -4px 8px 8px;

}

ToggleButton:hover .TickBox, DOTASettingsCheckbox:hover .TickBox
{
	//background-color: gradient( linear, 0% 0%, 0% 100%, from( #e7f6f5 ), to( #a0d6d7 ) );
	border: 2px solid #697879;
}

ToggleButton Label, DOTASettingsCheckbox Label
{
	margin-top: 4px;
	font-size: 16px;
	margin-left: 8px;
	color: baseText;
	letter-spacing: 1px;

	transition-property: color;
	transition-duration: 0.2s;
	transition-timing-function: linear;
}

ToggleButton:enabled:hover Label, DOTASettingsCheckbox:enabled:hover Label
{
	color: #d2d6d1;
}

ToggleButton:selected Label, DOTASettingsCheckbox:selected Label
{
	color: white;
}

ToggleButton:disabled Label, DOTASettingsCheckbox:disabled Label
{
	color: #333333;
}

ToggleButton:disabled .TickBox, DOTASettingsCheckbox:disabled .TickBox
{
	border-color: #333333;
}

ToggleButton:disabled:selected .TickBox, DOTASettingsCheckbox:disabled:selected .TickBox
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #222222 ), to( #333333 ) );
	border: 3px solid #000000;
	box-shadow: #33333377 -4px -4px 8px 8px;
}


ToggleButton.ExpandCollapseToggleButton
{
	margin: 0px;
	padding: 0px;
}

.ExpandCollapseToggleButton .TickBox
{
	margin: 0px;
	padding: 0px;
	width: 19px;
	height: 19px;
	vertical-align: middle;
	background-image: url("s2r://panorama/images/control_icons/expand_collapse_png.vtex");
	background-size: 100% 100%;
	background-color: none;
	box-shadow: none;
	border: 0px solid transparent;
	wash-color: #888888;
	transform: rotateZ( -90deg );

	transition-property: transform;
	transition-duration: 0.2s;
}


.ExpandCollapseToggleButton:hover .TickBox
{
	border: 0px solid transparent;
}

.ExpandCollapseToggleButton:selected .TickBox
{
	width: 19px;
	height: 19px;
	vertical-align: middle;
	background-image: url("s2r://panorama/images/control_icons/expand_collapse_png.vtex");
	background-size: 100% 100%;
	background-color: none;
	box-shadow: none;
	border: 0px solid transparent;
	wash-color: #888888;

	transform: rotateZ( 0deg );

	transition-property: transform;
	transition-duration: 0.2s;
}

//
// Radio Button
//

RadioButton
{
	background-color: none;
	flow-children: right;
//	padding: 4px 4px;
	color: white;

	transition-property: background-color, box-shadow, color, brightness;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
}

RadioButton > .RadioBox
{
	min-width: 20px;
	min-height: 20px;

	border-radius: 50%;
	background-color: gradient( radial, 50% 50%, 0% 0%, 10% 10%, from( #000000 ), to( #000000 ) );

	border: 2px solid #5e686966;
	box-shadow: #00000055 -2px -2px 1px 4px;

	transition-property: background-color, box-shadow, color, border;
	transition-duration: 0.20s;
	transition-timing-function: ease-in-out;
}

RadioButton:selected > .RadioBox
{
	background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, from( #e7f6f5 ), to( #333399 ) );
	border: 3px solid #000000;

	box-shadow: #5b62bf77 -3px -3px 6px 6px;

	transition-property: background-color;
	transition-duration: 0.5s;
	transition-timing-function: ease-in-out;
}

RadioButton:enabled:hover > .RadioBox
{
	border: 2px solid #697879;
}

RadioButton:enabled:selected:hover > .RadioBox
{
	border: 3px solid #000000;
}

RadioButton > Label
{
	color: baseText;
	margin-left: 4px;
	font-size: 16px;

	transition-property: color;
	transition-duration: 0.2s;
	transition-timing-function: linear;
}

DOTASettingsEnum RadioButton > Label
{
	font-size: 18px;
	color: #7d8688;
}


RadioButton:selected > Label
{
	color: white;

	transition-property: color, text-shadow;
	transition-duration: 0.2s;
	transition-timing-function: linear;
}

RadioButton:enabled:hover > Label
{
	color: white;

	transition-property: color;
	transition-duration: 0.2s;
	transition-timing-function: linear;
}

RadioButton:disabled > .RadioBox
{
	color: transparent;
	box-shadow: #33333377 -4px -4px 8px 8px;
	background-color: #00000000;
}

RadioButton:disabled:selected > .RadioBox
{
	background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, from( #e7f6f533 ), to( #33339933 ) );
}

RadioButton:disabled > Label
{
	color: #333333;
}



//
// Grey button with a small bevel
//
.ButtonBevel
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #373d45 ), to( #4d5860 ) );
	border-style: solid;
	border-width: 1px;

	padding: 4px 10px;

	border-top-color: #555555;
	border-left-color: #494949;
	border-bottom-color: #333333;
	border-right-color: #404040;

	transition-property: background-color, opacity;
	transition-duration: 0.05s;
	transition-timing-function: linear;
	box-shadow: #00000055 -2px -2px 4px 4px;

	min-width: 192px;
	min-height: 36px;
}

.ButtonBevel.Green
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #2a481f ), to( #226326 ) );

	border-top-color: #355639;
	border-left-color: #2c4d30;
	border-bottom-color: #1e371e;
	border-right-color: #214123;
}

.ButtonBevel.DarkGreen
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #1a2c13 ), to( #18441b ) );

	border-top-color: #223524;
	border-left-color: #2c4d30;
	border-bottom-color: #091009;
	border-right-color: #132615;
}

.ButtonBevel.Bronze
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #b9894c ), to( #dca157) );

	border-top-color: #e0a75e;
	border-left-color: #dca35b;
	border-bottom-color: #8d683a;
	border-right-color: #8f6b3c;
}

.ButtonBevel.Plus
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #877f4f ), to( #a79c5b ) );

	border-top-color: #b4a442;
	border-left-color: #aea463;
	border-bottom-color: #635e3b;
	border-right-color: #756f45;
}


.ButtonBevel:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #4c5561 ), to( #6c7d88 ) );

	border-top-color: #aaaaaa77;
	border-left-color: #aaaaaa33;
	border-bottom-color: #333333;
	border-right-color: #404040;
}

.ButtonBevel.Green:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #226528 ), to( #288a36 ) );

	border-top-color: #030503;
	border-left-color: #000000;
	border-bottom-color: #1e371d;
	border-right-color: #244426;
}

.ButtonBevel.DarkGreen:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #17421b ), to( #185321 ) );

	border-top-color: #000000;
	border-left-color: #000000;
	border-bottom-color: #172a16;
	border-right-color: #152716;
}

.ButtonBevel.Bronze:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #dda45a ), to( #fcb965 ) );

	border-top-color: #f2b362;
	border-left-color: #edb062;
	border-bottom-color: #966f3e;
	border-right-color: #966f3e;
}

.ButtonBevel.Plus:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #a79d63 ), to( #c2b56b ) );

	border-top-color: #cab84b;
	border-left-color: #c6bb72;
	border-bottom-color: #7c764b;
	border-right-color: #8e8755;
}

.ButtonBevel:disabled
{
	wash-color: #AAAAAA;
	saturation: 0;
}

.ButtonBevel.Bronze:disabled
{
	saturation: 0.75;
}

.ButtonBevel.Plus:disabled
{
	saturation: 0.75;
}

.ButtonBevel:disabled Label
{
	color: #333333;
}

.ButtonBevel:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #393939), to( #555555 ) );

	border-top-color: #222222;
	border-left-color: #303030;
	border-bottom-color: #666666;
	border-right-color: #444444;
	sound: "ui_generic_button_click";
}

.ButtonBevel.Green:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #1f3d25), to( #2b562c ) );

	border-top-color: #132416;
	border-left-color: #192f1b;
	border-bottom-color: #386b3b;
	border-right-color: #264a29;
}

.ButtonBevel.DarkGreen:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #132416), to( #183019 ) );

	border-top-color: #09110a;
	border-left-color: #0f1b10;
	border-bottom-color: #1f3820;
	border-right-color: #172c19;
}

.ButtonBevel.Bronze:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #4d391f), to( #9b713c ) );

	border-top-color: #684e2d;
	border-left-color: #6d522f;
	border-bottom-color: #977242;
	border-right-color: #906c3f;
}

.ButtonBevel.Plus:active
{
	color: #DCD086;

	background-color: gradient( linear, 0% 0%, 0% 100%, from( #877f4f ), to( #a79c5b ) );

	border-top-color: #635e3b;
	border-left-color: #756f45;
	border-bottom-color: #b4a442;
	border-right-color: #aea463;
}

.ButtonBevel Label
{
	margin-top: 2px;
	text-transform: uppercase;
	letter-spacing: 2px;
	color: #FFFFFF;
	horizontal-align: center;
	text-align: center;
	vertical-align: middle;
	text-shadow: 2px 2px 0px 1.0 #000000;

	transition-property: color;
	transition-duration: 0.35s;
	transition-timing-function: ease-in-out;
}

.ButtonBevel:active Label
{
		transform: translateY(1px);
}

.ExternalIcon
{
	background-image: url("s2r://panorama/images/control_icons/arrow_popout_png.vtex");
	background-repeat: no-repeat;
	background-size: 100% 100%;
	wash-color: white;
	height: 16px;
	width: 16px;
	margin-left: 6px;
	vertical-align: middle;
}

//
// Grey button with a small bevel
//
.ButtonDark
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #191e1e ), to( #292e2e ) );
	border: 2px solid #5e686966;

	transition-property: border, background-color;
	transition-duration: 0.1s;
	transition-timing-function: linear;

	min-width: 192px;
	min-height: 36px;
}

.ButtonDark:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #191e1e ), to( #292e2e ) );
	border: 2px solid #5e6869ff;
}

.ButtonDark:hover Label
{
	color: white;
}

.ButtonDark:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #191e1e ), to( #292e2e ) );

	border: 2px solid #5e686922;

	sound: "ui_generic_button_click";
}

.ButtonDark Label
{
	margin-top: 2px;
	text-transform: uppercase;
	color: #7f8b8d;
	horizontal-align: center;
	text-align: center;
	vertical-align: middle;
	text-shadow: 0px 0px 6px 1.0 #000000;
	padding-left: 8px;
	padding-right: 8px;


	transition-property: color;
	transition-duration: 0.1s;
	transition-timing-function: linear;
}

.ButtonDark:active Label
{
	transform: translateY(1px);
}

.ButtonDark:disabled
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #191e1e ), to( #292e2e ) );
	border: 2px solid #111111;
}

.ButtonDark:disabled Label
{
	color: #5d676933;
}


/*Dark Button with Gold Border / Hover */


.ButtonGold
{
    padding: 6px 15px;

    border: 2px solid rgba(201,155,88,0.8);

    transition-property: background-color, opacity, border;
    transition-duration: 0.1s;
    transition-timing-function: linear;
    box-shadow: #00000055 -2px -2px 4px 4px;
    opacity: 0.9;

}

.ButtonGold:enabled:hover
{
    background-color: gradient( linear, 0% 0%, 0% 100%, from( #c99b58 ), to( #f0bd74 ));
    box-shadow: fill #11181e20 0px 4px 14px 0px;
    opacity: 1;
}

.ButtonGold:active
{
    background-color: rgba(201,155,88,1);
    opacity: 1;

    border-top-color: #8b6938;
    border-left-color: #8b6938;
    border-bottom-color: rgba(201,155,88,1);
    border-right-color: rgba(201,155,88,1);
    sound: "ui_generic_button_click";
}

.ButtonGold Label
{
    margin-top: 2px;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: #ffffff;
    horizontal-align: center;
    text-align: center;
    vertical-align: middle;
    text-shadow: 1px 1px 5px rgba(0,0,0,0.9);

    transition-property: color;
    transition-duration: 0.35s;
    transition-timing-function: ease-in-out;
}

.ButtonGold:active Label
{
    opacity: 0.8;
}


//
// SimpleBevel - adds a very simple bevel effect to any button
//
.SimpleBevel
{
	border-top: 1px solid #99999905;
	border-left: 1px solid #99999905;
	border-right: 1px solid #00000060;
	border-bottom: 1px solid #00000060;
}

//
// Default Button for Popup panels
//

.PopupButton
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5a6c75 ), to( #3d4a50 ) );
	border: 1px solid black;

	margin: 8px;
	//margin-left: 32px;
	//margin-right: 32px;
	min-width: 192px;
	padding: 5px 10px 5px 10px;
	border-top: 1px solid #ffffff44;
	border-right: 1px solid #4e5b60;
	border-left: 1px solid #4e5b60;
	border-bottom: 1px solid #00000088;

	transition-property: background-color, wash-color;
	transition-delay: 0.0s;
	transition-duration: 0.1s;
	transition-timing-function: linear;
}

.PopupButton Label
{
	color: white;
	font-size: 24px;
	horizontal-align: center;
	vertical-align: middle;
}

.PopupButton:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #a9c8da ), to( #596b75 ) );
}

.PopupButton:disabled
{
	wash-color: #999999;
	saturation: 0;
}

.PopupButton:disabled Label
{
	color: #999999;
}

.PopupButton:active Label
{
	transform: translateY(1px);
}

.PopupButton:active
{
	border: 1px solid black;
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #272f33 ), to( #5a6c75 ) );

	border-top: 1px solid #00000088;
	border-right: 1px solid #00000088;
	border-left: 1px solid #00000088;
	border-bottom: 1px solid #aaaaaa44;
}

.PopupButton.Activated
{
	sound: "ui_quit_menu_fadeout";
}

.PopupButton.Green
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5e842c ), to( #40621d ) );
	border-top: 1px solid #859d6e;
	border-right: 1px solid #496922;
	border-left: 1px solid #496922;
	border-bottom: 1px solid #294211;
}
.PopupButton.Green:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #9eb46c ), to( #60823d ) );
}
.PopupButton.Green:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #204200 ), to( #5e842c ) );
	border-top: 1px solid #294211;
	border-right: 1px solid #496922;
	border-left: 1px solid #496922;
	border-bottom: 1px solid #859d6e;
}

.PopupButton.Blue
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #65a9db ), to( #2b4b75 ) );
	border-top: 1px solid #80bceb;
	border-right: 1px solid #507693;
	border-left: 1px solid #507693;
	border-bottom: 1px solid #273a48;
}
.PopupButton.Blue:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #9ccaf4 ), to( #2b4b75 ) );
}
.PopupButton.Blue:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #2b4b75 ), to( #65a9db ) );
	border-top: 1px solid #273a48;
	border-right: 1px solid #224569;
	border-left: 1px solid #224569;
	border-bottom: 1px solid #567e9e;
}

.PopupButton.Red
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #db6565 ), to( #75362b ) );
	border-top: 1px solid #eb807f;
	border-right: 1px solid #945150;
	border-left: 1px solid #945150;
	border-bottom: 1px solid #472727;
}
.PopupButton.Red:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #f5a39d ), to( #75362b ) );
}
.PopupButton.Red:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #75362b ), to( #db6565 ) );
	border-top: 1px solid #472727;
	border-right: 1px solid #692721;
	border-left: 1px solid #692721;
	border-bottom: 1px solid #9e5855;
}


.PopupButton.Purple
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #a265db ), to( #5d2b75 ) );
	border-top: 1px solid #b87feb;
	border-right: 1px solid #745094;
	border-left: 1px solid #745094;
	border-bottom: 1px solid #382747;
}
.PopupButton.Purple:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #d09df5 ), to( #5d2b75 ) );
}
.PopupButton.Purple:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5d2b75 ), to( #a265db ) );
	border-top: 1px solid #382747;
	border-right: 1px solid #4F3612;
	border-left: 1px solid #4c2069;
	border-bottom: 1px solid #7d559e;
}

.PopupButton.Gold
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #C79123 ), to( #8E601B ) );
	border-top: 1px solid #E4C269;
	border-right: 1px solid #8E601B;
	border-left: 1px solid #4F3612;
	border-bottom: 1px solid #4F3612;
}
.PopupButton.Gold:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #E4C269 ), to( #C79123 ) );
}
.PopupButton.Gold:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #C79123 ), to( #C79123 ) );
	border-top: 1px solid #8E601B;
	border-right: 1px solid #4F3612;
	border-left: 1px solid #4F3612;
	border-bottom: 1px solid #E4C269;
}


.PopupButton.Grey
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #585858 ), to( #3f3f3f ) );
	border-top: 1px solid #858585;
	border-bottom: 1px solid #292929;
	border-left: 1px solid #707070;
	border-right: 1px solid #292929;
}

.PopupButton.Grey:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #787878 ), to( #5f5f5f ) );
}

.PopupButton.Grey:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #3f3f3f ), to( #585858 ) );
	border-top: 1px solid #292929;
	border-right: 1px solid #707070;
	border-left: 1px solid #292929;
	border-bottom: 1px solid #858585;
}

.BlueButton
{
	margin: 8px;
	border: 1px solid #3366aa55;
	height: 34px;
	background-color: #3366aa11;
}

.BlueButton Label
{
	margin: 7px 24px 0px 24px;
	color: #66aadd;
	font-size: 16px;
	letter-spacing: normal;
	text-transform: uppercase;
}

.BlueButton:enabled:hover Label
{
	color: white;
}

.BlueButton:enabled:hover
{
	border: 1px solid #22aaffaa;
	background-color: #3366aa44;
}

.BlueButton:active
{
	background-color: #23468a44;
	sound: "ui_select_blue";
}

.BlueButton:active Label
{
	color: #76baed;
}

.BlueButton:disabled
{
	wash-color: #000000DD;
	saturation: 0;
}

//
//  Flat Button for the times when the understated look says more
//

.FlatButton
{
	margin: 8px;
	height: 28px;
	background-color: #202626;
}

.FlatButton Label
{
	margin: 4px 24px 0px 24px;
	color: #737e7e;
	font-size: 16px;
	letter-spacing: normal;
	text-transform: uppercase;
	letter-spacing: 1px;
}

.FlatButton:hover Label
{
	color: white;
}

.FlatButton:hover
{
	background-color: #333d3c;
}

.FlatButton:active
{
	background-color: #181c1c;
	sound: "ui_select_blue";
}

.FlatButton:active Label
{
	color: #6c6c6c;
}



//
//  Play Button Looking Button
//

.PlayGameButton
{
	vertical-align: bottom;
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5A615Ecc ), to( #879695cc ));
	box-shadow: fill #002a6644 -4px -4px 8px 9px;
	border-top: 1px solid #ffffff44;
	border-right: 1px solid #00000088;
	border-left: 1px solid #ffffff44;
	border-bottom: 1px solid #00000088;

	transition-property: background-color;
	transition-duration: .2s;

	wash-color: #99999922;
}

.PlayGameButton:hover
{

	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5f6663 ), to( #b4c2c1 ));
}

.PlayGameButton:active
{
	wash-color: #ffffff;
}

.PlayGameButton Label
{
	font-size: 24px;
	font-weight: normal;
	letter-spacing: 1px;
	text-transform: uppercase;
	color: #d5d7d7;
	vertical-align: middle;
}

.PlayGameButton:hover Label
{
	color: white;
}


//
// Loading throbber
//

/* haven't designed this yet */

//
// Text Entry
//

TextEntry
{
	border: 1px solid baseBorder;
	width: 352px;
	height: 36px;

	padding: 5px 7px 3px 7px;

	background-color: gradient( linear, 0% 0%, 0% 100%, from( #111111FF ), to( #222222FF ) );

	color: white;
	font-size: 20px;

	text-overflow: clip;
	white-space: nowrap;
}

TextEntry:disabled
{
	border-color: #44444440;
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #11111140 ), to( #22222240 ) );
	color: #999999ff;
}

TextEntry #PlaceholderText
{
	opacity: 1.0;

	transition-property: opacity;
	transition-duration: 0.2s;

	margin-top: 2px;

	color: #555555;
	font-size: 18px;
}

TextEntry:focus #PlaceholderText, TextEntry.HasInput #PlaceholderText
{
	opacity: 0.0;
}

.TextEntryIMECandidateRow
{
	flow-children: right;
	background-color: #000000;
}

.TextEntryIMECandidateRowPrefix
{
	font-size: 20px;
	font-weight: normal;
	color:#FFFFFF;
	background-color: #000000;
	vertical-align: middle;
}

.TextEntryIMECandidateRowSuffix
{
	font-size: 25px;
	font-weight: normal;
	color:#FFFFFF;
	background-color: #333333;
	vertical-align: middle;
}

.TextEntryIMECandidateRow.Highlight .TextEntryIMECandidateRowSuffix
{
	background-color: #4444FF;
}

.TextEntryIMEReadingString
{
	font-size: 20px;
	font-weight: normal;
	color: #DDDDDD;
	background-color: #333333;
	margin-top: 5px;
	margin-bottom: 5px;
	padding: 5px;
	border: 1px solid #FFFFFF;
}

.TextEntryIMEReadingString.NoReadingString
{
	visibility: collapse;
}

.TextEntryIMECandidateList
{
	color: #DDDDDD;
	background-color: #333333;
	flow-children: down;
	border: 1px solid #FFFFFF;
}

TextEntryIMEControls
{
	flow-children: down;
}

TextEntryAutocomplete
{
	width: 320px;
	flow-children: down;
	background-color: #3d4448;
	color: #e1e1e1;
	font-size: 28px;
	overflow: squish scroll;
	z-index: 0;
	//transform: translateY(-40px);
	opacity: 1.0;

	//transition-property: opacity, transform;
	//transition-duration: 0.15s;
	//transition-timing-function: ease-in;

	box-shadow: fill #00000066 -3px -3px 6px 6px;
}

TextEntryAutocomplete > Label
{
	width: 100%;
	padding: 6px 0px 2px 0px;
	margin-top: 0px 0px;
	margin-right: 0px;
	margin-left: 0px;
	padding-left: 16px;

	color: #ffffff99;
	background-color: #3d4448;

	font-size: 18px;
	font-weight: normal;
	z-index: 0;
	border-top: 1px solid #00000066;
	border-bottom: 1px solid #00000066;

	transition-property: background-color;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
}

TextEntryAutocomplete > Label:hover
{
	background-color: #585e62;
}

TextEntryAutocomplete > Label:focus
{
	background-color: #585e62;
}

TextEntry.ThinTextEntry
{
	height: 32px;
	font-size: 18px;
}

DropDownMenu Label
{
	width: 100%;
	padding: 6px 0px 2px 0px;
	margin-top: 0px 0px;
	margin-right: 0px;
	margin-left: 0px;
	padding-left: 16px;

	color: #ffffff99;
	background-color: #3d4448;

	font-size: 18px;
	font-weight: normal;
	z-index: 0;
	border-top: 1px solid #00000066;
	border-bottom: 1px solid #00000066;

	transition-property: background-color;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
}

DropDownMenu Label:hover
{
	background-color: #585e62;
}

DropDownMenu.DropDownMenuVisible
{
	opacity: 1.0;

	max-height: 480px;
	transition-property: box-shadow;
	transform: translateY(0px);
}

//
// Slider
//

Slider
{
	// needs to be at least as wide as the thumb and, as the thumb center stops at end
	// of track, needs to allow for 1/2 the height of the thumb on each end
	width: 30px;
	height: 130px;
	margin-bottom: 20px;
}

#SliderTrack
{
	height: 120px;
	width: 16px;
	background-color: #283842;
	align: center center;
	background-color: black;
	border: 2px solid #5e686966;
	box-shadow: #00000055 -2px -2px 1px 4px;
	z-index: 2;

}

SlottedSlider.HorizontalSlider .SliderNotch
{
	margin-top: 14px;
	border-bottom: 2px solid #5e686966;
	border-left:2px solid #5e686966;
	border-right:2px solid #5e686966;
	background-color: black;
	//border-radius: 13%;
	width: 12px;
	height: 12px;
	vertical-align: bottom;
	z-index: 3;
}

#SliderTrackProgress
{
	width: 10px;
	height: 99%;
	background-color: gradient( linear, 0% 100%, 0% 0%, from( #222222FF ), to( #444f52FF ) );
	align: center center;
	border-radius: 3px;
	z-index: 3;
}

#SliderThumb
{
	background-color: gradient( radial, 50% 50%, -5% -5%, 60% 60%, from( #91a5b9 ), to( #444f52 ) );
	border-radius: 13%;
	width: 30px;
	height: 16px;
	box-shadow: fill black 1px 1px 32px 0px;
	z-index: 4;
}

#SliderThumb:hover
{
	background-color: gradient( radial, 50% 50%, -5% -5%, 60% 60%, from( #b8c5d3 ), to( #444f52 ) );
}

Slider.HorizontalSlider, SlottedSlider.HorizontalSlider
{
	width: 420px;
	height: 30px;
}

Slider.HorizontalSlider #SliderTrack, SlottedSlider.HorizontalSlider #SliderTrack
{
	height: 16px;
	width: 100%;

	padding: 10px;
}

Slider.HorizontalSlider #SliderTrackProgress, SlottedSlider.HorizontalSlider #SliderTrackProgress
{
	width: 100%;
	height: 10px;
	background-color: gradient( linear, 0% 0%, 100% 0%, from( #222222FF ), to( #444f52FF ) );
	box-shadow: inset black 0px 1px 4px 0px;
	margin-left: 1px;
}

Slider.HorizontalSlider #SliderTrackProgress:disabled, SlottedSlider.HorizontalSlider #SliderTrackProgress:disabled
{
	width: 100%;
	height: 10px;
	background-color: gradient( linear, 0% 0%, 100% 0%, from( #010101FF ), to( #333333FF ) );
	box-shadow: inset black 0px 1px 4px 0px;  // flickering when sliding...bug?
	margin-left: 1px;
}


Slider.HorizontalSlider #SliderThumb, SlottedSlider.HorizontalSlider #SliderThumb
{
	background-color: gradient( radial, 50% 50%, -5% -5%, 60% 60%, from( #91a5b9 ), to( #444f52 ) );
	border-radius: 13%;
	width: 16px;
	height: 30px;
}

Slider.HorizontalSlider #SliderThumb:hover, SlottedSlider.HorizontalSlider #SliderThumb:hover
{
	background-color: gradient( radial, 50% 50%, -5% -5%, 60% 60%, from( #b8c5d3 ), to( #444f52 ) );
}

Slider.HorizontalSlider #SliderThumb:disabled, SlottedSlider.HorizontalSlider #SliderThumb:disabled
{
	background-color: #333333;
}

.PreGame #PreGameLabel
{
	opacity: 1;
}

.SliderTick
{
	width: 4px;
	height: 14px;
	background-color: #3B4242;
}


.CarouselContainer
{
	vertical-align: top;
	horizontal-align: right;
	margin-top: 18px;
	margin-right: 18px;
	flow-children: right;
}

.CarouselDot
{
	background-color: none;
	box-shadow:  none;

}

.CarouselDot Label
{
	visibility: collapse;

}

.CarouselDot .RadioBox
{

	min-height: 16px;
	min-width: 16px;
	margin: 8px;
	border: 0px;
	border-radius: 50%;
	box-shadow:  black 0px 0px 4px 0px;
	background-color: #ffffff08;
	//transition-duration: 0s;
}

.CarouselDot:selected .RadioBox
{
	border: 0px;
	background-color: white;
	transition-duration: 0.16s;
	box-shadow:  black 0px 0px 4px 0px;
}

.CarouselDot:hover .RadioBox
{
	box-shadow:  none 0px 0px 4px 0px;
	border: 1px solid white;
}

.CarouselDot:selected:hover .RadioBox
{
	box-shadow:  black 0px 0px 4px 0px;
	border: 0px solid white;
}


//
// ContextMenu
//
.ContextMenu
{
	width: 100%;
	height: 100%;

	transition-property: opacity, transform;
	transition-duration: ContextMenuFadoutTime;
	transition-timing-function: ease-in-out;
	opacity: 1.0;

	z-index: contextmenu_zindex;

	visibility: visible;
}

.ContextMenu.Destructing
{
	opacity: 0.0;
}


//
// SimpleContextMenu - Shows a list of text buttons
//
SimpleContextMenu .ContextMenuBody
{
	padding: 5px;

	flow-children: down;
	width: 230px;

	opacity: 1.0;

	background-color: #212828;
	border: 2px solid black;
	box-shadow: fill 4px 4px 4px #0009;
}

SimpleContextMenu.Wider .ContextMenuBody
{
	width: 250px;
}

SimpleContextMenu.ExtraWide .ContextMenuBody
{
	width: 300px;
}

SimpleContextMenu .ContextMenuBody Button
{
	width: 100%;

	padding: 6px 10px 6px 10px;
	margin: 0px 0px;
	border: 1px solid black;
	background-color: #00000040;
 //   border-radius: 8px;

	transition-property: background-color;
	transition-duration: 0.2s;
}

SimpleContextMenu .ContextMenuBody Label
{
	horizontal-align: left;
}

SimpleContextMenu .ContextMenuBody Button:enabled:hover
{
	background-color: #00000080;
}

SimpleContextMenu .ContextMenuBody Button:enabled:hover Label
{
	color: white;
}

SimpleContextMenu .ContextMenuBody Button:disabled Label
{
	color: #444444;
}

//
//   PROGRESS BAR
//


ProgressBar
{
	width: 320px;
	height: 22px;
	border-radius: 8px;
	background-color: #000000ff;
	box-shadow: #00000066 -3px -3px 6px 6px;
	overflow: noclip;
}

.ProgressBarLeft
{
	//box-shadow: #ffffff66 -3px -3px 6px 6px;
	border-top: 1px solid #ffffff33;
}

.GuildPage .ProgressBarLeft
{
	brightness: 2;
}

.GuildPrimaryColor0 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor0&33 ), color-stop(.98, guildColor0 ), to( #ffffff44 ) ); }
.GuildPrimaryColor1 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor1&33 ), color-stop(.98, guildColor1 ), to( #ffffff44 ) ); }
.GuildPrimaryColor2 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor2&33 ), color-stop(.98, guildColor2 ), to( #ffffff44 ) ); }
.GuildPrimaryColor3 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor3&33 ), color-stop(.98, guildColor3 ), to( #ffffff44 ) ); }
.GuildPrimaryColor4 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor4&33 ), color-stop(.98, guildColor4 ), to( #ffffff44 ) ); }
.GuildPrimaryColor5 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor5&33 ), color-stop(.98, guildColor5 ), to( #ffffff44 ) ); }
.GuildPrimaryColor6 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor6&33 ), color-stop(.98, guildColor6 ), to( #ffffff44 ) ); }
.GuildPrimaryColor7 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor7&33 ), color-stop(.98, guildColor7 ), to( #ffffff44 ) ); }
.GuildPrimaryColor8 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor8&33 ), color-stop(.98, guildColor8 ), to( #ffffff44 ) ); }
.GuildPrimaryColor9 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor9&33 ), color-stop(.98, guildColor9 ), to( #ffffff44 ) ); }
.GuildPrimaryColor10 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor10&33 ), color-stop(.98, guildColor10 ), to( #ffffff44 ) ); }
.GuildPrimaryColor11 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor11&33 ), color-stop(.98, guildColor11 ), to( #ffffff44 ) ); }
.GuildPrimaryColor12 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor12&33 ), color-stop(.98, guildColor12 ), to( #ffffff44 ) ); }
.GuildPrimaryColor13 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor13&33 ), color-stop(.98, guildColor13 ), to( #ffffff44 ) ); }
.GuildPrimaryColor14 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor14&33 ), color-stop(.98, guildColor14 ), to( #ffffff44 ) ); }
.GuildPrimaryColor15 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor15&33 ), color-stop(.98, guildColor15 ), to( #ffffff44 ) ); }
.GuildPrimaryColor16 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor16&33 ), color-stop(.98, guildColor16 ), to( #ffffff44 ) ); }
.GuildPrimaryColor17 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor17&33 ), color-stop(.98, guildColor17 ), to( #ffffff44 ) ); }
.GuildPrimaryColor18 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor18&33 ), color-stop(.98, guildColor18 ), to( #ffffff44 ) ); }
.GuildPrimaryColor19 .ProgressBarLeft { background-color: gradient( linear, 0% 0%, 100% 0%, from( guildColor19&33 ), color-stop(.98, guildColor19 ), to( #ffffff44 ) ); }


//
//   CLOSE BUTTON
//

.CloseButton
{
	width: 24px;
	height: 24px;
	wash-color: #888888;

	background-image: url("s2r://panorama/images/control_icons/x_close_grey_psd.vtex");
	background-size: 24px 24px;
	background-repeat: no-repeat;
	background-position: 50% 50%;
	margin: 4px;

	transition-property: wash-color, pre-transform-scale2d, background-color;
	transition-duration: 0.1s;
	transition-timing-function: ease-in;
}

.CloseButton:hover
{
	wash-color: #bbbbbb;
}

.CloseButton:active
{
	wash-color: white;
	background-size: 20px 20px;
	//pre-transform-scale2d: 0.8;
	sound: "ui_friends_slide_in";
}

//
//   Maximize BUTTON
//

.MaximizeButton
{
	width: 24px;
	height: 24px;
	wash-color: #888888;

	background-image: url("s2r://panorama/images/control_icons/maximize_icon_psd.vtex");
	background-size: 24px 24px;
	background-repeat: no-repeat;
	background-position: 50% 50%;
	margin: 4px;

	transition-property: wash-color, pre-transform-scale2d, background-color;
	transition-duration: 0.1s;
	transition-timing-function: ease-in;
}

.MaximizeButton:hover
{
	wash-color: #bbbbbb;
}

.MaximizeButton:active
{
	wash-color: white;
	background-size: 20px 20px;
	//pre-transform-scale2d: 0.8;
	sound: "ui_friends_slide_in";
}

//
//   Restore BUTTON
//

.RestoreButton
{
	width: 24px;
	height: 24px;
	wash-color: #888888;

	background-image: url("s2r://panorama/images/control_icons/restore_icon_psd.vtex");
	background-size: 24px 24px;
	background-repeat: no-repeat;
	background-position: 50% 50%;
	margin: 4px;

	transition-property: wash-color, pre-transform-scale2d, background-color;
	transition-duration: 0.1s;
	transition-timing-function: ease-in;
}

.RestoreButton:hover
{
	wash-color: #bbbbbb;
}

.RestoreButton:active
{
	wash-color: white;
	background-size: 20px 20px;
	//pre-transform-scale2d: 0.8;
	sound: "ui_friends_slide_in";
}

//
//   HELP BUTTON
//

.HelpButton Label
{
	font-weight: bold;
	padding: 0px 8px 0px 8px;
	wash-color: #888888;
	color: white;
	font-size: 20px;

	transition-property: wash-color, pre-transform-scale2d;
	transition-duration: 0.1s;
	transition-timing-function: ease-in;
}

.HelpButton:hover Label
{
	wash-color: #bbbbbb;
}

.HelpButton:active Label
{
	wash-color: white;
}


//
//   CONTROL ICON BUTTON
//

.ControlIconButton
{
	width: 28px;
	height: 28px;
	wash-color: #888888;
	background-image: url("s2r://panorama/images/control_icons/dota_logo_white_png.vtex");
	background-size: 100% 100%;
	background-position: 50% 50%;
	background-repeat: no-repeat;
	pre-transform-scale2d: 1;
	margin: 4px;
	transition-property: wash-color, pre-transform-scale2d;
	transition-duration: 0.1s;
	transition-timing-function: ease-in;
}

.ControlIconButton:hover
{
	wash-color: #bbbbbb;
}

.ControlIconButton:active
{
	wash-color: white;
	background-size: 24px 24px;
}

//
//   SETTINGS BUTTON
//

.SettingsButton
{
	background-image: url("s2r://panorama/images/control_icons/gear_png.vtex");
}

.SettingsButton.Activated
{
	animation-name: SettingsButtonAnimation;
	animation-duration: 0.50s;
	animation-delay: 0.15s;
	animation-timing-function: linear;
	animation-iteration-count: 1;
}

.SettingsButton:active
{
	sound: "ui_rollover_micro";
}


@keyframes 'SettingsButtonAnimation'
{
	0%
	{
		transform: rotateZ( 0deg );
	}

	100%
	{
		transform: rotateZ( 45deg );
	}
}

//
//   REFRESH BUTTON
//

.RefreshButton
{
	background-image: url("s2r://panorama/images/control_icons/refresh_psd.vtex");
	background-repeat: no-repeat;
	background-position: 50%;
}

.RefreshButton.Activated
{
	animation-name: RefreshButtonAnimation;
	animation-duration: 0.55s;
	animation-delay: 0.15s;
	animation-timing-function: ease-in-out;
	animation-iteration-count: 1;
	animation-direction: reverse;
}


@keyframes 'RefreshButtonAnimation'
{
	0%
	{
		transform: rotateZ( 0deg );
	}
	100%
	{
		transform: rotateZ( 180deg );
	}

}

//
// 	EDIT BUTTON
//

.EditButton
{
	background-image: url("s2r://panorama/images/control_icons/edit_png.vtex");
	background-size: 65%;
}



//
// 	SPINNERS
//

.Spinner
{
	width: 28px;
	height: 28px;

	background-image: url("s2r://panorama/images/status_icons/loadingthrobber_round_psd.vtex");
	background-repeat: no-repeat;
	background-position: 50% 50%;
	background-size: contain;

	animation-name: SpinnerRotate;
	animation-duration: 1.0s;
	animation-timing-function: linear;
	animation-iteration-count: infinite;
}

@keyframes 'SpinnerRotate'
{
	0%
	{
		transform: rotateZ( 360deg );
	}


	50%
	{
		transform: rotateZ( 180deg );
	}

	100%
	{
		transform: rotateZ( 0deg );
	}
}

//
//   ARROWS
//

.Arrow
{
	width: 60px;
	height: 60px;
	vertical-align: middle;
	wash-color: #9db1b7;
	background-image: url("s2r://panorama/images/control_icons/arrow_right_png.vtex");
	background-size: contain;
	background-repeat: no-repeat;
	background-position: 50% 50%;

	margin-right: 3px;
	margin-left: 3px;

	transition-property: wash-color, transform;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
}

.Arrow:enabled:hover
{
	wash-color: white;
}

.Arrow.Left
{
	horizontal-align: left;
	transform: scaleX(-1);
}

.Arrow.Left:enabled:hover
{
	transform: scaleX(-1) translateX(-3px);
}

.Arrow.Right
{
	horizontal-align: right;
}

.Arrow.Right:enabled:hover
{
	transform: translateX(3px);
}

.Arrow.Left:active
{
	transform: scaleX(-1) translateX(-6px);
	sound: "ui_select_arrow";
}

.Arrow.Right:active
{
	transform: translateX(6px);
	sound: "ui_select_arrow";
}

.Arrow.Left:disabled
{
	opacity: 0.1;
	transform: scaleX(-1) translateX(0px);
	pre-transform-scale2d: .1;
}

.Arrow.Right:disabled
{
	opacity: 0.1;
	transform: translateX(0px);
	pre-transform-scale2d: .1;
}

.ExpandDownArrow
{
	height: 16px;
	width: 16px;
	background-image: url("s2r://panorama/images/control_icons/arrow_dropdown_png.vtex");
	background-size: contain;
	wash-color: #888888;
	margin-left: 4px;
	margin-right: 4px;
	margin-top: -1px;

	transition-property: wash-color, transform, opacity;
	transition-duration: 0.24s;
	transition-timing-function: ease-in-out;
}

.PopoutArrow
{
	width: 16px;
	height: 16px;

	background-image: url("s2r://panorama/images/control_icons/arrow_top_right_png.vtex");
	background-repeat: no-repeat;
	background-size: contain;
	background-position: center;
}

//
//   RARITY COLORS
//

@define rarity_color_common: #b0c3d9;
@define rarity_color_uncommon: #5e98d9;
@define rarity_color_rare: #4b69ff;
@define rarity_color_mythical: #8847ff;
@define rarity_color_legendary: #d32ce6;
@define rarity_color_ancient: #eb4b4b;
@define rarity_color_immortal: #e4ae39;
@define rarity_color_arcana: #ade55c;
@define rarity_color_seasonal: #fff34f;


.ItemRarity_common .ItemRarityBackgroundColor, .ItemRarity_common.ItemRarityBackgroundColor
{
	background-color: rarity_color_common;
}

.ItemRarity_uncommon .ItemRarityBackgroundColor, .ItemRarity_uncommon.ItemRarityBackgroundColor
{
	background-color: rarity_color_uncommon;
}

.ItemRarity_rare .ItemRarityBackgroundColor, .ItemRarity_rare.ItemRarityBackgroundColor
{
	background-color: rarity_color_rare;
}

.ItemRarity_mythical .ItemRarityBackgroundColor, .ItemRarity_mythical.ItemRarityBackgroundColor
{
	background-color: rarity_color_mythical;
}

.ItemRarity_legendary .ItemRarityBackgroundColor, .ItemRarity_legendary.ItemRarityBackgroundColor
{
	background-color: rarity_color_legendary;
}

.ItemRarity_ancient .ItemRarityBackgroundColor, .ItemRarity_ancient.ItemRarityBackgroundColor
{
	background-color: rarity_color_ancient;
}

.ItemRarity_immortal .ItemRarityBackgroundColor, .ItemRarity_immortal.ItemRarityBackgroundColor
{
	background-color: rarity_color_immortal;
}

.ItemRarity_arcana .ItemRarityBackgroundColor, .ItemRarity_arcana.ItemRarityBackgroundColor
{
	background-color: rarity_color_arcana;
}

.ItemRarity_seasonal .ItemRarityBackgroundColor, .ItemRarity_seasonal.ItemRarityBackgroundColor
{
	background-color: rarity_color_seasonal;
}

.ItemRarity_common .ItemRarityColor, .ItemRarity_common.ItemRarityColor, a.ItemRarity_common.ItemRarityColor
{
	color: rarity_color_common;
}

.ItemRarity_uncommon .ItemRarityColor, .ItemRarity_uncommon.ItemRarityColor, a.ItemRarity_uncommon.ItemRarityColor
{
	color: rarity_color_uncommon;
}

.ItemRarity_rare .ItemRarityColor, .ItemRarity_rare.ItemRarityColor, a.ItemRarity_rare.ItemRarityColor
{
	color: rarity_color_rare;
}

.ItemRarity_mythical .ItemRarityColor, .ItemRarity_mythical.ItemRarityColor, a.ItemRarity_mythical.ItemRarityColor
{
	color: rarity_color_mythical;
}

.ItemRarity_legendary .ItemRarityColor, .ItemRarity_legendary.ItemRarityColor, a.ItemRarity_legendary.ItemRarityColor
{
	color: rarity_color_legendary;
}

.ItemRarity_ancient .ItemRarityColor, .ItemRarity_ancient.ItemRarityColor, a.ItemRarity_ancient.ItemRarityColor
{
	color: rarity_color_ancient;
}

.ItemRarity_immortal .ItemRarityColor, .ItemRarity_immortal.ItemRarityColor, a.ItemRarity_immortal.ItemRarityColor
{
	color: rarity_color_immortal;
}

.ItemRarity_arcana .ItemRarityColor, .ItemRarity_arcana.ItemRarityColor, a.ItemRarity_arcana.ItemRarityColor
{
	color: rarity_color_arcana;
}

.ItemRarity_seasonal .ItemRarityColor, .ItemRarity_seasonal.ItemRarityColor, a.ItemRarity_seasonal.ItemRarityColor
{
	color: rarity_color_seasonal;
}

.ItemRarity_common:hover .ItemRarityColor, .ItemRarity_common.ItemRarityColor:hover, a.ItemRarity_common.ItemRarityColor:hover
{
	text-decoration: none;
}

.ItemRarity_uncommon:hover .ItemRarityColor, .ItemRarity_uncommon.ItemRarityColor:hover, a.ItemRarity_uncommon.ItemRarityColor:hover
{
	text-decoration: none;
}

.ItemRarity_rare:hover .ItemRarityColor, .ItemRarity_rare.ItemRarityColor:hover, a.ItemRarity_rare.ItemRarityColor:hover
{
	text-decoration: none;
}

.ItemRarity_mythical:hover .ItemRarityColor, .ItemRarity_mythical.ItemRarityColor:hover, a.ItemRarity_mythical.ItemRarityColor:hover
{
	text-decoration: none;
}

.ItemRarity_legendary:hover .ItemRarityColor, .ItemRarity_legendary.ItemRarityColor:hover, a.ItemRarity_legendary.ItemRarityColor:hover
{
	text-decoration: none;
}

.ItemRarity_ancient:hover .ItemRarityColor, .ItemRarity_ancient.ItemRarityColor:hover, a.ItemRarity_ancient.ItemRarityColor:hover
{
	text-decoration: none;
}

.ItemRarity_immortal:hover .ItemRarityColor, .ItemRarity_immortal.ItemRarityColor:hover, a.ItemRarity_immortal.ItemRarityColor:hover
{
	text-decoration: none;
}

.ItemRarity_arcana:hover .ItemRarityColor, .ItemRarity_arcana.ItemRarityColor:hover, a.ItemRarity_arcana.ItemRarityColor:hover
{
	text-decoration: none;
}

.ItemRarity_seasonal:hover .ItemRarityColor, .ItemRarity_seasonal.ItemRarityColor:hover, a.ItemRarity_seasonal.ItemRarityColor:hover
{
	text-decoration: none;
}

.PersonaName
{
	color: #FFFFFF;
}

.PersonaName:hover
{
	text-decoration: none;
}

.PersonaGuildTag
{
	font-weight: light;
	font-size: 13px;
}

.MainNameLine .PersonaGuildTag
{
	font-weight: light;
	font-size: 23px;
}

.PersonaGuildTag:hover
{
	text-decoration: none;
}

.GuildTier0.PersonaGuildTag { color: guildTierColor0; }
.GuildTier1.PersonaGuildTag { color: guildTierColor1; }
.GuildTier2.PersonaGuildTag { color: guildTierColor2; }
.GuildTier3.PersonaGuildTag { color: guildTierColor3; }

#MousePanningImage
{
	wash-color: #ffffffff;
	visibility: visible;
	width: mousepanningcursorsize;
	height: mousepanningcursorsize;
	z-index: 10;
	opacity: 0.0;
	transition-property: opacity;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
}

.MousePanning #MousePanningImage
{
	opacity: 0.5;
}

.StateParticipatingInTourney .RedBackgroundImageButton Label
{
	text-transform: uppercase;
	font-size: 21px;
	color: #ccc;
	//color: #d04721;
	margin-top: 2px;
	transition-property: color;
	transition-duration: .2s;
}

.RedBackgroundImageButton
{
	background-image: url("s2r://panorama/images/red_large_off_png.vtex");
	background-size: 100% 100%;
	background-position: 50% 50%;
	background-repeat: no-repeat;
	transition-property: brightness, saturation;
	transition-duration: .3s;
	overflow: noclip noclip;
}

.RedBackgroundImageButton:hover
{
	background-image: url("s2r://panorama/images/red_large_off_png.vtex");
	background-size: 100% 100%;
	brightness: 3;
	saturation: 1.4;
	background-position: 50% 50%;
	background-repeat: no-repeat;
}

.RedBackgroundImageButton Panel
{
	horizontal-align: center;
	vertical-align: center;
}

.RedBackgroundImageButton Label
{
	margin-top: 1px;
	margin-right: 4px;
	color: white;
	font-size: 22px;
}

.RedBackgroundImageButton Image
{
	vertical-align: center;
	width: 25px;
	height: 25px;
}

.DOTAReturnToDashboardOverlay
{
	margin: 2px;
	width: 48px;
	height: 48px;
	background-image: url("s2r://panorama/images/control_icons/return_to_dashboard_background_psd.vtex");
	background-repeat: no-repeat;
	background-position: 50% 50%;
	background-size: 100%;
}

.DOTAShowDashboardButton
{
	transform: rotateZ( 180deg );

	horizontal-align: center;
	vertical-align: center;
	width: 62px;
	height: 57px;
	background-image: url("s2r://panorama/images/control_icons/return_to_game_png.vtex");
	background-size: 27px;
	background-repeat: no-repeat;
	background-position: 50% 50%;
	wash-color: #ffffff;

	transition-property: background-color, wash-color;
	transition-delay: 0.0s;
	transition-duration: 0.2s;
	transition-timing-function: ease-out;
}

.DOTAShowDashboardButton:hover
{
	wash-color: #ffffff;
	background-size: 32px;
}

#DOTANotificationButton .TopBarSmallButton
{
	background-position: 20% 50%;
}

.TopBarSmallButton
{
	margin: 0px;
	width: 62px;
	height: 61px;

	background-size: 30px;
	background-repeat: no-repeat;
	background-position: 50% 50%;

	transition-property: background-color, wash-color;
	transition-delay: 0.0s;
	transition-duration: 0.2s;
	transition-timing-function: ease-out;
}

.TopBarSmallButton:hover
{
	wash-color: #bd5e2daa;
}

.TopBarSmallButton:active
{
	background-position: 50% 55%;
}

.CardHeaderSmallButton
{
	margin: 5px;
	width: 20px;
	height: 23px;
	background-size: 20px 23px;
	background-repeat: no-repeat;
	transition-property: background-color, wash-color;
	transition-delay: 0.0s;
	transition-duration: 0.2s;
	transition-timing-function: ease-out;
}

.RadialSweep
{
	width: 150%;
	height: 150%;

	transform: translateX(-6px) translateY(-6px);

	background-image: url("s2r://panorama/images/textures/radialsweep_psd.vtex");
	background-repeat: no-repeat;
	background-position: 50% 50%;
	background-size: 100%;

	animation-name: RadialSweep;
	animation-duration: 2s;
	animation-timing-function: linear;
	animation-iteration-count: infinite;
}


@keyframes 'RadialSweep'
{
	0%
	{
		transform: rotateZ( 0deg ) translateX(-6px) translateY(-6px);
	}

	50%
	{
		transform: rotateZ( -180deg ) translateX(-6px) translateY(-6px);
	}

	100%
	{
		transform: rotateZ( -360deg ) translateX(-6px) translateY(-6px);
	}
}


.ReadyToPlay
{
	color: #aceba9;
	font-size: 42px;
}

.HorizontalDivider
{
	width: 100%;
	height: 1px;
	background-color: #ffffff88;
}

.VerticalDivider
{
	width: 1px;
	height: 100%;
	background-color: #ffffff88;
}

.HorizontalLineFill
{
	width: fill-parent-flow (1.0);
	height: 1px;
	background-color: white;
	vertical-align: middle;
	margin-left: 4px;
	margin-right: 4px;
}


.Star
{
	background-image: url("s2r://panorama/images/status_icons/icon_star_empty_png.vtex");
	background-repeat: no-repeat;
	background-size: 100% 100%;
	wash-color: #666666;
	height: 19px;
	width: 19px;
	margin-right: 3px;
}

.StarLit
{
	background-image: url("s2r://panorama/images/status_icons/icon_star_png.vtex");
	wash-color: #ffffff;
}


.HelpIcon,
.InfoIcon
{
	width: 18px;
	height: 18px;
	background-image: url("s2r://panorama/images/status_icons/information_psd.vtex");
	background-position: 50% 50%;
	background-repeat: no-repeat;
	background-size: contain;
	margin-left: 6px;
	margin-right: 6px;

	tooltip-position: right;
	tooltip-body-position: 50% 10%;
	wash-color: #888888;
	vertical-align: middle;

	transition-property: wash-color;
	transition-timing-function: ease-in-out;
	transition-duration: 0.2s;

}

.HelpIcon
{
	background-image: url("s2r://panorama/images/status_icons/helpicon_psd.vtex");
}


.TextGlowPulse
{
	animation-name: TextGlowPulse;
	animation-duration: 1.1s;
	animation-delay: 0s;
	animation-timing-function: linear;
	animation-iteration-count: infinite;
}



@keyframes 'TextGlowPulse'
{
	0%
	{
		text-shadow: 0px 0px 4px 1.0 #AAE9A700;
	}

	50%
	{
		text-shadow: 0px 0px 4px 1.0 #AAE9A7dd;
	}

	100%
	{
		text-shadow: 0px 0px 4px 1.0 #AAE9A700;
	}
}

/* Rich Presence colors */

.StateFindingMatch DOTAUserRichPresence.FriendPlayingDota Label, .StateFindingMatch DOTAUserName.FriendPlayingDota Label a .PersonaName		{ color: #CDFFC9; text-shadow: 0px 0px 6px 2.0 #6A9561;}
.LookingForPartyMembers DOTAUserRichPresence.FriendPlayingDota Label, .LookingForPartyMembers DOTAUserName.FriendPlayingDota Label a .PersonaName		{ color: #CDFFC9; text-shadow: 0px 0px 6px 2.0 #6A9561;}
DOTAUserRichPresence.FriendPlayingDota Label, DOTAUserName.FriendPlayingDota Label a .PersonaName			{ color: #6A9561; }
DOTAUserRichPresence.FriendPlayingDotaIdle Label, DOTAUserName.FriendPlayingDotaIdle Label a .PersonaName	{ color: #6A9561; }
DOTAUserRichPresence.FriendPending Label, DOTAUserName.FriendPending Label a .PersonaName					{ color: #eeee82; }
DOTAUserRichPresence.FriendOnline Label, DOTAUserName.FriendOnline Label a .PersonaName						{ color: #6a9dcc; }
DOTAUserRichPresence.FriendOffline Label, DOTAUserName.FriendOffline Label a .PersonaName					{ color: #444444; }

@keyframes 'ProbablyUnlocalizedAnimation'
{
	0%
	{
		pre-transform-scale2d: 0.95;
	}

	50%
	{
		pre-transform-scale2d: 1.05;
	}

	100%
	{
		pre-transform-scale2d: 0.95;
	}
}

Label.ProbablyUnlocalizedText
{
	background-color: red;
	color: yellow;

	animation-name: ProbablyUnlocalizedAnimation;
	animation-duration: 0.4s;
	animation-timing-function: linear;
	animation-iteration-count: infinite;
}


/* EdgeScroller */
EdgeScroller.CanScrollHorizontal
{
	padding-left: 18px;
	padding-right: 18px;
}

EdgeScroller.CanScrollVertical
{
	padding-top: 18px;
	padding-bottom: 18px;
}

EdgeScrollBar
{
	width: 100%;
	height: 100%;
	layout-position: fixed;

	transition-property: transform;
	transition-timing-function: ease-in-out;
	transition-duration: 0.2s;
}

EdgeScrollBar .EdgeScrollButton
{
	background-color: #ffffff10;
	background-size: contain;
	background-position: center;
	background-repeat: no-repeat;

	wash-color: #999999;

	transition-property: wash-color;
	transition-duration: 0.1s;
}

EdgeScrollBar .EdgeScrollButton:hover
{
	wash-color: #cccccc;
}
EdgeScrollBar .EdgeScrollButton:active
{
	wash-color: #ffffff;
}

EdgeScrollBar.Horizontal .EdgeScrollButton
{
	width: 16px;
	height: 100%;
}
EdgeScrollBar.Horizontal #MinButton
{
	horizontal-align: left;
	background-image: url("s2r://panorama/images/control_icons/arrow_solid_left_png.vtex");
}
EdgeScrollBar.Horizontal #MaxButton
{
	horizontal-align: right;
	background-image: url("s2r://panorama/images/control_icons/arrow_solid_right_png.vtex");
}

EdgeScrollBar.Vertical .EdgeScrollButton
{
	width: 100%;
	height: 16px;
}
EdgeScrollBar.Vertical #MinButton
{
	vertical-align: top;
	background-image: url("s2r://panorama/images/control_icons/arrow_solid_up_png.vtex");
}
EdgeScrollBar.Vertical #MaxButton
{
	vertical-align: bottom;
	background-image: url("s2r://panorama/images/control_icons/arrow_solid_down_png.vtex");
}

EdgeScrollBar.AtMinimum #MinButton,
EdgeScrollBar.AtMaximum #MaxButton
{
	visibility: collapse;
}


/* Pagination arrows and buttons */
.PaginationArrow
{
	background-image: url("s2r://panorama/images/control_icons/arrow_solid_right_png.vtex");
	background-repeat: no-repeat;
	background-color: #90a3a8;
	background-size: 12px 12px;
	background-position: 63% 50%;
	border-radius: 50%;
	width: 23px;
	height: 23px;
	vertical-align: top;
	margin-right: 4px;
	opacity: 0.2;
	transition-property: opacity;
	transition-duration: 0.2s;
}

.PaginationArrow:enabled:hover
{
	opacity: 0.6;
}

.PaginationArrow:active
{
	opacity: 1.0;
}

.PaginationArrow:disabled
{
	brightness: 0.5;
}

.PaginationArrow.Left
{
	transform: scaleX( -1.0 );
}

.PaginationArrow.Right
{
}

.PaginationArrow.Up
{
	transform: rotateZ( -90.0deg );
}

.PaginationArrow.Down
{
	transform: rotateZ( 90.0deg );
}

.PaginationButton
{
	//background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, from( #7e8f93 ), to( #000000 ) );
	background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, color-stop(0.0, #7e8f93 ), color-stop(0.5, #7e8f93), color-stop(0.6, #00000000 ) );
	//background-color: #7e8f93;
	background-repeat: no-repeat;
	//background-size: 10px 20px;
	background-position: 50% 50%;
	width: 16px;
	height: 16px;
	//padding: 10px;
	margin: 1px 0px 1px 1px;
	vertical-align: center;
	opacity: .2;
	border-radius: 50%;
}

.Gold.PaginationButton,
.Gold.PaginationButton:selected
{
	background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, color-stop(0.0, #FAAB36 ), color-stop(0.5, #FAAB36), color-stop(0.6, #00000000 ) );
}

.Silver.PaginationButton,
.Silver.PaginationButton:selected
{
	background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, color-stop(0.0, #B7B7B7 ), color-stop(0.5, #B7B7B7), color-stop(0.6, #00000000 ) );
}

.Red.PaginationButton,
.Red.PaginationButton:selected
{
	background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, color-stop(0.0, #aa2305 ), color-stop(0.5, #aa2305), color-stop(0.6, #00000000 ) );
}

.Green.PaginationButton,
.Green.PaginationButton:selected
{
	background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, color-stop(0.0, #0caa05 ), color-stop(0.5, #20aa05), color-stop(0.6, #00000000 ) );
}

.Blue.PaginationButton,
.Blue.PaginationButton:selected
{
	background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, color-stop(0.0, #0563aa ), color-stop(0.5, #0563aa), color-stop(0.6, #00000000 ) );
}

.Teal.PaginationButton,
.Teal.PaginationButton:selected
{
	background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, color-stop(0.0, #05aa7d ), color-stop(0.5, #05aa7d), color-stop(0.6, #00000000 ) );
}


.PaginationButton:enabled:hover
{
	opacity: 0.6;
}

.PaginationButton:selected
{
	//background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, from( #e7f6f5 ), to( #333399 ) );
	background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, color-stop(0.0, #9db1b7 ), color-stop(0.5, #9db1b7), color-stop(0.6, #00000000 ) );
	//background-color: #90a3a8;
	opacity: 1;
	border-radius: 50%;
}

@keyframes 'OpacityPulse'
{
	0%
	{
		opacity: 1.0;
	}

	50%
	{
		opacity: 0.2;
	}

	100%
	{
		opacity: 1.0;
	}
}


/* Use a tiny border-radius to use a different shader and get proper anti-aliasing, but only on high-end machines.
   On low end machines, the memory cost from the render target is really high. */
.Rotating2dPanel
{
	border-radius: 0.1px;
}
.LowVisualQuality .Rotating2dPanel
{
	border-radius: 0px;
}




/* Styles for the DragZoom Panel */
DragZoom
{
	z-index: 2;
}

#DragZoomContentPanel
{
	vertical-align: middle;
	horizontal-align: middle;
	overflow: noclip;
	z-index: 1;
	transition-property: transform, position;
	transition-delay: 0.0s;
	transition-timing-function: ease-in-out;
}

.DragZoomFastTransition
{
	transition-duration: 0.0s;
}

.DragZoomSlowTransition
{
	transition-duration: 0.5s;
}

/* End of Styles for DragZoom Panel */


/* DOTASortHeader */
DOTASortHeader
{
	flow-children: right;
}

DOTASortHeader .SortDirectionArrow
{
	width: 20px;
	height: 20px;
	margin-left: 2px;
	margin-bottom: 3px;
	vertical-align: middle;

	background-size: contain;
	background-repeat: no-repeat;
	background-position: 50% 50%;
}

DOTASortHeader.SortAscending .SortDirectionArrow
{
	background-image: url("s2r://panorama/images/control_icons/arrow_dropdown_png.vtex");
	transform: scaleY( -1.0 );
}
DOTASortHeader.SortDescending .SortDirectionArrow
{
	background-image: url("s2r://panorama/images/control_icons/arrow_dropdown_png.vtex");
}


/* DOTAPunchCard */
DOTAPunchCard
{
	flow-children: right;
}

DOTAPunchCard .StampPanel
{
	width: 100px;
	height: 83px;

	margin-left: -50px;

	background-size: 100% 100%;

	background-image: url("s2r://panorama/images/punch_card/punch_card_arrow_pip_off_png.vtex");
}

DOTAPunchCard .StampPanel:selected
{
	background-image: url("s2r://panorama/images/punch_card/punch_card_arrow_pip_on_png.vtex");
}

DOTAPunchCard .StampPanel.StampIndex0
{
	margin-left: 0px;
}

DOTAPunchCard .StampPanel.StampIndex0 { z-index: 100; }
DOTAPunchCard .StampPanel.StampIndex1 { z-index: 99; }
DOTAPunchCard .StampPanel.StampIndex2 { z-index: 98; }
DOTAPunchCard .StampPanel.StampIndex3 { z-index: 97; }
DOTAPunchCard .StampPanel.StampIndex4 { z-index: 96; }
DOTAPunchCard .StampPanel.StampIndex5 { z-index: 95; }
DOTAPunchCard .StampPanel.StampIndex6 { z-index: 94; }
DOTAPunchCard .StampPanel.StampIndex7 { z-index: 93; }
DOTAPunchCard .StampPanel.StampIndex8 { z-index: 92; }
DOTAPunchCard .StampPanel.StampIndex9 { z-index: 91; }

DOTAPunchCard #RewardPanel
{
	height: 83px;
	width: 200px;
	vertical-align: middle;

	margin-left: -50px;
	margin-right: 28px;
}

DOTAPunchCard.StampsFull #RewardPanel
{
	background-image: url("s2r://panorama/images/punch_card/punch_card_bonus_backer_on_png.vtex");
	background-position: 0% 50%;
}

DOTAPunchCard #RewardPanelBackground
{
	background-color: black;
	height: 33px;
	width: 180px;
	vertical-align: middle;
}

DOTAPunchCard.StampsFull #RewardPanelBackground
{
	visibility: collapse;
}

DOTAPunchCard #RewardPanel Label
{
	horizontal-align: center;
	text-align: center;
	vertical-align: middle;
	text-transform: uppercase;
	margin-top: 3px;
	color: white;
	font-weight: bold;
}

DOTAPunchCard.StampCount0 #RewardPanel Label { text-shadow: 0px 0px 4px 1.0 #00ffff; }
DOTAPunchCard.StampCount1 #RewardPanel Label { text-shadow: 0px 0px 4px 2.0 #00ffff; }
DOTAPunchCard.StampCount2 #RewardPanel Label { text-shadow: 0px 0px 4px 3.0 #00ffff; }
DOTAPunchCard.StampCount3 #RewardPanel Label { text-shadow: 0px 0px 4px 4.0 #00ffff; }
DOTAPunchCard.StampCount4 #RewardPanel Label { text-shadow: 0px 0px 4px 5.0 #00ffff; }
DOTAPunchCard.StampCount5 #RewardPanel Label { text-shadow: 0px 0px 5px 5.0 #00ffff; }
DOTAPunchCard.StampCount6 #RewardPanel Label { text-shadow: 0px 0px 6px 5.0 #00ffff; }
DOTAPunchCard.StampCount7 #RewardPanel Label { text-shadow: 0px 0px 7px 5.0 #00ffff; }
DOTAPunchCard.StampCount8 #RewardPanel Label { text-shadow: 0px 0px 8px 5.0 #00ffff; }
DOTAPunchCard.StampCount9 #RewardPanel Label { text-shadow: 0px 0px 9px 5.0 #00ffff; }

EconItemImage #Overlay
{
	width: 100%;
	height: 100%;
}

.FillWidth
{
	width: fill-parent-flow( 1.0 );
}
.FillHeight
{
	height: fill-parent-flow( 1.0 );
}


.Vignette
{
	width: 100%;
	height: 100%;
	background-image: url("s2r://panorama/images/backgrounds/dashboard_vignette_png.vtex");
	background-size: cover;

	transition-property: opacity;
	transition-duration: 0.23s;
	transition-timing-function: ease-in-out;
}

.RightVignette
{
	horizontal-align: right;
	height: 100%;
	width: 64px;
	background-color: gradient( linear, 0% 0%, 100% 0%, from( #00000000 ), to( #000000ff ) );
}

.LeftVignette
{
	horizontal-align: left;
	height: 100%;
	width: 64px;
	background-color: gradient( linear, 0% 0%, 100% 0%, from( #000000ff ), to( #00000000 ) );
}



.PanelSlideThumb
{
	width: 32px;
	height: 96px;
	transform: translateX( 0px );
	background-color: #181818cc;
	border-top-right-radius: 12px;
	border-bottom-right-radius: 12px;
	box-shadow: #000000aa -4px -4px 8px 8px;
	vertical-align: middle;
	horizontal-align: right;
}

.PanelSlideThumbArrow
{
	width: 16px;
	height: 16px;
	vertical-align: middle;
	horizontal-align: right;
	background-image: url("s2r://panorama/images/control_icons/double_arrow_left_png.vtex");
	background-position: 0% 50%;
	background-size: contain;
	background-repeat: no-repeat;
	wash-color: #91aba6;

	transform: translateX( -9px );

	transition-property: transform, wash-color;
	transition-delay: 0.0s;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
}

.PanelSlideThumb:hover
{
	background-color: #383838cc;
}

.PanelSlideThumb:hover .PanelSlideThumbArrow
{
	transform: translateX( -12px );
	wash-color: white;
}

.DrawerClosed .PanelSlideThumbArrow
{
	transform: rotateZ(180deg) translateX( -6px );
}

.DrawerClosed .PanelSlideThumb:hover .PanelSlideThumbArrow
{
	transform: translateX( 3px ) rotateZ(180deg);
	wash-color: white;
}

.DrawerClosed
{
	transform: translateX( -10px );
}

.RightAlign
{
	horizontal-align: right;
}

.LeftAlign
{
	horizontal-align: left;
}

.CenterAlign
{
	horizontal-align: center;
}


.TopAlign
{
	vertical-align: top;
}


.BottomAlign
{
	vertical-align: bottom;
}

.MiddleAlign
{
	vertical-align: center;
}

.VisibilityCollapsed
{
	visibility: collapse;
}


/* Ability Modifiers */
.ModifierIcon
{
	width: 24px;
	height: 24px;
	background-image: url("s2r://panorama/images/status_icons/modifier_animation_psd.vtex");
	background-repeat: no-repeat;
	background-position: center 50% 50%;
	background-size: 24px 24px;
}

.ModifierIcon.Empty
{
	visibility: collapse;
}

.ModifierIcon.icon_replacement
{
	background-image: url("s2r://panorama/images/status_icons/modifier_icons_psd.vtex");
}

.ModifierIcon.particle
{
	background-image: url("s2r://panorama/images/status_icons/modifier_spelleffects_psd.vtex");
}

.ModifierIcon.particle_create
{
	background-image: url("s2r://panorama/images/status_icons/modifier_ambienteffects_psd.vtex");
}

.ModifierIcon.speech
{
	background-image: url("s2r://panorama/images/status_icons/modifier_voice_psd.vtex");
}

.ModifierIcon.activity
{
	background-image: url("s2r://panorama/images/status_icons/modifier_animation_psd.vtex");
}

.ModifierIcon.loading_screen
{
	background-image: url("s2r://panorama/images/status_icons/modifier_loading_screen_psd.vtex");
}

.ModifierIcon.cursor_pack
{
	background-image: url("s2r://panorama/images/status_icons/modifier_cursor_pack_psd.vtex");
}

.ModifierIcon.portrait_game
{
	background-image: url("s2r://panorama/images/status_icons/modifier_portrait_game_psd.vtex");
}

.ModifierIcon.model
{
	background-image: url("s2r://panorama/images/status_icons/modifier_model_psd.vtex");
}

.ModifierIcon.custom_kill_effect
{
	background-image: url("s2r://panorama/images/status_icons/modifier_kill_effect_psd.vtex");
}

/*these are temp*/

.ModifierIcon.soundscape
{
	background-image: url("s2r://panorama/images/status_icons/modifier_voice_psd.vtex");
}

.ModifierIcon.sound
{
	background-image: url("s2r://panorama/images/status_icons/modifier_voice_psd.vtex");
}

.ModifierIcon.announcer
{
	background-image: url("s2r://panorama/images/status_icons/modifier_voice_psd.vtex");
}

.ModifierIcon.pet
{
	background-image: url("s2r://panorama/images/status_icons/modifier_model_psd.vtex");
}


/* ToolTip Styles */

#DOTAHud .TooltipContainer
{
	transition-duration: 0.0s;
}

DOTATooltipFriendsMenu #Contents
{
	background-color: #333f3d;
}

DOTATooltipFriendsMenu #BottomArrow
{
	wash-color: #333f3d;
}

DOTATooltipCustomGame #Contents
{
	padding: 0px;

	border: 1px solid #9BCAF455;
}

DOTATooltipCustomGame #LeftArrow, DOTATooltipCustomGame #RightArrow, DOTATooltipCustomGame #TopArrow, DOTATooltipCustomGame #BottomArrow
{
}

DOTATooltipCustomGame #BottomArrow
{
	background-image: url("s2r://panorama/images/tooltips/tooltip_arrow_bottom_customgames_psd.vtex");
	wash-color: white;
}
DOTATooltipCustomGame #LeftArrow
{
	background-image: url("s2r://panorama/images/tooltips/tooltip_arrow_left_customgames_psd.vtex");
	wash-color: white;
}
DOTATooltipCustomGame #RightArrow
{
	background-image: url("s2r://panorama/images/tooltips/tooltip_arrow_right_customgames_psd.vtex");
	wash-color: white;
}
DOTATooltipCustomGame #TopArrow
{
	background-image: url("s2r://panorama/images/tooltips/tooltip_arrow_top_customgames_psd.vtex");
	wash-color: white;
}

/* Custom styling for Profile Card tooltip */
DOTATooltipProfileCard #Contents
{
	background-color: transparent;
	padding: 0px;
	border: 0px solid transparent;
	box-shadow: none;
	transition-delay: 0.2s;
}

DOTATooltipProfileCard.TooltipVisible
{
	transition-delay: 0.0s;
}

/* Use margins on the arrows to make sure that we don't try to show the arrow over top of the badge */
DOTATooltipProfileCard #RightArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_right_png.vtex");
	wash-color: #495757;
	width: 16px;
	height: 32px;

	margin-left: -1px;
}

DOTATooltipProfileCard #TopArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_top_png.vtex");
	wash-color: #495757;

	width: 32px;
	height: 16px;

	margin-bottom: -1px;
}

DOTATooltipProfileCard #LeftArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_left_png.vtex");
	wash-color: #495757;
	width: 16px;
	height: 32px;

	margin-right: -1px;
}

DOTATooltipProfileCard #BottomArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_bottom_png.vtex");
	wash-color: #495757;

	width: 32px;
	height: 16px;

	margin-top: -1px;
}

.BindingsTooltip #Contents
{
	min-width: 576px;
	max-width: 576px;
	background-color: #393E3F;
	border: 2px solid black;
}

.BindingsTooltip .TooltipRow
{
	width: 450px;
}

.BindingsTooltip #LeftArrow,
.BindingsTooltip #TopArrow
{
	opacity: 1;
	height: 10px;
	wash-color: #2B2F30;
}

.BindingsTooltip .TextContents
{
	width: 100%;
	transform: translateX(5px);
}

.BindingsTooltip.TooltipContainer
{
	opacity: 0;
	transition-property: opacity;
	transition-duration: 0.1s;
	margin-top: -2px;
	margin-left: 8px;
}

.BindingsTooltip.TooltipContainer.TooltipVisible
{
	opacity: 1;
}

.OptionsTooltip #Contents
{
	min-width: 576px;
	max-width: 576px;
	background-color: #393E3F;
	border: 2px solid black;
}

.OptionsTooltip .TooltipRow
{
	width: 450px;
}

.OptionsTooltip #LeftArrow
{
	opacity: 1;
	height: 10px;
	wash-color: #2B2F30;
	margin: 0px;
	width: fit-children;
}

.RightArrowVisible.OptionsTooltip #RightArrow
{
	visibility: visible;
}

.OptionsTooltip .TextContents
{
	width: 100%;
	transform: translateX(5px);
}

.OptionsTooltip.TooltipContainer
{
	opacity: 0;
	transition-property: opacity;
	transition-duration: 0.1s;
	margin-top: -2px;
	margin-left: 8px;
}

.OptionsTooltip.TooltipContainer.TooltipVisible
{
	opacity: 1;
}

.LeaverConsequencesTooltip #Contents
{
	width: 330px;
	background-color: #69312c;
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #2d2d2d ), color-stop(.5, #2d2d2d), to( #69312c ) );
}

.LeaverConsequencesTooltip #TextLabel
{
	color: red;
}

.LeaverConsequencesTooltip #BottomArrow,
{
	wash-color: #69312c;
}

#SeasonPassRewardPreview.TooltipContainer
{
	pre-transform-scale2d: .3;

	transition-property: opacity, pre-transform-scale2d;
	transition-timing-function: ease-in-out;
	//transition-delay: .3s;


	animation-duration: 0.61s;
	animation-timing-function: ease-in-out;
	animation-iteration-count: 1;
}

@keyframes 'BubbleUp'
{
	0%
	{
		pre-transform-scale2d: .2;
		opacity: 0;
	}

	50%
	{
		pre-transform-scale2d: 1.1;
		opacity: 1;
	}

	100%
	{
		pre-transform-scale2d: 1;
		opacity: 1;
	}
}

#SeasonPassRewardPreview.TooltipContainer.TooltipVisible
{
	animation-name: BubbleUp;
	transition-delay: 0.0s;
	pre-transform-scale2d: 1;
	transition-duration: 0.175s, 0.12s;
	animation-delay: 0.0s;
}

/* Context menu properties moved from context_menu_base.css */

DOTAContextMenuPlayer #RightArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_right_png.vtex");
	wash-color: #495757;

	width: 16px;
	height: 32px;

	margin-left: -1px;
}

DOTAContextMenuPlayer #TopArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_top_png.vtex");
	wash-color: #495757;

	width: 32px;
	height: 16px;

	margin-bottom: -1px;
}

DOTAContextMenuPlayer #LeftArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_left_png.vtex");
	wash-color: #495757;

	width: 16px;
	height: 32px;

	margin-right: -1px;
}

DOTAContextMenuPlayer #BottomArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_bottom_png.vtex");
	wash-color: #495757;

	width: 32px;
	height: 16px;

	margin-top: -1px;
}

.BattlePassActivityTooltip #LeftArrow
{
	background-image: url("s2r://panorama/images/tooltips/tooltip_arrow_left_ti9_png.vtex");
	wash-color: white;
	width: 16px;
	height: 24px;
}
.BattlePassActivityTooltip #RightArrow
{
	background-image: url("s2r://panorama/images/tooltips/tooltip_arrow_right_ti9_png.vtex");
	wash-color: white;
	width: 16px;
	height: 24px;
}
.BattlePassActivityTooltip #BottomArrow
{
	background-image: url("s2r://panorama/images/tooltips/tooltip_arrow_bottom_ti9_png.vtex");
	wash-color: white;
	width: 24px;
	height: 16px;
	margin-top: -1px;
}
.BattlePassActivityTooltip #TopArrow
{
	background-image: url("s2r://panorama/images/tooltips/tooltip_arrow_top_ti9_png.vtex");
	wash-color: white;
	width: 24px;
	height: 16px;
}

DOTATooltipChatBubble.TooltipContainer.BottomArrowVisible #BottomArrow,
DOTATooltipChatBubble.TooltipContainer.TopArrowVisible #TopArrow,
DOTATooltipChatBubble.TooltipContainer.RightArrowVisible #RightArrow,
DOTATooltipChatBubble.TooltipContainer.LeftArrowVisible #LeftArrow
{
	wash-color: #ffffff;
}

DOTATooltipChatBubble.TooltipContainer.BottomArrowVisible #BottomArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_bottom_png.vtex");
}
DOTATooltipChatBubble.TooltipContainer.TopArrowVisible #TopArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_top_png.vtex");
}
DOTATooltipChatBubble.TooltipContainer.RightArrowVisible #RightArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_right_png.vtex");
}
DOTATooltipChatBubble.TooltipContainer.LeftArrowVisible #LeftArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_left_png.vtex");
}

DOTATooltipDroppedItem
{
	tooltip-position: top;
	tooltip-body-position: 50% 50%;
}

DOTATooltipRune
{
	tooltip-position: bottom;
}

/* Custom styling for customize profile card arrow */
.ContextMenuGrayArrow #RightArrow, .ContextMenuGrayArrow #LeftArrow
{
	width: 16px;
	height: 32px;
}
.ContextMenuGrayArrow #TopArrow, .ContextMenuGrayArrow #BottomArrow
{
	width: 32px;
	height: 16px;
}

.ContextMenuGrayArrow #RightArrow, .ContextMenuGrayArrow #LeftArrow, .ContextMenuGrayArrow #TopArrow, .ContextMenuGrayArrow #BottomArrow
{
	wash-color: #495757;
}

.ContextMenuGrayArrow #RightArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_right_png.vtex");
	margin-left: 0px;
}
.ContextMenuGrayArrow #TopArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_top_png.vtex");
	margin-bottom: 0px;
}
.ContextMenuGrayArrow #LeftArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_left_png.vtex");
	margin-right: 0px;
}
.ContextMenuGrayArrow #BottomArrow
{
	background-image: url("s2r://panorama/images/tooltips/profilecard_tooltip_arrow_bottom_png.vtex");
	margin-top: 0px;
}

.ContextMenuBlackArrow #RightArrow
{
	background-image: url("s2r://panorama/images/tooltips/black_tooltip_arrow_right_png.vtex");
	height: 27px;
	width: 14px;
	margin-left: -1px;
}
.ContextMenuBlackArrow #TopArrow
{
	background-image: url("s2r://panorama/images/tooltips/black_tooltip_arrow_top_png.vtex");
	height: 14px;
	width: 27px;
	margin-bottom: -1px;
}
.ContextMenuBlackArrow #LeftArrow
{
	background-image: url("s2r://panorama/images/tooltips/black_tooltip_arrow_left_png.vtex");
	height: 27px;
	width: 14px;
	margin-bottom: -1px;
}
.ContextMenuBlackArrow #BottomArrow
{
	background-image: url("s2r://panorama/images/tooltips/black_tooltip_arrow_bottom_png.vtex");
	height: 14px;
	width: 27px;
	margin-top: -1px;
}


/* TournamentMatchDetails custom styling */
TournamentMatchDetails #Contents
{
	padding: 0px;
	border: 0px;
	border-top: 1px solid #384041;
	border-bottom: 1px solid #1e2424;
	background-color: #2a3334;
}

/* CenterBuffer for Alignment transitions*/

.CenterBuffer
{
	width: fill-parent-flow( 1.0 );
	//background-color: #ff000022;
	height: 100%;
	transition-property: width;
	transition-timing-function: ease-in-out;
	transition-duration: 0.5s;
}


/* -- NumberEntry ---------------------------------------------------------- */

NumberEntry:not(.CustomStyling)
{
	flow-children: right;
	width: 100px;
	height: 36px;
}

NumberEntry:not(.CustomStyling) #TextEntry
{
	width: fill-parent-flow( 1.0 );
	height: 100%;
}

NumberEntry:not(.CustomStyling) .ChangeValueButtons
{
	flow-children: down;
	margin-left: 2px;
}

NumberEntry:not(.CustomStyling) .ChangeValueButton
{
	width: 17px;
	height: 17px;

	border: 1px solid baseBorder;
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #111111FF ), to( #222222FF ) );
}

NumberEntry:not(.CustomStyling) .ChangeValueButtonImage
{
	visibility: collapse;
}

NumberEntry:not(.CustomStyling) .ChangeValueButtonLabel
{
	text-align: center;
	vertical-align: middle;
	horizontal-align: center;
	margin-top: -4px;
}

NumberEntry:not(.CustomStyling) .ChangeValueButton:disabled
{
	brightness: 0.5;
}

NumberEntry:not(.CustomStyling) .ChangeValueButton:hover:enabled
{
	brightness: 2.0;
}

NumberEntry:not(.CustomStyling) .ChangeValueButton:active
{
	brightness: 1.5;
}

NumberEntry:not(.CustomStyling) .ChangeValueButton__Increment
{
	margin-bottom: 2px;
}

/* -- NumberEntry.DiretidePurchase ----------------------------------------- */
NumberEntry.CustomStyling.DiretidePurchase
{
	height: 100%;
	width: 100%;
}

NumberEntry.CustomStyling.DiretidePurchase #TextEntry
{
	visibility:collapse;
}

NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButtons
{
	width: 100%;
	height: 100%;
	flow-children: down;
	padding-top: 1px;
	padding-bottom: 1px;
}

NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton
{
	height: 23px;
	width: 100%;
	background-color: #34AD3930;
	background-color: gradient( linear, 100% 0%, 0% 0%, from( #34AD3930 ), to( #34AD3940 ) );
	transition-duration: .2s;
	transition-property: background-color;
}
NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton__Decrement
{
	height: fill-parent-flow(1.0);
}

.PriorityButton NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton
{
	background-image: url("s2r://panorama/images/econ/diretide/chest_button_texture_priority_increment_psd.vtex");
	//opacity-mask: url("s2r://panorama/images/masks/softedge_box_png.vtex");
	//background-color: gradient( linear, 100% 0%, 0% 0%, from( #24FF4A ), to( #4CD138 ) );
	border:0px;
	
	transition-property: background-image;
	transition-delay: .2s;
}
NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton__Increment
{
	border-bottom: 1px solid #3B8130;
	margin-top: 1px;
	height: 22px;
}
.PriorityButton NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton__Increment
{
	border-bottom: 1px solid #AFCE89;
	transition-property: background-image;
	transition-delay: .2s;
}
NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton__Decrement
{
	background-color:#34AD3930;
	margin-bottom: 1px;
}

NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton:enabled:hover
{
	background-color: gradient( linear, 100% 0%, 0% 0%, from( #34AD3988 ), to( #34AD3999 ) );
}

.PriorityButton NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton:enabled:hover
{
	background-image: url("s2r://panorama/images/econ/diretide/chest_button_texture_priority_increment_on_psd.vtex");
	transition-property: background-image;
	transition-delay: .2s;
}

NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton:active
{
	brightness: 1.5;
}

NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButtonLabel
{
	visibility:collapse;
}

NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton .ChangeValueButtonImage
{
	height: 100%;
	width: 100%;
	
	background-size: 40% 40%;
	background-repeat: no-repeat no-repeat;
	background-position: center;
	
	background-color: none;
	wash-color: #E8FFC2;
	
	opacity-mask: url("s2r://panorama/images/masks/softedge_box_png.vtex");
}

NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton__Increment .ChangeValueButtonImage
{
	background-image: url("s2r://panorama/images/control_icons/arrow_solid_up_png.vtex");
}
NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton__Decrement .ChangeValueButtonImage
{
	background-image: url("s2r://panorama/images/control_icons/arrow_solid_down_png.vtex");
}

.PriorityButton NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton .ChangeValueButtonImage
{
	wash-color: white;
}

NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton:disabled .ChangeValueButtonImage
{
	wash-color: #042F08;
}
.PriorityButton NumberEntry.CustomStyling.DiretidePurchase .ChangeValueButton:disabled .ChangeValueButtonImage
{
	/* ~30% brighter */
	wash-color: #09610A;
}


/* ------------------------------------------------------------------------- */

/*
DOTAScenePanel
{
	opacity: 0;
	transition-property: opacity, pre-transform-scale2d, transform, wash-color;
	transition-duration: 0.5s;
	transition-timing-function: ease-in-out;
}

DOTAScenePanel.SceneLoaded
{
	opacity: 1;
}
*/

DOTAItemImage
{
	background-image: url("s2r://panorama/images/items/emptyitembg_png.vtex");
	background-repeat: no-repeat;
	background-size: 100% 99%;

}

DOTAItemImage #RecipeOutputImage
{
	opacity: 1;
	saturation: 0.25;
	brightness: 2.0;
	pre-transform-scale2d: .7;
	transform: rotateZ(-8deg) rotateY(30deg) translateX(2px) translateY(1px);
	border-radius: 1px;
	opacity-mask: url("s2r://panorama/images/masks/softedge_box_png.vtex");
}

#RecipeContainer
{
	wash-color: #d8984bff;
	width: 100%;
	height: 100%;
	contrast: .9;
	background-color: black;
	opacity-mask: url("s2r://panorama/images/items/recipe_opacity_mask_nopurchase_psd.vtex");
}

DOTAShopItem .CanPurchase #RecipeContainer,
DOTAShopItem .AvailableAtOtherShop #RecipeContainer
{
	opacity-mask: url("s2r://panorama/images/items/recipe_opacity_mask_nopurchase_psd.vtex");
}

#dragImage
{
	box-shadow: fill #000000cc 4px 4px 8px 8px;
	border-radius: 4px;
	border: 1px solid #343434;
	transform: translateX(6px) translateY(6px);
}

@keyframes 'StunPortrait'
{
	0%
	{
		transform: translateX(-24px);

	}

	10%
	{
		transform: translateX(24px);

	}

	20%
	{
		transform: translateX(-18px);

	}

	30%
	{
		transform: translateX(18px);

	}

	40%
	{
		transform: translateX(-12px);

	}
	50%
	{
		transform: translateX(12px);

	}
	60%
	{
		transform: translateX(-6px);

	}
	70%
	{
		transform: translateX(6px);

	}
	80%
	{
		transform: translateX(-4px);

	}
	90%
	{
		transform: translateX(4px);

	}
	100%
	{
		transform: translateX(0px);

	}
}



@keyframes 'StunPortraitReverse'
{
	0%
	{
		transform: translateX(24px);

	}

	10%
	{
		transform: translateX(-24px);

	}

	20%
	{
		transform: translateX(18px);

	}

	30%
	{
		transform: translateX(-18px);

	}

	40%
	{
		transform: translateX(12px);

	}
	50%
	{
		transform: translateX(-12px);

	}
	60%
	{
		transform: translateX(6px);

	}
	70%
	{
		transform: translateX(-6px);

	}
	80%
	{
		transform: translateX(4px);

	}
	90%
	{
		transform: translateX(-4px);

	}
	100%
	{
		transform: translateX(0px);

	}
}

.ComingSoonSticker
{
	background-color: #0FB678dd;
	font-size: 16px;
	padding: 2px 8px 2px 8px;
	border-radius: 4px;
	border: 1px solid #8f7;
	text-transform: uppercase;
	color: #8f7;
	letter-spacing: 1px;
	text-shadow: 1px 1px 0px 2 #000000;
	box-shadow: #000000 -2px -2px 4px 4px;

	transition-property: background-color, border, opacity;
	transition-duration: 0.2s;
	transition-timing-function:ease-in-out;
}


.StatBranch
{
	background-image: url("s2r://panorama/images/spellicons/statbranch_psd.vtex");
	background-size: 100% 100%;
	background-repeat: no-repeat;
}

.ScepterDetails
{
	background-image: url("s2r://panorama/images/spellicons/aghsicon_psd.vtex");
	background-size: 100% 100%;
	background-repeat: no-repeat;
}

.GradientTopBottom
{
	width: 100%;
	height: 100%;
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #000000aa ), color-stop( .10, #00000000), color-stop( .90, #00000000), to( #00000088 ) );
}

#CourierContextMenu #LeftArrow, #CourierContextMenu #RightArrow,
{
	wash-color: black;
}

.DevLabel
{
	color: #0f0;
	text-shadow: 1px 1px 0px 2.0 #000000;
	font-size: 14px;
	width: 300px;
	background-color: #000c;
}

.InspectIcon
{
	background-image: url("s2r://panorama/images/control_icons/icon_search_png.vtex");
	background-size: cover;
	background-repeat: no-repeat;
	background-position: 50% 50%;

	width: 32px;
	height: 32px;
	vertical-align: middle;
	horizontal-align: center;
	margin: 6px;
	opacity: 1;

	transition-property: opacity, pre-transform-scale2d;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
}

.MainBranchOnly
{
	visibility: collapse;
}
.MainBranch .MainBranchOnly
{
	visibility: visible;
}

.EventPointsValueIcon
{
	width: 15px;
	height: 15px;
	margin-right: 4px;

	transform: translateY( -2px );

	background-size: contain;
	background-position: center;
	background-repeat: no-repeat;
}

.EventPointsValueIcon.Season_PlusSubscription	{ background-image: dotaPlusCurrencySmallIcon; }
.EventPointsValue.Season_PlusSubscription		{ color: dotaPlusLightGold; }

.RankTierImage {}
.RankTier0 .RankTierImage { background-image: url("s2r://panorama/images/rank_tier_icons/rank0_psd.vtex"); opacity: 0.85; }
.RankTier1 .RankTierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank1_psd.vtex"); }
.RankTier2 .RankTierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank2_psd.vtex"); }
.RankTier3 .RankTierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank3_psd.vtex"); }
.RankTier4 .RankTierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank4_psd.vtex"); }
.RankTier5 .RankTierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank5_psd.vtex"); }
.RankTier6 .RankTierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank6_psd.vtex"); }
.RankTier7 .RankTierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank7_psd.vtex"); }
.RankTier8 .RankTierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank8_psd.vtex"); }
.RankTier8.LeaderboardTop100 .RankTierImage	{ background-image: url("s2r://panorama/images/rank_tier_icons/rank8b_psd.vtex"); }
.RankTier8.LeaderboardTop10 .RankTierImage	{ background-image: url("s2r://panorama/images/rank_tier_icons/rank8c_psd.vtex"); }
.RankTier8.LeaderboardTop1 .RankTierImage	{ background-image: url("s2r://panorama/images/rank_tier_icons/rank8c_psd.vtex"); }

.RankTier0TierImage { background-image: url("s2r://panorama/images/rank_tier_icons/rank0_psd.vtex"); opacity: 0.85; }
.RankTier1TierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank1_psd.vtex"); }
.RankTier2TierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank2_psd.vtex"); }
.RankTier3TierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank3_psd.vtex"); }
.RankTier4TierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank4_psd.vtex"); }
.RankTier5TierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank5_psd.vtex"); }
.RankTier6TierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank6_psd.vtex"); }
.RankTier7TierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank7_psd.vtex"); }
.RankTier8TierImage {	background-image: url("s2r://panorama/images/rank_tier_icons/rank8_psd.vtex"); }

.RankPipImage { }
.RankPips0 .RankPipImage {  visibility: collapse; /*background-image: url("s2r://panorama/images/rank_tier_icons/pip_empty_psd.vtex"); opacity: 0.5;*/ }
.RankPips1 .RankPipImage {	background-image: url("s2r://panorama/images/rank_tier_icons/pip1_psd.vtex"); }
.RankPips2 .RankPipImage {	background-image: url("s2r://panorama/images/rank_tier_icons/pip2_psd.vtex"); }
.RankPips3 .RankPipImage {	background-image: url("s2r://panorama/images/rank_tier_icons/pip3_psd.vtex"); }
.RankPips4 .RankPipImage {	background-image: url("s2r://panorama/images/rank_tier_icons/pip4_psd.vtex"); }
.RankPips5 .RankPipImage {	background-image: url("s2r://panorama/images/rank_tier_icons/pip5_psd.vtex"); }
.RankPips6 .RankPipImage {	background-image: url("s2r://panorama/images/rank_tier_icons/pip6_psd.vtex"); }
.RankPips7 .RankPipImage {	background-image: url("s2r://panorama/images/rank_tier_icons/pip7_psd.vtex"); }



.RankDataMissing #EliteFX, .RankTier0 #EliteFX, .RankTier1 #EliteFX, .RankTier2 #EliteFX, .RankTier3 #EliteFX, .RankTier4 #EliteFX, .RankTier5 #EliteFX, .RankTier6 #EliteFX, .RankTier7 #EliteFX
{
	visibility: collapse;
}

.RankTier8.LeaderboardTop10 #EliteFX, .RankTier8.LeaderboardTop1 #EliteFX
{
	visibility: visible;
}


/* Hero Movies are rendered at 283x375, but then saved into a 256x256 movie. This size is a sane default that has the correct aspect ratio. */
DOTAHeroMovie
{
	width: 193px;
	height: 256px;
}



/*  Frostivus 2017 */

.Frostivus2017 #GuidesTabContents .Body,
.Frostivus2017 DOTAWatchDownloadsElement,
.Frostivus2017 .FeaturedPageCell,
.Frostivus2017 #SearchResultsContainer,
.Frostivus2017 #DOTAWatchReplaysPage .RowContainer,
.Frostivus2017 #WatchLiveGameListLeft,
.Frostivus2017 #FriendMenu,
.Frostivus2017 #CategoriesOptionsContainer,
.Frostivus2017 #DOTAWatchTournamentsPage,
.Frostivus2017 #LeaguePassGrid,
.Frostivus2017 #ProfileContentRightContents,
.Frostivus2017 .ConductSummaryAvailable #PlayStyleBox,
.Frostivus2017 #ConductBox,
.Frostivus2017 #PatchContents,
.Frostivus2017 .ChallengeContent,
.Frostivus2017 .StatusAndFriendFeed,
.Frostivus2017 DOTACustomGamesSubscribedPage,
.Frostivus2017 DOTACustomLobbyList,
.Frostivus2017 DOTAParty,
.Frostivus2017 #GamesWithLobbies,
.Frostivus2017 .WorkshopVoteBox,
.Frostivus2017 #ItemDetailsContents,
.Frostivus2017 .GameOfTheDayBox,
.Frostivus2017 #TopGamesCarousel,
.Frostivus2017 .ResultsBox,
.Frostivus2017 #FriendsButton,
.EmbedChatInPage.Frostivus2017 #ParticipantArea,
.Frostivus2017 #ParticipantArea,
.Frostivus2017.EmbedChatInPage DOTAChat.ChatExpanded #ChatLinesContainer,
.Frostivus2017 DOTAChat.ChatExpanded #ChatLinesContainer
{
	background-color: #001122f1;
}

.Frostivus2017 #HeroFilters,
.Frostivus2017 #Chat:not(.ChatExpanded) .ChatLine
{
	brightness: 2;
}

.Frostivus2017 #BackgroundVignette, .Vignette
{
	opacity: 1;
	background-image: none;
	background-color: gradient( radial, 50% 55%, 0% 0%, 70% 70%, from( #161008ee ), color-stop( .2,  #161008e2 ), color-stop( .4,  #161008dd ),to( #16100800 ) );

}

.Frostivus2017 #TopBarSecondaryTabs
{
	background-color: gradient( linear, 0% 0%, 100% 0%, from( #001133dd ), color-stop( 0.8, #001133cc ),  to( #00000000 ) );
}

.Frostivus2017 #TopBarSearchBox
{
	background-color: #000c;
}

.Frostivus2017 #ArmoryPageContainer
{
	background-color: gradient( linear, 90% 0%, 100% 0%, from( #001133ee ), to( #001133aa ) );
	margin-left: 0px;
	padding-left: 2px;
}

.Frostivus2017 .HeroGuidePicker,
.Frostivus2017 .HeroPageTab Label
{
	color: #ccc;
}


.Frostivus2017 .HeroPageContent
{
	background-color: gradient( radial, 50% 60%, 0% 0%, 70% 80%, from( #001133aa ), color-stop( .3,  #001133a2 ), color-stop( .6,  #00113388 ),to( #00113355 ) );
}

.Frostivus2017 #HeroGridPanel
{
	//background-color: #00113388;
	background-color: gradient( radial, 50% 60%, 0% 0%, 70% 80%, from( #001133aa ), color-stop( .3,  #001133a2 ), color-stop( .6,  #001133aa ),to( #00113355 ) );
}


.Frostivus2017 #StatLine Label.StatLineNumber,
.Frostivus2017 #StatLine Label
{
	color: #aCc4f8;
}

.Frostivus2017 DOTAPostGame
{
	padding-top: 150px;
	margin-top: 0px;
	height: 100%;
	background-color: #000d;
}

.Frostivus2017 #ContainerCenter
{
	background-color: #00113300;
	vertical-align: top;
}

.Frostivus2017 #PinnedHeroes
{
	vertical-align: top;
	margin-top: 30px;
}


.Frostivus2017 .ProfileContentRightTabOption Label
{
	color: #cde;
}

.Frostivus2017 #DOTAWatchDownloadsPage #Header
{
	background-color: #001133cc;
}
.Frostivus2017 #TreasuryControls
{
	background-color: #001133ee;
	padding: 4px;
	padding-left: 8px;
}

.Frostivus2017 DOTATreasureDetailsPage
{
	background-color: #000000cc;
}

.Frostivus2017 Label#TournamentDetails
{
	color: #bbb;
}

.Frostivus2017 .HeroCategoryTitle.Showing
{
    opacity: 1;
	wash-color: #D0D6F7;
}


.Frostivus2017 .SecondaryTabButton Label
{
	color:#bbc;
	text-shadow: 1px 1px 2px 2 #0005;
}

.Frostivus2017 .SecondaryTabButton:selected Label
{
	color:white;
}


/* Dota Plus */

.DotaPlusHeadline1
{
  font-size: 48px;
  font-weight: semibold;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.DotaPlusHeadline2
{
  font-size: 30px;
  font-weight: bold;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.DotaPlusHeadline3
{
  font-size: 24px;
  font-weight: semibold;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.DotaPlusHeadline4
{
  font-size: 21px;
  font-weight: bold;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.DotaPlusBody
{
  font-size: 21px;
  font-weight: normal;
}

.DotaPlusHorizonalRule
{
  height: 2px;
  background-color: dotaPlusGold;
}

.DotaPlusContainer
{
	border-top: 2px solid dotaPlusGold;
	box-shadow: #00000055 -2px -2px 1px 4px;
	background-color: dotaPlusBgGradient;
}

.ShardCurrencyContainer
{
	border-top: 2px solid #C7E7E650;
	box-shadow: #00000055 -2px -2px 1px 4px;
	background-color: dotaShardCurrencyBgGradient;
}

.DotaPlusIconLarge		{ background-image: dotaPlusLogo; }
.DotaPlusIconSmall		{ background-image: dotaPlusLogoSmall; }
.DotaPlusChallangeIcon	{ background-image: dotaPlusChallengeIcon; }
.DotaPlusChatWheelIcon	{ background-image: dotaPlusChatWheelIcon; }
.DotaPlusRelicIcon		{ background-image: dotaPlusRelicIcon; }
.DotaPlusCurrencyIcon	{ background-image: dotaPlusCurrencyIcon; }

.DotaPlusBrightButton
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #e0c59a ), to( #ae9249 ) );
	padding: 6px 16px 6px 16px;
}
.DotaPlusBrightButton:hover:enabled
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #f7d9aa ), to( #d1af58 ) );
}
.DotaPlusBrightButton:active
{
	background-color: gradient( linear, 0% 100%, 0% 0%, from( #f7d9aa ), to( #d1af58 ) );
}
.DotaPlusBrightButton:disabled
{
	saturation: 0.0;
	brightness: 0.15;
}
.DotaPlusBrightButton Label
{
	color: white;
	text-transform: uppercase;
	text-align: center;
	horizontal-align: center;
}

.DotaPlusDarkButton
{
	border-top: 1px solid #ffffff22;
	border-left: 1px solid #ffffff11;
	border-right: 1px solid #00000044;
	border-bottom: 1px solid #00000088;

    background-color: gradient( linear, 0% 0%, 0% 100%, from( #433620 ), to( #141b21 ) );

	padding: 8px 16px 4px 16px;
}
.DotaPlusDarkButton:hover:enabled
{
	brightness: 2.0;
}
.DotaPlusDarkButton:active
{
	brightness: 1.5;
}
.DotaPlusDarkButton:disabled
{
	saturation: 0.0;
	wash-color: #555;
}
.DotaPlusBrightButton Label
{
	color: white;
	text-transform: uppercase;
	text-align: center;
	horizontal-align: center;
}
.DotaPlusDarkButton Label
{
	color: dotaPlusLightGold;
	text-transform: uppercase;
	text-align: center;
	horizontal-align: center;
}

.DotaPlusGoldButton
{
	background-color: dotaPlusGoldGradient;
	border-top: 1px solid #DCD08680;
}
.DotaPlusGoldButton:hover:enabled
{
	background-color: dotaPlusGoldGradientHover;
}
.DotaPlusGoldButton:active
{
	background-color: dotaPlusGoldGradientActive;
}

.DotaPlusGoldButton Label
{
	text-align: center;
	color: white;
	text-transform: uppercase;
	padding: 8px 12px;
	font-size: 20px;
	letter-spacing: 2px;
	font-weight: bold;
	horizontal-align: center;
	text-shadow: 1px 1px 4px black;
}


.RequiresPlusForVisibility
{
	visibility: collapse;
}
.PlusSubscriber .RequiresPlusForVisibility
{
	visibility: visible;
}

.PlusHeroBadgeIcon
{
	background-repeat: no-repeat;
	background-size: contain;
	background-position: center;
}
.BronzeTier		.PlusHeroBadgeIcon, .BronzeTier.PlusHeroBadgeIcon	{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_0_png.vtex"); }
.SilverTier		.PlusHeroBadgeIcon, .SilverTier.PlusHeroBadgeIcon	{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_1_png.vtex"); }
.GoldTier		.PlusHeroBadgeIcon, .GoldTier.PlusHeroBadgeIcon		{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_2_png.vtex"); }
.PlatinumTier	.PlusHeroBadgeIcon, .PlatinumTier.PlusHeroBadgeIcon { background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_3_png.vtex"); }
.MasterTier		.PlusHeroBadgeIcon, .MasterTier.PlusHeroBadgeIcon	{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_4_png.vtex"); }

.PlusHeroBadgeIconSmall
{
	background-repeat: no-repeat;
	background-size: contain;
	background-position: center;
}
.BronzeTier		.PlusHeroBadgeIconSmall, .BronzeTier.PlusHeroBadgeIconSmall		{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_0_small_png.vtex"); }
.SilverTier		.PlusHeroBadgeIconSmall, .SilverTier.PlusHeroBadgeIconSmall		{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_1_small_png.vtex"); }
.GoldTier		.PlusHeroBadgeIconSmall, .GoldTier.PlusHeroBadgeIconSmall		{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_2_small_png.vtex"); }
.PlatinumTier	.PlusHeroBadgeIconSmall, .PlatinumTier.PlusHeroBadgeIconSmall	{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_3_small_png.vtex"); }
.MasterTier		.PlusHeroBadgeIconSmall, .MasterTier.PlusHeroBadgeIconSmall		{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_4_small_png.vtex"); }

.PlusHeroBadgeIconTiny
{
	background-repeat: no-repeat;
	background-size: contain;
	background-position: center;
}
.BronzeTier		.PlusHeroBadgeIconTiny, .BronzeTier.PlusHeroBadgeIconTiny		{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_0_tiny_png.vtex"); }
.SilverTier		.PlusHeroBadgeIconTiny, .SilverTier.PlusHeroBadgeIconTiny		{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_1_tiny_png.vtex"); }
.GoldTier		.PlusHeroBadgeIconTiny, .GoldTier.PlusHeroBadgeIconTiny			{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_2_tiny_png.vtex"); }
.PlatinumTier	.PlusHeroBadgeIconTiny, .PlatinumTier.PlusHeroBadgeIconTiny		{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_3_tiny_png.vtex"); }
.MasterTier		.PlusHeroBadgeIconTiny, .MasterTier.PlusHeroBadgeIconTiny		{ background-image: url("s2r://panorama/images/hero_badges/hero_badge_rank_4_tiny_png.vtex"); }

/* PROGRESS BARS */
.BronzeTier		.ProgressBarLeft		{ background-color: gradient( linear, 0% 0%, 100% 0%, from( #97472A ), to( #CF9F68 ) ); }
.SilverTier		.ProgressBarLeft		{ background-color: gradient( linear, 0% 0%, 100% 0%, from( #59748A ), to( #C8E9FF ) ); }
.GoldTier		.ProgressBarLeft		{ background-color: gradient( linear, 0% 0%, 100% 0%, from( #ae9249 ), to( #e0c59a ) ); }
.PlatinumTier	.ProgressBarLeft		{ background-color: gradient( linear, 0% 0%, 100% 0%, from( #4867C5 ), to( #85D4EF ) ); }
.MasterTier		.ProgressBarLeft		{ background-color: gradient( linear, 0% 0%, 100% 0%, from( #48165E ), to( #918BD0 ) ); }

@define PhysicalDamageColor: #ae2f28;
@define MagicalDamageColor: #5b93d1;
@define PureDamageColor: #d8ae53;

.PhysicalDamageLabel
{
	color: PhysicalDamageColor;
	font-weight: bold;
}
.MagicalDamageLabel
{
	color: MagicalDamageColor;
	font-weight: bold;
}
.PureDamageLabel
{
	color: PureDamageColor;
	font-weight: bold;
}

.DotaPlusUpsell
{
	width: 100%;
	height: 100%;
	margin-top: 12px;
	flow-children: down;
}

.DotaPlusUpsellTitle
{
	margin-top: 24px;
	font-size: 32px;
	color: white;
	horizontal-align: center;
	text-align: center;
	text-transform: uppercase;
}


.DotaPlusUpsellFeatures
{
	margin-top: 18px;
	padding-left: 12px;
	flow-children: right;
	horizontal-align: center;
}

.DotaPlusUpsellFeature
{
	flow-children: down;
	margin-right: 12px;
	background-color: #0006;
}

.DotaPlusUpsellFeatureTitle
{
	width: 100%;
	font-size: 20px;
	color: #ccc;
	text-transform: uppercase;
	horizontal-align: center;
	text-align: center;
	background-color: black;
	padding: 5px 8px 1px 8px;
	letter-spacing: 1px;
	height: 33px;
	text-overflow: shrink;
}

.DotaPlusUpsellFeatureImage
{
	width: 246px;
	height: 164px;
}

.DotaPlusUpsellFeatureDescription
{
	padding: 8px;
	font-size: 16px;
	color: #999;
	width: 240px;
	height: 124px;
	text-overflow: shrink;
}

.DotaPlusSubscribeButton
{
	margin-top: 36px;
	horizontal-align: center;
}

.DotaPlusSubscribeButton Label
{
	font-size: 24px;
	font-weight: bold;
	color: white;
}

.DotaPlusLearnMoreButton
{
	margin-top: 12px;
	flow-children: right;
	horizontal-align: center;
}

.DotaPlusLearnMoreButton Label
{
	font-size: 24px;
	font-weight: bold;
	letter-spacing: 2px;
	color: #ba8e4a;
	text-transform: uppercase;
}

.DotaPlusLearnMoreButton .PopoutArrow
{
    width: 16px;
    height: 16px;
	horizontal-align: right;
	vertical-align: middle;
	margin-bottom: 4px;
	margin-left: 6px;

    background-image: url("s2r://panorama/images/control_icons/arrow_top_right_png.vtex");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    wash-color: #ba8e4a;
}

.DotaPlusLearnMoreButton:hover
{
	brightness: 2.0;
}
.DotaPlusLearnMoreButton:active
{
	brightness: 1.5;
}

.PlusSubscriber .DotaPlusUpsell
{
	visibility: collapse;
}


DOTATeamImage.ImageStyleDefault		{ width: 250px; height: 150px; }
DOTATeamImage.ImageStyleBaseLogo	{ width: 256px; height: 256px; }
DOTATeamImage.ImageStyleSponsorLogo { width: 250px; height: 150px; }
DOTATeamImage.ImageStyleBannerLogo	{ width: 256px; height: 256px; }

#TournamentSeriesDropdownDropDownMenu .NodeGroup
{
	color: white;
}

#TournamentSeriesDropdownDropDownMenu .Team
{
	font-weight: bold;
}


/* Ugly and hacky, but this should work */
#NewBloomGiftTooltip.TooltipContainer.LeftArrowVisible #LeftArrow,
#NewBloomGiftTooltip.TooltipContainer.RightArrowVisible #RightArrow,
#NewBloomGiftTooltip.TooltipContainer.TopArrowVisible #TopArrow,
#NewBloomGiftTooltip.TooltipContainer.BottomArrowVisible #BottomArrow
{
    visibility: collapse;
}


.PlayerSlot0 .PlayerColor, .PlayerSlot0.PlayerColor { color: HeroSlot0Color; }
.PlayerSlot1 .PlayerColor, .PlayerSlot1.PlayerColor { color: HeroSlot1Color; }
.PlayerSlot2 .PlayerColor, .PlayerSlot2.PlayerColor { color: HeroSlot2Color; }
.PlayerSlot3 .PlayerColor, .PlayerSlot3.PlayerColor { color: HeroSlot3Color; }
.PlayerSlot4 .PlayerColor, .PlayerSlot4.PlayerColor { color: HeroSlot4Color; }
.PlayerSlot5 .PlayerColor, .PlayerSlot5.PlayerColor { color: HeroSlot5Color; }
.PlayerSlot6 .PlayerColor, .PlayerSlot6.PlayerColor { color: HeroSlot6Color; }
.PlayerSlot7 .PlayerColor, .PlayerSlot7.PlayerColor { color: HeroSlot7Color; }
.PlayerSlot8 .PlayerColor, .PlayerSlot8.PlayerColor { color: HeroSlot8Color; }
.PlayerSlot9 .PlayerColor, .PlayerSlot9.PlayerColor { color: HeroSlot9Color; }


/* NEUTRAL ITEMS TEXT COLORS */

@define NeutralTier1: #BEBEBE;
@define NeutralTier2: #92E47E;
@define NeutralTier3: #7F93FC;
@define NeutralTier4: #D57BFF;
@define NeutralTier5: #FFE195;

/* AsyncDataPanel Stuff */
AsyncDataPanel > .AsyncContainer
{
	width: 100%;
	height: 100%;

	visibility: collapse;
}

AsyncDataPanel.AsyncFailureState > .AsyncFailureContainer,
AsyncDataPanel.AsyncLoadingState > .AsyncLoadingContainer,
AsyncDataPanel.AsyncSuccessState > .AsyncSuccessContainer
{
	visibility: visible;
}

AsyncDataPanel > .AsyncFailureContainer Label
{
	color: #999;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-size: 16px;
	vertical-align: middle;
	horizontal-align: center;
	text-align: center;
	padding: 0px 8px;
}
AsyncDataPanel > .AsyncLoadingContainer
{
	horizontal-align: center;
}

AsyncDataPanel > .AsyncLoadingContainer .Wrapper
{
	flow-children: down;
	horizontal-align: center;
	vertical-align: center;
}

AsyncDataPanel > .AsyncLoadingContainer .Spinner
{
	horizontal-align: center;
	wash-color: #999;
}

AsyncDataPanel > .AsyncLoadingContainer Label
{
	color: #999;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-size: 16px;
	horizontal-align: center;
	text-align: center;
	padding: 0px 8px;
	margin-top: 6px;
}

/* End AsyncDataPanel Stuff */

/* Default styling for challenge description values */
.ChallengeValue
{
	color: white;
}

/* Make sure guild images are opaque. */
DOTAGuildImage
{
	background-color: black;
}

a.PurchasePlusLink
{
	text-decoration: underline;
}
