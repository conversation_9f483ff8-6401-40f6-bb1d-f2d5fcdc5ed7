<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 110
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 249, 242, 161, 255 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_PositionLock_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_Orient2DRelToCP_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomSequence_0,
		&C_INIT_CreateInEpitrochoid_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\particle_swirl_04b.vtex"
	string m_Notes = ""
	int m_nOrientationType = 2
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float(3) m_Gravity = ( 0.000000, 0.000000, 500.000000 )
}

C_OP_PositionLock C_OP_PositionLock_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.500000
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.500000
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	int(4) m_ColorFade = ( 96, 176, 251, 255 )
	float m_flFadeStartTime = 0.500000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flStartScale = 8.000000
	float m_flEndScale = 0.000000
	float m_flBias = 0.750000
}

C_OP_Orient2DRelToCP C_OP_Orient2DRelToCP_0
{
	string m_Notes = ""
	float m_flRotOffset = 315.000000
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	string m_Notes = ""
	int m_nSequenceMax = 3
}

C_INIT_CreateInEpitrochoid C_INIT_CreateInEpitrochoid_0
{
	string m_Notes = ""
	bool m_bUseCount = true
	float m_flParticleDensity = 4.000000
	float m_flOffset = 92.000008
	float m_flRadius2 = -41.000000
	float m_flRadius1 = 62.000000
	int m_nComponent2 = 0
	int m_nComponent1 = 1
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMax = 0.600000
	float m_fLifetimeMin = 0.600000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMax = 16.000000
	float m_flRadiusMin = 16.000000
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 200.000000
}