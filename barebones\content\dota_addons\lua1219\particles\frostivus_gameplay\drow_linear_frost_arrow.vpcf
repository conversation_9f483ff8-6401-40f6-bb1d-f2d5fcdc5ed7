<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 16
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 240.000000
	int(4) m_ConstantColor = ( 207, 237, 255, 255 )
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0,
		&C_OP_RenderDeferredLight_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_RemapVelocityToVector_0,
		&C_OP_MovementPlaceOnGround_0,
		&C_OP_FadeInSimple_0,
		&C_OP_RampScalarLinear_0,
		&C_OP_SetControlPointsToParticle_0,
		&C_OP_Decay_0,
		&C_OP_BasicMovement_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_VelocityFromCP_0,
		&C_INIT_NormalAlignToCP_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/drow_frost_arrow_model.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/drow_ice_trail.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/drow_frost_arrow_launch.vpcf"
		},
		ParticleChildrenInfo_t
		{
			bool m_bEndCap = true
			string m_ChildRef = "particles/frostivus_gameplay/drow_frost_arrow_explosion.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	bool m_bDisableZBuffering = true
	string m_hTexture = "materials\\particle\\particle_flares\\aircraft_blue2.vtex"
	string m_Notes = ""
}

C_OP_RenderDeferredLight C_OP_RenderDeferredLight_0
{
	float m_flAlphaScale = 4.000000
	string m_Notes = ""
	string m_hTexture = "materials\\particle\\particle_flares\\aircraft_blue2.vtex"
}

C_OP_RemapVelocityToVector C_OP_RemapVelocityToVector_0
{
	int m_nFieldOutput = 21
	bool m_bNormalize = true
	string m_Notes = ""
}

C_OP_MovementPlaceOnGround C_OP_MovementPlaceOnGround_0
{
	int m_nRefCP1 = 3
	string m_CollisionGroupName = "DEBRIS"
	float m_flTraceOffset = 256.000000
	float m_flMaxTraceLength = 1024.000000
	bool m_bIncludeWater = true
	float m_flOffset = 70.000000
	float m_flTolerance = 96.000000
	int m_nLerpCP = 3
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 1.000000
	string m_Notes = ""
}

C_OP_RampScalarLinear C_OP_RampScalarLinear_0
{
	float m_RateMax = 2.000000
	float m_RateMin = 1.000000
	int m_nField = 4
	string m_Notes = ""
}

C_OP_SetControlPointsToParticle C_OP_SetControlPointsToParticle_0
{
	bool m_bSetOrientation = true
	int m_nFirstControlPoint = 3
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	int m_nOpEndCapState = 1
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_INIT_VelocityFromCP C_INIT_VelocityFromCP_0
{
	int m_nControlPoint = 1
	string m_Notes = ""
}

C_INIT_NormalAlignToCP C_INIT_NormalAlignToCP_0
{
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMin = ( 213, 240, 246, 255 )
	int(4) m_ColorMax = ( 213, 240, 246, 255 )
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMin = 0.200000
	float m_fLifetimeMax = 0.200000
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 1
	string m_Notes = ""
}