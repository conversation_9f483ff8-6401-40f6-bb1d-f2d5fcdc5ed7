<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 3
	string m_pszSnapshotName = ""
	float(3) m_BoundingBoxMin = ( -384.000000, -384.000000, -384.000000 )
	float(3) m_BoundingBoxMax = ( 384.000000, 384.000000, 384.000000 )
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 150.000000
	float m_flConstantLifespan = 2.000000
	int(4) m_ConstantColor = ( 120, 166, 254, 200 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderDeferredLight_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_SetToCP_0,
		&C_OP_FadeInSimple_0,
		&C_OP_LerpEndCapScalar_0,
		&C_OP_EndCapTimedDecay_0,
		&C_OP_OscillateScalar_0,
		&C_OP_ClampScalar_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderDeferredLight C_OP_RenderDeferredLight_0
{
	float m_flAlphaScale = 2.000000
	string m_Notes = ""
	float m_flStartFalloff = 0.100000
	string m_hTexture = "materials\\particle\\particle_light_mask_tower_good.vtex"
}

C_OP_SetToCP C_OP_SetToCP_0
{
	string m_Notes = ""
	float(3) m_vecOffset = ( 20.000000, 0.000000, 200.000000 )
	bool m_bOffsetLocal = true
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.500000
}

C_OP_LerpEndCapScalar C_OP_LerpEndCapScalar_0
{
	string m_Notes = ""
	float m_flLerpTime = 0.500000
	int m_nFieldOutput = 7
	float m_flOutput = 0.000000
}

C_OP_EndCapTimedDecay C_OP_EndCapTimedDecay_0
{
	string m_Notes = ""
	float m_flDecayTime = 0.500000
}

C_OP_OscillateScalar C_OP_OscillateScalar_0
{
	bool m_bDisableOperator = true
	string m_Notes = ""
	int m_nField = 16
	float m_RateMin = -1.000000
	float m_RateMax = 1.000000
	float m_FrequencyMin = 0.250000
}

C_OP_ClampScalar C_OP_ClampScalar_0
{
	bool m_bDisableOperator = true
	string m_Notes = ""
	int m_nFieldOutput = 16
	float m_flOutputMin = 0.500000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 1
}