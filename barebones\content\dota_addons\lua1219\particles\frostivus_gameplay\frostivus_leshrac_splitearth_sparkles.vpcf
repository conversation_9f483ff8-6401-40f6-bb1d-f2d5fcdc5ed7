<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 20
	int m_nInitialParticles = 4
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 214, 45, 45, 255 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_OscillateVector_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_AgeNoise_0,
		&C_INIT_CreateFromParentParticles_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_InitialVelocityNoise_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	bool m_bDisableZBuffering = true
	string m_hTexture = "materials\\particle\\particle_flares\\aircraft_blue2.vtex"
	float m_flAnimationRate = 2.000000
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float(3) m_Gravity = ( 0.000000, 0.000000, 600.000000 )
	float m_fDrag = 0.200000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.150000
	string m_Notes = ""
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	float m_flEndTime = 99999.000000
	float m_Rate = -30.000000
	int m_nOpEndCapState = 1
	string m_Notes = ""
}

C_OP_OscillateVector C_OP_OscillateVector_0
{
	float m_flOscMult = 1.000000
	float m_flStartTime_max = 0.500000
	float m_flStartTime_min = 0.250000
	float(3) m_FrequencyMax = ( 4.000000, 4.000000, 4.000000 )
	float(3) m_RateMax = ( 8.000000, 8.000000, 8.000000 )
	float(3) m_RateMin = ( -8.000000, -8.000000, -8.000000 )
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 2.000000
	float m_fLifetimeMin = 0.500000
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusRandExponent = 0.800000
	float m_flRadiusMin = 7.000000
	float m_flRadiusMax = 25.000000
	string m_Notes = ""
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_AgeNoise C_INIT_AgeNoise_0
{
	float m_flAgeMax = 0.200000
	float m_flAgeMin = 0.200000
	string m_Notes = ""
}

C_INIT_CreateFromParentParticles C_INIT_CreateFromParentParticles_0
{
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	float(3) m_OffsetMin = ( -50.000000, -50.000000, -50.000000 )
	float(3) m_OffsetMax = ( 50.000000, 50.000000, 50.000000 )
	string m_Notes = ""
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	float(3) m_vecOutputMax = ( 1.000000, 1.000000, 900.000000 )
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmissionDuration = 0.250000
	float m_flEmitRate = 203.000000
	string m_Notes = ""
}