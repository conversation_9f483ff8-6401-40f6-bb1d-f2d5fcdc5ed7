<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 60
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 210, 239, 159, 255 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_Decay_0,
		&C_OP_FadeInSimple_0,
		&C_OP_PositionLock_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_SetSingleControlPointPosition_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RingWave_0,
		&C_INIT_RemapScalar_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_omnislash_ascension_sparks.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_fsos_flashbang.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\particle_ring_wavy4.vtex"
	float m_flAnimationRate2 = 0.100000
	float m_flAnimationRate = 1.000000
	int m_nOrientationType = 2
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float m_fDrag = 0.050000
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flStartScale = 1.500000
	int m_nOpEndCapState = 0
	float m_flBias = 0.900000
	string m_Notes = ""
	float m_flEndScale = 0.000000
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	int(4) m_ColorFade = ( 255, 216, 0, 255 )
	float m_flFadeStartTime = 0.100000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 0.400000
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	string m_Notes = ""
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	float m_Rate = -3.000000
	int m_nField = 16
	int m_nOpEndCapState = 1
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.500000
	string m_Notes = ""
}

C_OP_SetSingleControlPointPosition C_OP_SetSingleControlPointPosition_0
{
	float(3) m_vecCP1Pos = ( 0.000000, 0.000000, 20.000000 )
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 0.750000
	float m_fLifetimeMin = 0.850000
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 175.000000
	float m_flRadiusMin = 175.000000
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	float m_flNoiseScale = 3.000000
	float(3) m_vecOutputMax = ( 32.000000, 32.000000, 132.000000 )
	float(3) m_vecOutputMin = ( -32.000000, -32.000000, -32.000000 )
	float m_flNoiseScaleLoc = 5.000000
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 20.000000 )
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 10.000000 )
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMin = ( 255, 143, 0, 255 )
	int(4) m_ColorMax = ( 210, 239, 159, 255 )
	string m_Notes = ""
}

C_INIT_RingWave C_INIT_RingWave_0
{
	bool m_bEvenDistribution = true
	string m_Notes = ""
}

C_INIT_RemapScalar C_INIT_RemapScalar_0
{
	string m_Notes = ""
	float m_flInputMin = 0.600000
	int m_nFieldOutput = 7
	float m_flOutputMin = 1.000000
	float m_flOutputMax = 0.000000
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmissionDuration = 1.000000
	string m_Notes = ""
	float m_flEmitRate = 60.000000
}