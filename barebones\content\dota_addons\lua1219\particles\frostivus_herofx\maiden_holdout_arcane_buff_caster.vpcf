<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 200
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flNoDrawTimeToGoToSleep = 12.000000
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_Decay_0,
		&C_OP_MovementRotateParticleAroundAxis_0,
		&C_OP_PositionLock_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomColor_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_CreateWithinSphere_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	string m_hTexture = "materials\\particle\\impact\\fleks3.vtex"
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float m_fDrag = 0.010000
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 0.150000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_MovementRotateParticleAroundAxis C_OP_MovementRotateParticleAroundAxis_0
{
	float m_flRotRate = -75.000000
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMax = ( 117, 175, 241, 255 )
	int(4) m_ColorMin = ( 155, 214, 245, 255 )
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 10.000000
	float m_fLifetimeMin = 8.000000
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMin = 2.000000
	float m_flRadiusMax = 4.000000
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	int m_nAlphaMax = 155
	int m_nAlphaMin = 55
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	float(3) m_OffsetMax = ( 50.000000, 0.000000, 50.000000 )
	float(3) m_OffsetMin = ( 40.000000, 0.000000, 32.000000 )
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	float m_flDegreesMin = -360.000000
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 63
	string m_Notes = ""
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	float m_flNoiseScale = 0.500000
	float m_flNoiseScaleLoc = 0.000000
	float(3) m_vecOutputMin = ( -16.000000, -16.000000, 0.000000 )
	float(3) m_vecOutputMax = ( 16.000000, 16.000000, 45.000000 )
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	float m_fRadiusMax = 52.000000
	float m_fSpeedMin = -82.000000
	float m_fSpeedMax = -12.000000
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 6.000000
	string m_Notes = ""
}