// Tutorial how to use #base: // 如何使用 #base 的教程:
// https://moddota.com/tools/combining-kv-files-using-base
#base "abilities/dummy_unit_ability.txt" // 基础文件：虚拟单位技能
#base "abilities/chaos_knight_reality_rift.txt" // 基础文件：混沌骑士裂隙
#base "abilities/detonator_conjure_image.txt" // 基础文件：爆破工幻象
#base "abilities/roshan.txt" // 基础文件：Roshan技能
#base "abilities/tormentor.txt" // 基础文件：折磨者技能

"DOTAAbilities" // Dota技能定义块
{
	// ability_datadriven example // 数据驱动技能示例
	"example_ability" // 技能名称 (内部使用)
	{
		"BaseClass"						"ability_datadriven" // 基础类别：数据驱动技能
		"AbilityTextureName"			"holdout_blade_fury" // 技能图标名称 (来自 vtex 文件)
		"AbilityBehavior"				"DOTA_ABILITY_BEHAVIOR_NO_TARGET | DOTA_ABILITY_BEHAVIOR_CHANNELLED" // 技能行为：无目标 | 持续施法
	//	Most used Ability Behaviors: // 最常用的技能行为:
	//	DOTA_ABILITY_BEHAVIOR_HIDDEN: 技能由单位拥有，但无法施放，也不会显示在HUD上。
	//	DOTA_ABILITY_BEHAVIOR_PASSIVE: 像上面一样无法施放，但会显示在技能HUD上。
	//	DOTA_ABILITY_BEHAVIOR_NO_TARGET: 不需要目标即可施放，按下按钮即刻触发。
	//	DOTA_ABILITY_BEHAVIOR_UNIT_TARGET: 技能需要指定一个目标单位才能施放。
	//	DOTA_ABILITY_BEHAVIOR_POINT: 技能可以施放在鼠标光标指向的任何位置（如果点击了单位，则施放在单位所在的位置）。
	//	DOTA_ABILITY_BEHAVIOR_AOE: 技能会绘制一个作用范围半径。你仍然需要一个目标行为（如 DOTA_ABILITY_BEHAVIOR_POINT）才能使其生效。
	//	DOTA_ABILITY_BEHAVIOR_NOT_LEARNABLE: 技能可能可以施放或有施法机制，但不能被学习（通常是临时技能）。
	//	DOTA_ABILITY_BEHAVIOR_CHANNELLED: 技能是持续施法的。如果使用者移动或被沉默，技能会被打断。
	//	DOTA_ABILITY_BEHAVIOR_TOGGLE: 切换型技能。
	//	DOTA_ABILITY_BEHAVIOR_AUTOCAST: 自动施法型技能。
	//	DOTA_ABILITY_BEHAVIOR_DIRECTIONAL: 需要英雄指定方向。例如：米拉娜的箭或帕吉的钩子。
	//	DOTA_ABILITY_BEHAVIOR_IMMEDIATE: 可与 DOTA_ABILITY_BEHAVIOR_NO_TARGET 和 AbilityCastPoint 0 结合使用，实现移动时瞬发。
	//	DOTA_ABILITY_BEHAVIOR_ATTACK: 用于非被动的攻击特效。
	//	DOTA_ABILITY_BEHAVIOR_ROOT_DISABLES: 技能在被缠绕时无法使用。
	//	DOTA_ABILITY_BEHAVIOR_UNRESTRICTED: 技能在指令受限时仍可使用。
	//	DOTA_ABILITY_BEHAVIOR_IGNORE_PSEUDO_QUEUE: 可以在眩晕、施法或强制攻击时执行。仅适用于切换型技能。
	//	DOTA_ABILITY_BEHAVIOR_IGNORE_CHANNEL: 使用此技能不会打断持续施法。

		"AbilityUnitTargetTeam"			"DOTA_UNIT_TARGET_TEAM_ENEMY" // 技能目标队伍：敌方
		"AbilityUnitTargetType"			"DOTA_UNIT_TARGET_HERO | DOTA_UNIT_TARGET_BASIC" // 技能目标类型：英雄 | 普通单位
		"AbilityUnitTargetFlags"		"DOTA_UNIT_TARGET_FLAG_MAGIC_IMMUNE_ENEMIES" // 技能目标标志：包括魔法免疫的敌人
		"AbilityUnitDamageType"			"DAMAGE_TYPE_PURE" // 技能伤害类型：纯粹伤害
		"SpellImmunityType"				"SPELL_IMMUNITY_ENEMIES_YES" // 法术免疫类型：对敌人有效（即可以作用于魔免单位）
		"SpellDispellableType"			"SPELL_DISPELLABLE_YES" // 可驱散类型：是

		"AbilityCastPoint"				"0.3" // 施法前摇时间 (秒)
		"AbilityCooldown"				"10.0" // 冷却时间 (秒)
		"AbilityChannelTime"			"2.0 1.8 1.6 1.5" // 持续施法时间 (秒)，按等级变化
		
		"AbilityCastAnimation"			"ACT_DOTA_DISABLED" // 施法动作 (这里设为禁用状态的动作)
		"AbilityManaCost"				"300" // 魔法消耗
		"AbilityProcsMagicStick"		"1" // 是否触发魔棒/魔杖 (1表示是)

		"precache" // 预缓存资源
		{
			"particle"          "particles/econ/generic/generic_aoe_explosion_sphere_1/generic_aoe_explosion_sphere_1.vpcf" // 粒子特效文件
			"soundfile"         "soundevents/game_sounds_heroes/game_sounds_gyrocopter.vsndevts" // 声音事件文件
		}

		"OnSpellStart" // 技能开始施法时触发的事件
		{
			"ApplyModifier" // 应用修改器 (Modifier)
			{
				"Target"    	"CASTER" // 目标：施法者
				"ModifierName"  "modifier_channel_start" // 修改器名称
			}
			"FireSound" // 播放声音
			{
				"EffectName"    "Hero_Gyrocopter.CallDown.Fire" // 声音事件名称
				"Target"      	"CASTER" // 声音来源：施法者
			}
		}

		"OnChannelSucceeded" // 持续施法成功完成时触发的事件
		{
			"RemoveModifier" // 移除修改器
			{
				"Target"        	"CASTER" // 目标：施法者
				"ModifierName"      "modifier_channel_start" // 修改器名称
			}

			"AttachEffect" // 附加特效
			{
				"EffectName"      		"particles/econ/generic/generic_aoe_explosion_sphere_1/generic_aoe_explosion_sphere_1.vpcf" // 特效名称
				"EffectAttachType"    	"follow_origin" // 附加类型：跟随原点
				"EffectRadius"      		"%radius" // 特效半径 (引用 AbilitySpecial 中的 radius)
				"EffectDurationScale" 		"1" // 特效持续时间缩放
				"EffectLifeDurationScale" 	"1" // 特效生命周期缩放
				"EffectColorA"      		"255 0 0" // 特效颜色 A (RGB)
				"EffectColorB"      		"255 0 0" // 特效颜色 B (RGB)
				"Target"      				"CASTER" // 特效目标：施法者
			}

            "Damage" // 造成伤害
			{
				"Type"          "DAMAGE_TYPE_PURE" // 伤害类型：纯粹伤害
				"Damage"        "%damage" // 伤害值 (引用 AbilitySpecial 中的 damage)
				"Target" // 伤害目标设置
				{
					"Center"		"CASTER" // 中心点：施法者
					"Radius"		"%radius" // 半径 (引用 AbilitySpecial 中的 radius)
					"Teams"			"DOTA_UNIT_TARGET_TEAM_ENEMY" // 目标队伍：敌方
					"Types"			"DOTA_UNIT_TARGET_HERO | DOTA_UNIT_TARGET_BASIC" // 目标类型：英雄 | 普通单位
					"Flags"			"DOTA_UNIT_TARGET_FLAG_MAGIC_IMMUNE_ENEMIES" // 目标标志：包括魔法免疫的敌人
				}
			}

			"Knockback" // 击退效果
			{
				"Center"  "CASTER" // 中心点：施法者
				"Target" // 击退目标设置
				{
					"Center"  	"CASTER" // 中心点：施法者
					"Radius"  	"%radius" // 半径 (引用 AbilitySpecial 中的 radius)
					"Teams"   	"DOTA_UNIT_TARGET_TEAM_ENEMY" // 目标队伍：敌方
				}
				"Duration"  "%duration" // 击退持续时间 (引用 AbilitySpecial 中的 duration)
				"Distance"  "%distance" // 击退距离 (引用 AbilitySpecial 中的 distance)
				"Height"  	"%height" // 击退高度 (引用 AbilitySpecial 中的 height)
			}

      		"FireSound" // 播放声音
			{
				"EffectName"    "Hero_Gyrocopter.CallDown.Damage" // 声音事件名称
				"Target"      	"CASTER" // 声音来源：施法者
			}
		}

    	"OnChannelFinish" // 持续施法正常结束时触发 (注意：通常在 Succeeded 或 Interrupted 之后，根据情况可能不会触发)
		{
			"RemoveModifier" // 移除修改器
			{
				"Target"        	"CASTER" // 目标：施法者
				"ModifierName"      "modifier_channel_start" // 修改器名称
			}
		}

        "OnChannelInterrupted" // 持续施法被打断时触发的事件
		{
			"RemoveModifier" // 移除修改器
			{
				"Target"    		"CASTER" // 目标：施法者
				"ModifierName"  	"modifier_channel_start" // 修改器名称
			}
		}

		"Modifiers" // 定义修改器 (Buff/Debuff)
		{
			"modifier_channel_start" // 修改器名称 (与上面事件中使用的名称对应)
			{
				"IsHidden"			"1" // 是否隐藏 (不在状态栏显示)
				"IsBuff"			"1" // 是否为增益效果
				"IsDebuff"			"0" // 是否为负面效果
				"IsStunDebuff"		"0" // 是否为眩晕类负面效果
				"IsPurgable"		"1" // 是否可被驱散
            
				"EffectName"    	"particles/test_particle/channel_field_2.vpcf"//"gyro_calldown_marker_c"//"gyrocopter_call_down" // 修改器附加的粒子特效
				"EffectAttachType"  "follow_origin" // 特效附加类型：跟随原点
                "EffectRadius"      "%radius" // 特效半径 (引用 AbilitySpecial 中的 radius)
				"EffectColorA"      "255 0 0" // 特效颜色 A
				"EffectColorB"      "255 0 0" // 特效颜色 B
			}
		}

		"AbilitySpecial" // 定义技能特殊值 (可在事件和修改器中通过 %key% 引用)
		{
			"01" // 条目编号 (必须从01开始递增)
			{
				"var_type"						"FIELD_INTEGER" // 变量类型：整数
				"duration"						"1" // 变量名：duration, 值：1 (这里可能是击退持续时间)
			}
		  
			"02"
			{
				"var_type"						"FIELD_INTEGER" // 变量类型：整数
				"damage"						"400 600 800 1000" // 变量名：damage, 值按等级变化
				"CalculateSpellDamageTooltip"	"1" // 在技能面板显示计算后的魔法伤害 (考虑魔抗等)
			}
		  
			"03"
			{
				"var_type"						"FIELD_INTEGER" // 变量类型：整数
				"radius"						"550 550 600 650" // 变量名：radius, 值按等级变化
				"LinkedSpecialBonus"			"special_bonus_unique_hero_name" // 关联的天赋加成 (用于显示 "+X radius" 等) - 需要替换为实际天赋名称
			}
		  
			"04"
			{
				"var_type"						"FIELD_INTEGER" // 变量类型：整数
				"distance"						"400 500 600 700" // 变量名：distance (击退距离), 值按等级变化
			}
		  
			"05"
			{
				"var_type"						"FIELD_INTEGER" // 变量类型：整数
				"height"						"100 200 300 400" // 变量名：height (击退高度), 值按等级变化
			}
		}
	}

	"create_attack_tower"
	{
		"BaseClass"						"ability_lua" 	
		"AbilityTextureName"			"holdout_battle_rage" 
		"ScriptFile"					"ability_example/create_attack_tower"
		"AbilityBehavior"				"DOTA_ABILITY_BEHAVIOR_POINT"
		"AbilityCastPoint"				"0.0"
		"AbilityCooldown"				"0.0"
		"AbilityManaCost"				"0.0"
		"AbilityGoldCost"				"50"
		"AbilityCastRange"				"4000"
		"MaxLevel"						"1"
		"AbilitySpecial"
		{
			"01"
			{
				"var_type"						"FIELD_INTEGER" 
				"tower_id"						"1" 	
			}
		}
	}

	"create_slow_tower"
	{
		"BaseClass"						"ability_lua" 	
		"AbilityTextureName"			"holdout_blade_fury" 
		"ScriptFile"					"ability_example/create_slow_tower"
		"AbilityBehavior"				"DOTA_ABILITY_BEHAVIOR_POINT"
		"AbilityCastPoint"				"0.0"
		"AbilityCooldown"				"0.0"
		"AbilityManaCost"				"0.0"
		"AbilityGoldCost"				"50"
		"AbilityCastRange"				"4000"
		"MaxLevel"						"1"
		"AbilitySpecial"
		{
			"01"
			{
				"var_type"						"FIELD_INTEGER" 
				"tower_id"						"2" 	
			}
		}
	}

	"attack_slowed_area"
	{
		"BaseClass"						"ability_lua" 	
		"ScriptFile"					"ability_example/attack_slowed_area"
		"AbilityBehavior"				"DOTA_ABILITY_BEHAVIOR_PASSIVE"
		"AbilityUnitTargetTeam"			"DOTA_UNIT_TARGET_TEAM_ENEMY"
		"AbilityCastRange"				"600"
		"AbilitySpecial"
		{
			"01"
			{
				"var_type"						"FIELD_INTEGER" 
				"slow_radius"					"600" 	
			}

			"02"
			{
				"var_type"						"FIELD_INTEGER" 
				"slow_percentage"				"50" 	
			}

			"03"
			{
				"var_type"						"FIELD_INTEGER" 
				"slow_time"						"2" 	
			}
		}
	}
}
