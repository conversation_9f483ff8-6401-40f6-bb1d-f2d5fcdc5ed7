<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 64
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 19.000000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_PositionLock_0,
		&C_OP_EndCapTimedDecay_0,
		&C_OP_LerpEndCapScalar_0,
		&C_OP_BasicMovement_0,
		&C_OP_MovementRotateParticleAroundAxis_0,
		&C_OP_RampScalarLinear_0,
		&C_OP_BasicMovement_2
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateSpiralSphere_0,
		&C_INIT_RemapInitialDirectionToCPToVector_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomRadius_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		&C_OP_AttractToControlPoint_0,
		&C_OP_RandomForce_0
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	float m_flSelfIllumAmount = 1.000000
	string m_hTexture = "materials\\particle\\crystal\\crystal.vtex"
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_OP_EndCapTimedDecay C_OP_EndCapTimedDecay_0
{
	float m_flDecayTime = 1.000000
	string m_Notes = ""
}

C_OP_LerpEndCapScalar C_OP_LerpEndCapScalar_0
{
	float m_flOutput = 0.000000
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	int m_nOpEndCapState = 0
	string m_Notes = ""
}

C_OP_MovementRotateParticleAroundAxis C_OP_MovementRotateParticleAroundAxis_0
{
	int m_nCP = 3
	float m_flRotRate = -180.000000
	float(3) m_vecRotAxis = ( 1.000000, -1.000000, 1.000000 )
	int m_nOpEndCapState = 0
	string m_Notes = ""
}

C_OP_RampScalarLinear C_OP_RampScalarLinear_0
{
	float m_flEndTime_max = 10000000000.000000
	float m_flEndTime_min = 10000000000.000000
	float m_RateMax = 4.000000
	float m_RateMin = -4.000000
	int m_nField = 4
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_2
{
	int m_nOpEndCapState = 1
	float(3) m_Gravity = ( 0.000000, 0.000000, -1500.000000 )
	float m_fDrag = 0.320000
	string m_Notes = ""
}

C_INIT_CreateSpiralSphere C_INIT_CreateSpiralSphere_0
{
	bool m_bUseParticleCount = true
	int m_nDensity = 1
	float m_flInitialRadius = 68.000000
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_INIT_RemapInitialDirectionToCPToVector C_INIT_RemapInitialDirectionToCPToVector_0
{
	bool m_bNormalize = true
	int m_nFieldOutput = 21
	int m_nCP = 3
	float m_flScale = 3.000000
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 12
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 29.000000
	float m_flRadiusMin = 19.000000
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 128
	string m_Notes = ""
}

C_OP_AttractToControlPoint C_OP_AttractToControlPoint_0
{
	int m_nControlPointNumber = 3
	float m_fFalloffPower = 0.550000
	float m_fForceAmount = -149800.000000
	int m_nOpEndCapState = 1
	float(3) m_vecComponentScale = ( 1.000000, 1.000000, 0.000000 )
	string m_Notes = ""
}

C_OP_RandomForce C_OP_RandomForce_0
{
	int m_nOpEndCapState = 1
	float(3) m_MinForce = ( -5000.000000, -5000.000000, -5000.000000 )
	float(3) m_MaxForce = ( 5000.000000, 5000.000000, 5000.000000 )
	string m_Notes = ""
}