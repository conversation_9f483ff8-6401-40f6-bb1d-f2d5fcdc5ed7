<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 32
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 25.000000
	int(4) m_ConstantColor = ( 255, 255, 255, 55 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_PositionLock_0,
		&C_OP_SpinUpdate_0,
		&C_OP_FadeInSimple_0,
		&C_OP_ColorInterpolate_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomRotationSpeed_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\particle_ring_wave_8.vtex"
	float m_flAnimationRate = 0.750000
	string m_Notes = ""
	int m_nOrientationType = 2
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.500000
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flEndScale = 3.000000
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_OP_SpinUpdate C_OP_SpinUpdate_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	int(4) m_ColorFade = ( 157, 0, 0, 255 )
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 0.600000
	float m_fLifetimeMin = 0.400000
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMin = 100.000000
	float m_flRadiusMax = 100.000000
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
	bool m_bRandomlyFlipDirection = false
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMax = ( 255, 159, 159, 255 )
	int(4) m_ColorMin = ( 251, 133, 147, 255 )
	string m_Notes = ""
}

C_INIT_RandomRotationSpeed C_INIT_RandomRotationSpeed_0
{
	string m_Notes = ""
	float m_flDegreesMin = 90.000000
	float m_flDegreesMax = 90.000000
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 10.000000
	string m_Notes = ""
}