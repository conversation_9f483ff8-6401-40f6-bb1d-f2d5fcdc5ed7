# notifications.lua ChangeLog

### Version 1.00
- Updated version to reflect the Barebones 1.0 release

### Version 0.89
- Changed the names of the panorama files from barebones_hud_base.* to barebones_notifications.*

### Version 0.88
- Updated notifications subsystem to support deleting notifications on-demand

### Version 0.87
- Updated notifications subsystem to support DOTAItemImage panel type.

### Version 0.86
- Updated notifications subsystem to apply hittest="false" to all generated panels

### Version 0.85
- Updated notifications.lua to support DOTAAbilityImage and Image panel types.
- Updated notifications.lua to use a table-based function call **NOTE: Older multi-parameter calls to notifications.lua WILL FAIL.**
- Updated examples/notificationsExample.lua to reflect the new calls and table argument system.

### Version 0.80
- Added global NOTIFICATIONS_VERSION
- Started tracking version updates for notifications.lua