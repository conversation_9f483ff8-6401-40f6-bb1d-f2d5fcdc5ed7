<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 100
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 255, 204, 0, 255 )
	float m_flMaxRecreationTime = -1.000000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderRopes_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_ColorInterpolate_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_Decay_0,
		&C_OP_BasicMovement_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RemapScalar_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_PositionPlaceOnGround_0,
		&C_INIT_CreateOnModel_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		&C_OP_AttractToControlPoint_0
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_fsos_flashbang.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_fsos_flash.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_fsos_caster_grndhit.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_fsos_caster_scortch.vpcf"
		}
	]
}

C_OP_RenderRopes C_OP_RenderRopes_0
{
	int m_bAdditive = 1
	int m_nOrientationType = 3
	string m_hTexture = "materials\\particle\\beam_edge_03.vtex"
	string m_Notes = ""
	float m_flTextureVWorldSize = 999.999939
	int m_nMaxTesselation = 3
	int m_nMinTesselation = 3
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	float m_flFadeStartTime = 0.250000
	float m_flFadeEndTime = 0.600000
	int(4) m_ColorFade = ( 72, 32, 0, 255 )
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 4.000000
	float m_flBias = 0.600000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMin = 2.000000
	float m_flRadiusMax = 2.000000
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 255, 186, 69, 255 )
	int(4) m_ColorMax = ( 255, 186, 69, 255 )
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.600000
	float m_fLifetimeMax = 0.600000
}

C_INIT_RemapScalar C_INIT_RemapScalar_0
{
	string m_Notes = ""
	float m_flInputMin = 0.100000
	float m_flInputMax = 0.185000
	float m_flOutputMin = 1.000000
	float m_flOutputMax = 0.000000
	bool m_bScaleInitialRange = true
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMax = ( -5.000000, 0.000000, 125.000000 )
	float(3) m_OffsetMin = ( -5.000000, 0.000000, 125.000000 )
	int m_nControlPointNumber = 1
}

C_INIT_PositionPlaceOnGround C_INIT_PositionPlaceOnGround_0
{
	string m_Notes = ""
	float m_flOffset = 5.000000
}

C_INIT_CreateOnModel C_INIT_CreateOnModel_0
{
	string m_Notes = ""
	string m_HitboxSetName = "low"
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmissionDuration = 0.100000
	float m_flEmitRate = 200.000000
}

C_OP_AttractToControlPoint C_OP_AttractToControlPoint_0
{
	string m_Notes = ""
	float m_fForceAmount = -100.000000
	float m_fFalloffPower = 0.000000
	int m_nControlPointNumber = 1
}