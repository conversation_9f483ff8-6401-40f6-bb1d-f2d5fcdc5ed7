<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 64
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_FadeAndKill_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_SpinUpdate_0,
		&C_OP_ColorInterpolate_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomColor_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_PositionPlaceOnGround_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\particle_ring_wave_10.vtex"
	string m_Notes = ""
	int m_nOrientationType = 2
	float m_flAnimationRate = 0.300000
	float m_flAnimationRate2 = 0.100000
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float m_fDrag = 0.020000
}

C_OP_FadeAndKill C_OP_FadeAndKill_0
{
	string m_Notes = ""
	float m_flEndFadeInTime = 0.200000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 80.000000
}

C_OP_SpinUpdate C_OP_SpinUpdate_0
{
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	int(4) m_ColorFade = ( 175, 155, 156, 255 )
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.350000
	float m_fLifetimeMax = 0.500000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMax = 1.500000
	float m_flRadiusMin = 0.500000
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
	int m_nAlphaMin = 250
	int m_nAlphaMax = 200
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 1
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 163, 154, 103, 255 )
	int(4) m_ColorMax = ( 200, 68, 42, 255 )
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMin = ( -5.000000, 0.000000, 125.000000 )
	float(3) m_OffsetMax = ( -5.000000, 0.000000, 125.000000 )
}

C_INIT_PositionPlaceOnGround C_INIT_PositionPlaceOnGround_0
{
	string m_Notes = ""
	float m_flOffset = 5.000000
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 12
}