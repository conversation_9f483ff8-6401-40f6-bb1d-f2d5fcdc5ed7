<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	int m_nInitialParticles = 0
	float(3) m_BoundingBoxMin = ( -10.000000, -10.000000, -10.000000 )
	float(3) m_BoundingBoxMax = ( 10.000000, 10.000000, 10.000000 )
	int m_nSnapshotControlPoint = 0
	string m_pszSnapshotName = ""
	string m_pszTargetLayerID = ""
	string m_hReferenceReplacement = ""
	string m_pszCullReplacementName = ""
	float m_flCullRadius = 0.000000
	float m_flCullFillCost = 1.000000
	int m_nCullControlPoint = 0
	float m_flMaxRecreationTime = 0.000000
	string m_hFallback = ""
	int m_nFallbackMaxCount = -1
	string m_hLowViolenceDef = ""
	uint(4) m_ConstantColor = ( 255, 255, 255, 55 )
	float(3) m_ConstantNormal = ( 0.000000, 0.000000, 1.000000 )
	float m_flConstantRadius = 25.000000
	float m_flConstantRotation = 0.000000
	float m_flConstantRotationSpeed = 0.000000
	float m_flConstantLifespan = 1.000000
	int m_nConstantSequenceNumber = 0
	int m_nConstantSequenceNumber1 = 0
	int m_nGroupID = 0
	float m_flMaximumTimeStep = 0.100000
	float m_flMaximumSimTime = 0.000000
	float m_flMinimumSimTime = 0.000000
	float m_flMinimumTimeStep = 0.000000
	int m_nMinimumFrames = 0
	int m_nMinCPULevel = 0
	int m_nMinGPULevel = 0
	bool m_bViewModelEffect = false
	bool m_bScreenSpaceEffect = false
	float m_flMaxDrawDistance = 100000.000000
	float m_flStartFadeDistance = 200000.000000
	float m_flNoDrawTimeToGoToSleep = 8.000000
	int m_nMaxParticles = 32
	int m_nSkipRenderControlPoint = -1
	int m_nAllowRenderControlPoint = -1
	int m_nAggregationMinAvailableParticles = 0
	float m_flAggregateRadius = 0.000000
	float m_flStopSimulationAfterTime = 1000000000.000000
	float(3) m_vControlPoint1DefaultOffsetRelativeToControlPoint0 = ( 0.000000, 0.000000, 0.000000 )
	string m_Name = ""
	CParticleOperatorInstance*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_SetControlPointsToParticle_0,
		&C_OP_MovementPlaceOnGround_0
	]
	CParticleOperatorInstance*[] m_Renderers = 
	[
		
	]
	CParticleOperatorInstance*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_RandomColor_0,
		&C_INIT_VelocityFromCP_0
	]
	CParticleOperatorInstance*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperatorInstance*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperatorInstance*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_core_linear_glow.vpcf"
			bool m_bPreventNameBasedLookup = false
			float m_flDelay = 0.000000
			bool m_bEndCap = false
			bool m_bDisableChild = false
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_core_linear_light.vpcf"
			bool m_bPreventNameBasedLookup = false
			float m_flDelay = 0.000000
			bool m_bEndCap = false
			bool m_bDisableChild = false
		}
	]
	bool m_bShouldSort = true
	bool m_bShouldBatch = false
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float(3) m_Gravity = ( 0.000000, 0.000000, 0.000000 )
	float m_fDrag = 0.000000
	int m_nMaxConstraintPasses = 3
	bool m_bLockULCorner = false
	bool m_bLockURCorner = false
	bool m_bLockLLCorner = false
	bool m_bLockLRCorner = false
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = 1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.500000
	int m_nFieldOutput = 7
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flStartTime = 0.000000
	float m_flEndTime = 1.000000
	float m_flStartScale = 1.000000
	float m_flEndScale = 2.000000
	bool m_bEaseInAndOut = false
	float m_flBias = 0.500000
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_OP_SetControlPointsToParticle C_OP_SetControlPointsToParticle_0
{
	int m_nChildGroupID = 0
	int m_nFirstControlPoint = 3
	int m_nNumControlPoints = 1
	int m_nFirstSourcePoint = 0
	bool m_bSetOrientation = false
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_OP_MovementPlaceOnGround C_OP_MovementPlaceOnGround_0
{
	float m_flOffset = 0.000000
	float m_flMaxTraceLength = 128.000000
	float m_flTolerance = 32.000000
	float m_flTraceOffset = 64.000000
	float m_flLerpRate = 0.000000
	string m_CollisionGroupName = "NONE"
	int m_nRefCP1 = -1
	int m_nRefCP2 = -1
	int m_nLerpCP = -1
	bool m_bKill = false
	bool m_bIncludeWater = false
	bool m_bSetNormal = false
	bool m_bScaleOffset = false
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMin = 0.400000
	float m_fLifetimeMax = 0.600000
	float m_fLifetimeRandExponent = 1.000000
	bool m_bRunForParentApplyKillList = true
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	float m_fRadiusMin = 0.000000
	float m_fRadiusMax = 0.000000
	float(3) m_vecDistanceBias = ( 1.000000, 1.000000, 1.000000 )
	float(3) m_vecDistanceBiasAbs = ( 0.000000, 0.000000, 0.000000 )
	int m_nControlPointNumber = 0
	int m_nScaleCP = -1
	float m_fSpeedMin = 0.000000
	float m_fSpeedMax = 0.000000
	float m_fSpeedRandExp = 1.000000
	bool m_bLocalCoords = false
	bool m_bUseHighestEndCP = false
	float m_flEndCPGrowthTime = 0.000000
	float(3) m_LocalCoordinateSystemSpeedMin = ( 0.000000, 0.000000, 0.000000 )
	float(3) m_LocalCoordinateSystemSpeedMax = ( 0.000000, 0.000000, 0.000000 )
	bool m_bRunForParentApplyKillList = true
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	int m_nFieldOutput = 7
	int m_nAlphaMin = 25
	int m_nAlphaMax = 50
	float m_flAlphaRandExponent = 1.000000
	bool m_bRunForParentApplyKillList = true
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMin = 30.000000
	float m_flRadiusMax = 50.000000
	float m_flRadiusRandExponent = 1.000000
	bool m_bRunForParentApplyKillList = true
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	float m_flDegreesMin = 0.000000
	float m_flDegreesMax = 360.000000
	float m_flDegrees = 0.000000
	int m_nFieldOutput = 4
	float m_flRotationRandExponent = 1.000000
	bool m_bRandomlyFlipDirection = true
	bool m_bRunForParentApplyKillList = true
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMin = 0
	int m_nSequenceMax = 2
	bool m_bShuffle = false
	bool m_bLinear = false
	bool m_bRunForParentApplyKillList = true
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	float m_flPercent = 0.500000
	bool m_bRunForParentApplyKillList = true
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	uint(4) m_ColorMin = ( 234, 89, 106, 255 )
	uint(4) m_ColorMax = ( 162, 9, 9, 255 )
	uint(4) m_TintMin = ( 0, 0, 0, 0 )
	uint(4) m_TintMax = ( 255, 255, 255, 255 )
	float m_flTintPerc = 0.000000
	float m_flUpdateThreshold = 32.000000
	int m_nTintCP = 0
	int m_nFieldOutput = 6
	symbol m_nTintBlendMode = 0
	float m_flLightAmplification = 1.000000
	bool m_bRunForParentApplyKillList = true
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_INIT_VelocityFromCP C_INIT_VelocityFromCP_0
{
	int m_nControlPoint = 1
	int m_nControlPointCompare = -1
	int m_nControlPointLocal = -1
	float m_flVelocityScale = 1.000000
	bool m_bDirectionOnly = false
	bool m_bRunForParentApplyKillList = true
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmissionDuration = 0.000000
	float m_flStartTime = 0.000000
	float m_flEmitRate = 100.000000
	float m_flEmissionScale = 0.000000
	int m_nScaleControlPoint = -1
	int m_nScaleControlPointField = 0
	bool m_bScalePerParticle = false
	bool m_bInitFromKilledParentParticles = false
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}