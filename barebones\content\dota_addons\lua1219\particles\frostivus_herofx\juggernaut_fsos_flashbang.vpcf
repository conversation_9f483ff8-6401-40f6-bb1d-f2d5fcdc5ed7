<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 2
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 600.000000
	int(4) m_ConstantColor = ( 0, 234, 255, 155 )
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_InterpolateRadius_2
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRotation_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_fsos_flashbang_glow2.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_fsos_flashbang_ray.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	bool m_bDisableZBuffering = true
	string m_hTexture = "materials\\particle\\particle_flares\\anam_white.vtex"
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndTime = 0.900000
	float m_flBias = 0.800000
	float m_flStartScale = 3.000000
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	int(4) m_ColorFade = ( 243, 209, 126, 255 )
	float m_flFadeEndTime = 0.500000
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.750000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_2
{
	string m_Notes = ""
	float m_flBias = 0.250000
	float m_flEndScale = 0.000000
	float m_flStartTime = 0.900000
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.500000
	float m_fLifetimeMax = 0.500000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 1
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
	float m_flDegrees = 90.000000
	float m_flDegreesMax = 0.000000
	bool m_bRandomlyFlipDirection = false
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 6
}