<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 32
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 32.000000
	float m_flConstantLifespan = 0.650000
	int(4) m_ConstantColor = ( 54, 245, 139, 255 )
	float m_flMaxRecreationTime = -1.000000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderRopes_0,
		&C_OP_RenderDeferredLight_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_FadeInSimple_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_RemapParticleCountOnScalarEndCap_0,
		&C_OP_RemapParticleCountOnScalarEndCap_2
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_CreationNoise_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderRopes C_OP_RenderRopes_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\beam_smoke_03.vtex"
	string m_Notes = ""
	float m_flTextureVScrollRate = -0.200000
	float m_flTextureVWorldSize = 1428.571533
	int m_nMaxTesselation = 2
	int m_nMinTesselation = 2
}

C_OP_RenderDeferredLight C_OP_RenderDeferredLight_0
{
	int(4) m_ColorScale = ( 54, 245, 139, 255 )
	string m_Notes = ""
	float m_flRadiusScale = 3.500000
	float m_flAlphaScale = 10.000000
	float m_flStartFalloff = 0.500000
	string m_hTexture = "materials\\particle\\beam_smoke_03.vtex"
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.180000
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.600000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	bool m_bDisableOperator = true
	string m_Notes = ""
	float m_flEndScale = 0.100000
}

C_OP_RemapParticleCountOnScalarEndCap C_OP_RemapParticleCountOnScalarEndCap_0
{
	string m_Notes = ""
	bool m_bBackwards = true
	int m_nInputMax = 10
	int m_nFieldOutput = 16
}

C_OP_RemapParticleCountOnScalarEndCap C_OP_RemapParticleCountOnScalarEndCap_2
{
	string m_Notes = ""
	bool m_bBackwards = true
	int m_nInputMax = 25
	bool m_bScaleCurrent = true
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
}

C_INIT_CreationNoise C_INIT_CreationNoise_0
{
	string m_Notes = ""
	int m_nFieldOutput = 7
	float m_flOutputMax = 0.400000
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 24.000000
}