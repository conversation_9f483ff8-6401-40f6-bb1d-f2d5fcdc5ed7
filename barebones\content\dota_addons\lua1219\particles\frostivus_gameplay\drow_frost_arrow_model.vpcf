<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 16
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 0.800000
	int(4) m_ConstantColor = ( 217, 246, 255, 255 )
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderModels_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_InheritFromParentParticles_0,
		&C_OP_PositionLock_0,
		&C_OP_RampScalarLinearSimple_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_PositionOffset_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderModels C_OP_RenderModels_0
{
	string m_EconSlotName = ""
	string m_Notes = ""
	string m_ActivityName = ""
	string m_hOverrideMaterial = ""
	ModelReference_t[] m_ModelList = 
	[
		ModelReference_t
		{
			string m_model = "models/projectiles/drow_arrow.vmdl"
		}
	]
	bool m_bAnimated = true
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
	int m_nOpEndCapState = 1
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flStartScale = 0.200000
}

C_OP_InheritFromParentParticles C_OP_InheritFromParentParticles_0
{
	string m_Notes = ""
	int m_nFieldOutput = 21
}

C_OP_PositionLock C_OP_PositionLock_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 3
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	string m_Notes = ""
	int m_nField = 4
	float m_Rate = 14.000000
	float m_flEndTime = 10000000000.000000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 3
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMax = 0.100000
	float m_fLifetimeMin = 0.100000
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 3
	float(3) m_OffsetMin = ( -40.000000, 0.000000, 0.000000 )
	float(3) m_OffsetMax = ( -40.000000, 0.000000, 0.000000 )
	bool m_bLocalCoords = true
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 1
}