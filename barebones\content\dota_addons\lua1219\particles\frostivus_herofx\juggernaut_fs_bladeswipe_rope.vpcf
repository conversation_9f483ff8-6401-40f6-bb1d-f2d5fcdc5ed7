<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 100
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 15.000000
	int(4) m_ConstantColor = ( 255, 0, 0, 255 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderRopes_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_PositionLock_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_CreateSequentialPath_0,
		&C_INIT_RemapParticleCountToScalar_0,
		&C_INIT_RandomAlpha_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_fs_bladeswipe_flash.vpcf"
		}
	]
}

C_OP_RenderRopes C_OP_RenderRopes_0
{
	float m_flSelfIllumAmount = 2.000000
	string m_hTexture = "materials\\particle\\beam_streak_01.vtex"
	string m_Notes = ""
	float m_flTextureVOffset = 0.100000
	float m_flTextureVScrollRate = -0.150000
	float m_flTextureVWorldSize = 999.999939
	int m_nMaxTesselation = 2
	int m_nMinTesselation = 2
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flStartScale = 12.000000
	float m_flEndScale = 0.000000
	float m_flBias = 0.600000
	int m_nOpEndCapState = 1
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.750000
	int m_nOpEndCapState = 1
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.050000
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	float m_flFadeEndTime = 0.600000
	int(4) m_ColorFade = ( 105, 64, 36, 255 )
	int m_nOpEndCapState = 1
}

C_OP_PositionLock C_OP_PositionLock_0
{
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.600000
	float m_fLifetimeMax = 0.600000
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 255, 235, 235, 255 )
	int(4) m_ColorMax = ( 255, 133, 60, 255 )
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMin = 8.000000
	float m_flRadiusMax = 8.000000
}

C_INIT_CreateSequentialPath C_INIT_CreateSequentialPath_0
{
	string m_Notes = ""
	float m_flNumToAssign = 60.000000
	CPathParameters m_PathParams = CPathParameters
	{
		int m_nEndControlPointNumber = 3
		int m_nStartControlPointNumber = 2
	}
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_0
{
	int m_nInputMax = 60
	string m_Notes = ""
	float m_flOutputMin = 0.100000
	float m_flOutputMax = 10.000000
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 805.000000
	float m_flEmissionDuration = 0.075000
}