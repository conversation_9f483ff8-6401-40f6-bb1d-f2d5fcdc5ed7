<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 5
	string m_pszSnapshotName = ""
	float(3) m_BoundingBoxMin = ( 40.000000, 40.000000, -10.000000 )
	float(3) m_BoundingBoxMax = ( -40.000000, -40.000000, 10.000000 )
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantLifespan = 2.000000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderModels_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_Orient2DRelToCP_0,
		&C_OP_InterpolateRadius_2,
		&C_OP_SetSingleControlPointPosition_0,
		&C_OP_SetChildControlPoints_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_RemapScalarOnceTimed_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreationNoise_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomYaw_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreationNoise_2,
		&C_INIT_CreatePhyllotaxis_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_PositionPlaceOnGround_0,
		&C_INIT_RemapParticleCountToScalar_0,
		&C_INIT_RandomTrailLength_0,
		&C_INIT_RandomSecondSequence_0,
		&C_INIT_RandomSequence_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		&C_OP_RandomForce_0
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/frostivus_leshrac_splitearth_groundflash_lines.vpcf"
		}
	]
}

C_OP_RenderModels C_OP_RenderModels_0
{
	string m_hOverrideMaterial = ""
	string m_EconSlotName = ""
	float m_flAnimationRate = 60.000000
	bool m_bOrientZ = true
	string m_ActivityName = ""
	bool m_bScaleAnimationRate = true
	string m_Notes = ""
	ModelReference_t[] m_ModelList = 
	[
		ModelReference_t
		{
			string m_model = "models/props_destruction/rockyspikes_dynamic.vmdl"
		}
	]
	bool m_bAnimated = true
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float m_fDrag = 0.250000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flStartTime = 0.125000
	float m_flBias = 0.350000
	string m_Notes = ""
}

C_OP_Orient2DRelToCP C_OP_Orient2DRelToCP_0
{
	int m_nFieldOutput = 12
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_2
{
	float m_flEndTime = 0.125000
	float m_flStartScale = 0.000000
	float m_flBias = 0.850000
	string m_Notes = ""
}

C_OP_SetSingleControlPointPosition C_OP_SetSingleControlPointPosition_0
{
	int m_nCP1 = 2
	float(3) m_vecCP1Pos = ( 0.000000, 0.000000, -128.000000 )
	string m_Notes = ""
}

C_OP_SetChildControlPoints C_OP_SetChildControlPoints_0
{
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	int(4) m_ColorFade = ( 179, 255, 192, 255 )
	float m_flFadeEndTime = 0.000000
	string m_Notes = ""
}

C_OP_RemapScalarOnceTimed C_OP_RemapScalarOnceTimed_0
{
	float m_flRemapTime = 1.330000
	int m_nFieldInput = 13
	float m_flInputMin = 1.000000
	float m_flInputMax = 5.000000
	int m_nFieldOutput = 13
	float m_flOutputMin = 6.000000
	float m_flOutputMax = 10.000000
	string m_Notes = ""
}

C_INIT_CreationNoise C_INIT_CreationNoise_0
{
	float m_flOutputMax = 15.000000
	float m_flOutputMin = -15.000000
	int m_nFieldOutput = 5
	float m_flNoiseScaleLoc = 1.000000
	float m_flNoiseScale = 3.000000
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMin = 1.500000
	float m_flRadiusMax = 2.250000
	string m_Notes = ""
}

C_INIT_RandomYaw C_INIT_RandomYaw_0
{
	float m_flDegreesMax = 0.000000
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	float m_flDegreesMax = 0.000000
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 1.700000
	float m_fLifetimeMin = 1.700000
	string m_Notes = ""
}

C_INIT_CreationNoise C_INIT_CreationNoise_2
{
	float m_flNoiseScale = 1.000000
	float m_flNoiseScaleLoc = 0.100000
	int m_nFieldOutput = 20
	float m_flOutputMax = 0.000000
	string m_Notes = ""
}

C_INIT_CreatePhyllotaxis C_INIT_CreatePhyllotaxis_0
{
	int m_nScaleCP = 1
	float m_fsizeOverall = 1.000000
	float m_fRadPerPointTo = 0.500000
	float m_fMinRad = 1.000000
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 256.000000 )
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 256.000000 )
	string m_Notes = ""
}

C_INIT_PositionPlaceOnGround C_INIT_PositionPlaceOnGround_0
{
	bool m_bSetNormal = true
	float m_flMaxTraceLength = 512.000000
	string m_CollisionGroupName = "DEBRIS"
	float m_flOffset = 5.000000
	string m_Notes = ""
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_0
{
	bool m_bDisableOperator = true
	int m_nInputMax = 100
	int m_nFieldOutput = 12
	float m_flOutputMax = 13750.700195
	string m_Notes = ""
}

C_INIT_RandomTrailLength C_INIT_RandomTrailLength_0
{
	float m_flMinLength = 0.850000
	float m_flMaxLength = 1.500000
	string m_Notes = ""
}

C_INIT_RandomSecondSequence C_INIT_RandomSecondSequence_0
{
	int m_nSequenceMax = 5
	int m_nSequenceMin = 1
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 5
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 35
	string m_Notes = ""
}

C_OP_RandomForce C_OP_RandomForce_0
{
	float(3) m_MaxForce = ( 0.000000, 0.000000, -100.000000 )
	float(3) m_MinForce = ( 0.000000, 0.000000, -50.000000 )
	float m_flOpEndFadeOutTime = 3.000000
	float m_flOpStartFadeOutTime = 2.000000
	float m_flOpEndFadeInTime = 1.000000
	float m_flOpStartFadeInTime = 1.000000
	string m_Notes = ""
}