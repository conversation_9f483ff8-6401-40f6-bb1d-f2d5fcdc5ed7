"CustomUI"
{
	// swf files should be published into the same folder as this file

	// Add a numbered block for each swf file to load when your addon starts


	//"1"
	//{
	//	"File"		"Example"
	//	"Depth"		"50"
	//}
}

// =============================================
// Depths of base Dota UI elements
// =============================================
// hud_chat: 8,

// error_msg: 10,
	
// voicechat: 11,
// shop: 12,	
// tutorial: 13,
// herodisplay: 14,
// actionpanel: 15,
// inventory: 16,
// channelbar: 17,

// gameend: 19,
// chat_wheel: 20,
// survey: 21,	
// quests: 22,
// questlog: 23,

// ti_onstage_side: 30,

// last_hit_challenge: 35,
// waitingforplayers: 36,
// highlight_reel: 37,
// stats_dropdown: 38,
// halloween: 39,	
// killcam: 40,		// and inspect
// scoreboard: 41,
// quickstats: 42,
// shared_units: 43,
// shared_content: 44,

// holdout: 50,

// spectator_items: 145,
// spectator_graph: 146,
// spectator_harvest: 147,
// spectator_player: 148,
// spectator_fantasy: 149,

// heroselection: 250,
// spectate_heroselection: 251,
// shared_heroselectorandloadout : 252,

// broadcaster: 364,

// spectate: 365,
// coach: 366,

// combat_log: 367,

// guide_panel: 368,

// loadgame: 380,

// report_dialogue : 381,
// popups : 382,
// matchmaking_ready : 383,

// ti_onstage_pods: 500,

// overlay: 1000
// =============================================