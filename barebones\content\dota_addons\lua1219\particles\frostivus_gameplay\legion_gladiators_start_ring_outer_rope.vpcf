<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 75
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 161, 184, 201, 255 )
	int m_nConstantSequenceNumber = 4
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderRopes_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_FadeOutSimple_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_AlphaDecay_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RingWave_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_PositionPlaceOnGround_0,
		&C_INIT_RemapParticleCountToScalar_0,
		&C_INIT_RemapParticleCountToScalar_2,
		&C_INIT_RandomLifeTime_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderRopes C_OP_RenderRopes_0
{
	bool m_bMod2X = true
	int m_nOrientationType = 3
	string m_hTexture = "materials\\particle\\beam_crack_03.vtex"
	string m_Notes = ""
	float m_flTextureVWorldSize = 499.999969
	int m_nMaxTesselation = 3
	int m_nMinTesselation = 3
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.150000
	string m_Notes = ""
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	int m_nOpEndCapState = 1
	string m_Notes = ""
	int m_nField = 7
	float m_Rate = -2.000000
	float m_flEndTime = 1000000000.000000
}

C_OP_AlphaDecay C_OP_AlphaDecay_0
{
	string m_Notes = ""
	float m_flMinAlpha = 0.001000
}

C_INIT_RingWave C_INIT_RingWave_0
{
	float m_flPitch = 180.000000
	int m_nControlPointNumber = 7
	float m_flParticlesPerOrbit = 11.000000
	bool m_bEvenDistribution = true
	float m_flInitialRadius = 635.000000
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 300.000000
	float m_flRadiusMin = 150.000000
	string m_Notes = ""
}

C_INIT_PositionPlaceOnGround C_INIT_PositionPlaceOnGround_0
{
	bool m_bIncludeWater = true
	float m_flOffset = 5.000000
	string m_Notes = ""
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_0
{
	bool m_bScaleInitialRange = true
	float m_flOutputMax = 0.000000
	float m_flOutputMin = 1.000000
	int m_nFieldOutput = 7
	int m_nInputMax = 14
	int m_nInputMin = 12
	string m_Notes = ""
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_2
{
	bool m_bScaleInitialRange = true
	int m_nFieldOutput = 7
	int m_nInputMax = 5
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 15.000000
	float m_fLifetimeMax = 15.000000
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 15
}