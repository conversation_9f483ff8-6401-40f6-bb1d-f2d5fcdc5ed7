<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 16
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 255, 255, 255, 55 )
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_SpinUpdate_0,
		&C_OP_SpinYaw_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_FadeAndKill_0,
		&C_OP_PositionLock_0,
		&C_OP_ColorInterpolate_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomRotationSpeed_0,
		&C_INIT_RandomYaw_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomAlpha_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	string m_hTexture = "materials\\particle\\particle_ring_pulled.vtex"
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_SpinUpdate C_OP_SpinUpdate_0
{
	string m_Notes = ""
}

C_OP_SpinYaw C_OP_SpinYaw_0
{
	int m_nSpinRateDegrees = 20
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flEndScale = 9.000000
	float m_flStartScale = 0.000000
	string m_Notes = ""
}

C_OP_FadeAndKill C_OP_FadeAndKill_0
{
	float m_flEndFadeInTime = 0.250000
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	float m_flFadeStartTime = 0.250000
	int(4) m_ColorFade = ( 47, 92, 17, 255 )
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 10.000000
	float m_flRadiusMin = 30.000000
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 0.750000
	float m_fLifetimeMin = 0.300000
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomRotationSpeed C_INIT_RandomRotationSpeed_0
{
	float m_flDegreesMax = 120.000000
	float m_flDegreesMin = 100.000000
	string m_Notes = ""
}

C_INIT_RandomYaw C_INIT_RandomYaw_0
{
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMax = ( 255, 252, 158, 255 )
	int(4) m_ColorMin = ( 255, 233, 50, 255 )
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	int m_nAlphaMin = 200
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 12
	string m_Notes = ""
}