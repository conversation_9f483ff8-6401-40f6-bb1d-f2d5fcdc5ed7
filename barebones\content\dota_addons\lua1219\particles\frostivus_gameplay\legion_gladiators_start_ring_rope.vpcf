<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 75
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 255, 253, 223, 255 )
	int m_nConstantSequenceNumber = 4
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderRopes_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_FadeOutSimple_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_AlphaDecay_0,
		&C_OP_ColorInterpolate_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RingWave_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_PositionPlaceOnGround_0,
		&C_INIT_RemapParticleCountToScalar_0,
		&C_INIT_RemapParticleCountToScalar_2,
		&C_INIT_RandomLifeTime_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/legion_gladiators_start_ring_energy.vpcf"
		}
	]
}

C_OP_RenderRopes C_OP_RenderRopes_0
{
	int m_bAdditive = 1
	int m_nOrientationType = 3
	string m_hTexture = "materials\\particle\\beam_crack_02.vtex"
	string m_Notes = ""
	float m_flTextureVWorldSize = 333.333344
	int m_nMaxTesselation = 3
	int m_nMinTesselation = 3
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.500000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 1.500000
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	float m_flEndTime = 1000000000.000000
	float m_Rate = -2.000000
	int m_nField = 7
	string m_Notes = ""
	int m_nOpEndCapState = 1
}

C_OP_AlphaDecay C_OP_AlphaDecay_0
{
	float m_flMinAlpha = 0.001000
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	int(4) m_ColorFade = ( 255, 219, 143, 255 )
	float m_flFadeStartTime = 0.750000
}

C_INIT_RingWave C_INIT_RingWave_0
{
	string m_Notes = ""
	float m_flInitialRadius = 625.000000
	bool m_bEvenDistribution = true
	float m_flParticlesPerOrbit = 28.000000
	int m_nControlPointNumber = 7
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMin = 50.000000
	float m_flRadiusMax = 50.000000
}

C_INIT_PositionPlaceOnGround C_INIT_PositionPlaceOnGround_0
{
	string m_Notes = ""
	float m_flOffset = 5.000000
	bool m_bIncludeWater = true
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_0
{
	string m_Notes = ""
	int m_nInputMin = 28
	int m_nInputMax = 34
	int m_nFieldOutput = 7
	float m_flOutputMin = 1.000000
	float m_flOutputMax = 0.000000
	bool m_bScaleInitialRange = true
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_2
{
	string m_Notes = ""
	int m_nInputMax = 5
	int m_nFieldOutput = 7
	bool m_bScaleInitialRange = true
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 15.000000
	float m_fLifetimeMin = 15.000000
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 35
	string m_Notes = ""
}