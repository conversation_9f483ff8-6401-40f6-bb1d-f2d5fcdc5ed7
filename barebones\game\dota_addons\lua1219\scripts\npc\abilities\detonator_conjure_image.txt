"DOTAAbilities"
{
	//=================================================================================================================
	// Detonator: Conjure Image - ability_lua example
	//=================================================================================================================
	"detonator_conjure_image"
	{
		"BaseClass"						"ability_lua"
		"ScriptFile"                	"ability_example/detonator_conjure_image.lua"
		"AbilityTextureName"			"terrorblade_conjure_image"
		"AbilityBehavior"				"DOTA_ABILITY_BEHAVIOR_UNIT_TARGET"
		"AbilityUnitTargetTeam"			"DOTA_UNIT_TARGET_TEAM_BOTH"
		"AbilityUnitTargetType"			"DOTA_UNIT_TARGET_HERO | DOTA_UNIT_TARGET_BASIC"
		"AbilityUnitTargetFlags"		"DOTA_UNIT_TARGET_FLAG_MAGIC_IMMUNE_ENEMIES"
		"SpellImmunityType"				"SPELL_IMMUNITY_ENEMIES_YES"
		"SpellDispellableType"			"SPELL_DISPELLABLE_NO"
		"FightRecapLevel"				"1"
		"MaxLevel"               		"1"
		"AbilitySound"					"Hero_Terrorblade.ConjureImage"

		// Casting
		//-------------------------------------------------------------------------------------------------------------
		"AbilityCastRange"				"900"
		"AbilityCastPoint"				"0.3"

		// Time		
		//-------------------------------------------------------------------------------------------------------------
		"AbilityCooldown"				"60.0"

		// Cost
		//-------------------------------------------------------------------------------------------------------------
		"AbilityManaCost"				"150"

		"precache"
		{
			"soundfile"		"soundevents/game_sounds_heroes/game_sounds_terrorblade.vsndevts"
		}

		// Special
		//-------------------------------------------------------------------------------------------------------------
		"AbilitySpecial"
		{
			"01"
			{
				"var_type"							"FIELD_FLOAT"
				"duration"							"60.0"
			}
			"02"
			{
				"var_type"							"FIELD_FLOAT"
				"illusion_damage_out"				"-20"
			}
			"03"
			{
				"var_type"							"FIELD_INTEGER"
				"illusion_damage_in"				"100"
			}
			"04" // tooltip only (illusion_damage_out + 100)
			{
				"var_type"							"FIELD_INTEGER"
				"illusion_damage_dealt"				"80"
				"CalculateSpellDamageTooltip"		"0"
			}
			"05" // tooltip only (illusion_damage_in + 100)
			{
				"var_type"							"FIELD_INTEGER"
				"illusion_damage_taken"				"200"
				"CalculateSpellDamageTooltip"		"0"
			}
		}
	}
}
