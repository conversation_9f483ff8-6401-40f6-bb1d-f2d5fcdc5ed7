<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 110
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 249, 242, 161, 120 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_PositionLock_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_Orient2DRelToCP_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_RampScalarLinearSimple_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomSequence_0,
		&C_INIT_CreateInEpitrochoid_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_CreateInEpitrochoid_2,
		&C_INIT_PositionOffset_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/test_particle/damage_immunity_b.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 0
	string m_hTexture = "materials\\particle\\particle_swirl_04c.vtex"
	int m_nOrientationType = 2
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.500000
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 0.500000
	string m_Notes = ""
}

C_OP_Orient2DRelToCP C_OP_Orient2DRelToCP_0
{
	float m_flRotOffset = 100.000000
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	float m_flFadeStartTime = 0.500000
	int(4) m_ColorFade = ( 243, 240, 190, 255 )
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flBias = 0.750000
	float m_flEndScale = 3.000000
	string m_Notes = ""
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	float m_flEndTime = 999999.000000
	float m_Rate = -6.000000
	int m_nField = 16
	int m_nOpEndCapState = 1
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 3
	string m_Notes = ""
}

C_INIT_CreateInEpitrochoid C_INIT_CreateInEpitrochoid_0
{
	float m_flRadius1 = 62.000000
	float m_flRadius2 = -41.000000
	float m_flOffset = 92.000008
	float m_flParticleDensity = 8.000000
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMin = 0.500000
	float m_fLifetimeMax = 0.500000
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMin = 46.000000
	float m_flRadiusMax = 46.000000
	string m_Notes = ""
}

C_INIT_CreateInEpitrochoid C_INIT_CreateInEpitrochoid_2
{
	bool m_bOffsetExistingPos = true
	float m_flOffset = 44.000000
	float m_flRadius2 = -34.000000
	float m_flRadius1 = 19.000000
	int m_nComponent2 = 2
	int m_nComponent1 = -1
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 90.000000 )
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 90.000000 )
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 120.000000
	string m_Notes = ""
}