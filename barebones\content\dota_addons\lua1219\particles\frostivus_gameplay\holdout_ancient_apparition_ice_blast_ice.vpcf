<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 96
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_FadeInSimple_0,
		&C_OP_OscillateVector_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_BasicMovement_0,
		&C_OP_OscillateVector_2,
		&C_OP_OscillateScalar_0,
		&C_OP_OscillateScalar_2
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomSequence_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	float m_flSelfIllumAmount = 4.000000
	string m_hTexture = "materials\\particle\\impact\\fleks4.vtex"
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 0.300000
	string m_Notes = ""
}

C_OP_OscillateVector C_OP_OscillateVector_0
{
	bool m_bOffset = true
	float(3) m_FrequencyMax = ( 3.000000, 3.000000, 3.000000 )
	float(3) m_RateMax = ( 30.000000, 30.000000, 30.000000 )
	float(3) m_RateMin = ( -30.000000, -30.000000, -30.000000 )
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.300000
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float m_fDrag = 0.050000
	float(3) m_Gravity = ( 0.000000, 0.000000, -450.000000 )
	string m_Notes = ""
}

C_OP_OscillateVector C_OP_OscillateVector_2
{
	bool m_bOffset = true
	bool m_bProportional = false
	float(3) m_FrequencyMax = ( 3.000000, 3.000000, 3.000000 )
	float(3) m_FrequencyMin = ( 0.500000, 0.500000, 0.500000 )
	float(3) m_RateMax = ( 150.000000, 150.000000, 150.000000 )
	float(3) m_RateMin = ( -150.000000, -150.000000, -150.000000 )
	string m_Notes = ""
}

C_OP_OscillateScalar C_OP_OscillateScalar_0
{
	float m_FrequencyMin = 0.100000
	float m_RateMax = 12.000000
	float m_RateMin = -12.000000
	int m_nField = 4
	string m_Notes = ""
}

C_OP_OscillateScalar C_OP_OscillateScalar_2
{
	float m_FrequencyMin = 0.200000
	float m_RateMax = 12.000000
	float m_RateMin = -12.000000
	int m_nField = 12
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 0.800000
	float m_fLifetimeMin = 0.050000
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 8.000000
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	int m_nControlPointNumber = 3
	float m_fRadiusMin = 24.000000
	float m_fRadiusMax = 32.000000
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 63
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 255.000000
	string m_Notes = ""
}