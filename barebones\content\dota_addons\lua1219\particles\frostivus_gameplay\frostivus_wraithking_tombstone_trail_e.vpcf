<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 128
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 32.000000
	int(4) m_ConstantColor = ( 12, 245, 116, 125 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderRopes_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_DistanceToCP_0,
		&C_OP_FadeInSimple_0,
		&C_OP_VectorNoise_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RemapParticleCountToScalar_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderRopes C_OP_RenderRopes_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\bendibeam2.vtex"
	string m_Notes = ""
	float m_flTextureVScrollRate = -0.100000
	float m_flTextureVWorldSize = 806.451599
	int m_nMaxTesselation = 3
	int m_nMinTesselation = 3
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float m_fDrag = 0.050000
	float(3) m_Gravity = ( 0.000000, 0.000000, 25.000000 )
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	int(4) m_ColorFade = ( 0, 88, 46, 255 )
	float m_flFadeEndTime = 0.600000
	bool m_bEaseInOut = false
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flBias = 0.800000
	float m_flEndScale = 0.000000
	float m_flStartScale = 3.000000
}

C_OP_DistanceToCP C_OP_DistanceToCP_0
{
	string m_Notes = ""
	int m_nStartCP = 3
	int m_nFieldOutput = 16
	float m_flInputMax = 256.000000
	float m_flInputMin = 64.000000
	int m_nOpEndCapState = 1
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.125000
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	string m_Notes = ""
	bool m_bAdditive = true
	float(3) m_vecOutputMax = ( 1.000000, 1.000000, 0.000000 )
	float(3) m_vecOutputMin = ( -1.000000, -1.000000, 0.000000 )
	int m_nFieldOutput = 0
	float m_fl4NoiseScale = 0.050000
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.400000
	float m_fLifetimeMax = 0.400000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 3
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_0
{
	string m_Notes = ""
	float m_flOutputMax = 48.000000
	int m_nInputMax = 2
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 40.000000
}