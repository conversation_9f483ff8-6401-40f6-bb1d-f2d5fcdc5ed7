<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 128
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 68, 199, 114, 55 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderTrails_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_LockToBone_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_RampScalarLinear_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_LerpEndCapScalar_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomRadius_0,
		&C_INIT_CreateOnModel_0,
		&C_INIT_RandomTrailLength_0,
		&C_INIT_OffsetVectorToVector_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomScalar_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomAlpha_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderTrails C_OP_RenderTrails_0
{
	float m_flSelfIllumAmount = 4.000000
	string m_hTexture = "materials\\particle\\beam_jagged_01.vtex"
	int m_nVertCropField = 18
	float m_flLengthFadeInTime = 2.000000
	string m_Notes = ""
	bool m_bIgnoreDT = true
	int(4) m_trailTint = ( 0, 128, 0, 255 )
	float m_flTrailEndAlpha = -0.000000
}

C_OP_LockToBone C_OP_LockToBone_0
{
	string m_Notes = ""
	int m_nOpEndCapState = 0
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.750000
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
}

C_OP_RampScalarLinear C_OP_RampScalarLinear_0
{
	float m_RateMin = -1.000000
	int m_nField = 18
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flBias = 0.650000
	float m_flEndScale = 0.000000
	string m_Notes = ""
}

C_OP_LerpEndCapScalar C_OP_LerpEndCapScalar_0
{
	float m_flOutput = 0.000000
	int m_nFieldOutput = 16
	float m_flLerpTime = 0.200000
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 98.000000
	float m_flRadiusMin = 50.000000
	string m_Notes = ""
}

C_INIT_CreateOnModel C_INIT_CreateOnModel_0
{
	bool m_bLocalCoords = true
	float(3) m_vecDirectionBias = ( 0.000000, 0.000000, 0.180000 )
	string m_Notes = ""
	float m_flHitBoxScale = 0.700000
}

C_INIT_RandomTrailLength C_INIT_RandomTrailLength_0
{
	float m_flMaxLength = 3.000000
	string m_Notes = ""
	float m_flMinLength = 1.000000
}

C_INIT_OffsetVectorToVector C_INIT_OffsetVectorToVector_0
{
	string m_Notes = ""
	int m_nFieldOutput = 2
	float(3) m_vecOutputMin = ( 0.000000, 0.000000, 420.000000 )
	float(3) m_vecOutputMax = ( 0.000000, 0.000000, 545.000000 )
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 1.250000
	float m_fLifetimeMax = 2.000000
}

C_INIT_RandomScalar C_INIT_RandomScalar_0
{
	int m_nFieldOutput = 18
	float m_flMax = 2.000000
	string m_Notes = ""
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	int m_nFieldOutput = 20
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	float m_flAlphaRandExponent = 2.500000
	int m_nAlphaMax = 30
	int m_nAlphaMin = 5
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 80.000000
}