<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 1
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 0.650000
	int m_nConstantSequenceNumber1 = 1
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderModels_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_FadeInSimple_0,
		&C_OP_EndCapTimedDecay_0,
		&C_OP_RemapScalarOnceTimed_0,
		&C_OP_ReinitializeScalarEndCap_0,
		&C_OP_LerpEndCapScalar_0,
		&C_OP_LerpEndCapScalar_2,
		&C_OP_PositionLock_0,
		&C_OP_Orient2DRelToCP_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_PositionOffset_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderModels C_OP_RenderModels_0
{
	bool m_bOrientZ = true
	bool m_bResetAnimOnStop = true
	string m_ActivityName = ""
	string m_Notes = ""
	string m_EconSlotName = ""
	string m_hOverrideMaterial = ""
	ModelReference_t[] m_ModelList = 
	[
		ModelReference_t
		{
			string m_model = "models/heroes/omniknight/omniknightwings.vmdl"
		}
	]
	bool m_bAnimated = true
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 0.100000
	string m_Notes = ""
}

C_OP_EndCapTimedDecay C_OP_EndCapTimedDecay_0
{
	float m_flDecayTime = 0.250000
	string m_Notes = ""
}

C_OP_RemapScalarOnceTimed C_OP_RemapScalarOnceTimed_0
{
	float m_flOutputMax = 2.000000
	float m_flOutputMin = 2.000000
	int m_nFieldOutput = 13
	float m_flInputMax = 10.000000
	int m_nFieldInput = 13
	float m_flRemapTime = 1.800000
	int m_nOpEndCapState = 0
	string m_Notes = ""
}

C_OP_ReinitializeScalarEndCap C_OP_ReinitializeScalarEndCap_0
{
	float m_flOutputMax = 4.000000
	float m_flOutputMin = 4.000000
	int m_nFieldOutput = 13
	string m_Notes = ""
}

C_OP_LerpEndCapScalar C_OP_LerpEndCapScalar_0
{
	float m_flOutput = 0.000000
	float m_flLerpTime = 0.250000
	int m_nOpEndCapState = 1
	string m_Notes = ""
}

C_OP_LerpEndCapScalar C_OP_LerpEndCapScalar_2
{
	float m_flOutput = 0.000000
	int m_nFieldOutput = 7
	float m_flLerpTime = 0.250000
	int m_nOpEndCapState = 1
	bool m_bDisableOperator = true
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	bool m_bLockRot = true
	string m_Notes = ""
}

C_OP_Orient2DRelToCP C_OP_Orient2DRelToCP_0
{
	float m_flRotOffset = -90.000000
	int m_nFieldOutput = 12
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMin = 7.000000
	float m_fLifetimeMax = 7.000000
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	float(3) m_OffsetMin = ( -2.000000, 0.000000, 0.000000 )
	float(3) m_OffsetMax = ( -2.000000, 0.000000, 0.000000 )
	bool m_bLocalCoords = true
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 1
	string m_Notes = ""
}