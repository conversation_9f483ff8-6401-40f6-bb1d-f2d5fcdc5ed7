<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 4
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 44, 248, 149, 155 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_ColorInterpolate_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_VelocityRandom_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/frostivus_wraithking_tombstone_launch_smoke.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/frostivus_wraithking_tombstone_launch_b.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	symbol m_nSequenceCombineMode = SEQUENCE_COMBINE_MODE_ALPHA_FROM0_RGB_FROM_1
	int m_bAdditive = 1
	float m_flZoomRate1 = 4.000000
	bool m_bMaxLuminanceBlendingSequence0 = false
	bool m_bMaxLuminanceBlendingSequence1 = true
	string m_hTexture = "materials\\particle\\smoke3\\smoke3b.vtex"
	string m_Notes = ""
	float m_flAnimationRate = 2.000000
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float m_fDrag = 0.050000
	float(3) m_Gravity = ( 0.000000, 0.000000, 100.000000 )
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flStartScale = 0.000000
	float m_flBias = 0.700000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 1.000000
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	int(4) m_ColorFade = ( 63, 38, 38, 255 )
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMax = 1.000000
	float m_fLifetimeMin = 0.400000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	float m_fSpeedMax = 100.000000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMin = 44.000000
	float m_flRadiusMax = 72.000000
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	string m_Notes = ""
	int m_nSequenceMax = 8
}

C_INIT_VelocityRandom C_INIT_VelocityRandom_0
{
	string m_Notes = ""
	float(3) m_LocalCoordinateSystemSpeedMax = ( 230.000000, 0.000000, 0.000000 )
	float(3) m_LocalCoordinateSystemSpeedMin = ( 200.000000, 0.000000, 0.000000 )
	int m_nControlPointNumber = 3
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 4
}