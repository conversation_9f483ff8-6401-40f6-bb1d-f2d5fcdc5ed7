<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 512
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 20.000000
	int(4) m_ConstantColor = ( 255, 255, 255, 155 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_VectorNoise_0,
		&C_OP_FadeOutSimple_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomColor_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomYaw_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_Orient2DRelToCP_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	string m_hTexture = "materials\\particle\\smoke\\steam\\steam.vtex"
	string m_Notes = ""
	float m_flAnimationRate = 1.000000
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float m_fDrag = 0.100000
	string m_Notes = ""
	float(3) m_Gravity = ( 0.000000, 0.000000, -200.000000 )
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flBias = 0.350000
	float m_flEndScale = 2.000000
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	string m_Notes = ""
	float m_fl4NoiseScale = 0.500000
	int m_nFieldOutput = 0
	float(3) m_vecOutputMin = ( -55.000000, -55.000000, -55.000000 )
	float(3) m_vecOutputMax = ( 55.000000, 55.000000, 55.000000 )
	bool m_bAdditive = true
	bool m_bOffset = true
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 1.000000
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.250000
	float m_fLifetimeMax = 2.000000
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 0, 43, 29, 255 )
	int(4) m_ColorMax = ( 0, 0, 0, 255 )
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	float m_fSpeedMax = 320.000000
	float m_fRadiusMax = 2.000000
	int m_nControlPointNumber = 3
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	string m_Notes = ""
	bool m_bLocalSpace = true
	float(3) m_vecOutputMin = ( -24.000000, -24.000000, -24.000000 )
	float(3) m_vecOutputMax = ( 24.000000, 24.000000, 24.000000 )
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomYaw C_INIT_RandomYaw_0
{
	string m_Notes = ""
	float m_flDegreesMin = -4.000000
	float m_flDegreesMax = 4.000000
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	string m_Notes = ""
	int m_nSequenceMax = 1
}

C_INIT_Orient2DRelToCP C_INIT_Orient2DRelToCP_0
{
	string m_Notes = ""
	float m_flRotOffset = 180.000000
	int m_nCP = 3
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 256.000000
}