<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 32
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 2.000000
	int(4) m_ConstantColor = ( 0, 0, 0, 255 )
	int m_nConstantSequenceNumber = 3
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderTrails_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_InterpolateRadius_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_OffsetVectorToVector_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		&C_OP_AttractToControlPoint_0
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderTrails C_OP_RenderTrails_0
{
	int m_bAdditive = 0
	string m_hTexture = "materials\\particle\\sparks\\sparks.vtex"
	string m_Notes = ""
	float m_flMaxLength = 128.000000
	bool m_bIgnoreDT = true
	int(4) m_trailTint = ( 255, 255, 255, 255 )
	float m_flTrailEndAlpha = 0.000000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 0.000000
	float m_flBias = 0.250000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMax = 0.100000
	float m_fLifetimeMin = 0.100000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 3
	float(3) m_LocalCoordinateSystemSpeedMin = ( 330.000000, 0.000000, 0.000000 )
	float(3) m_LocalCoordinateSystemSpeedMax = ( 130.000000, 0.000000, 0.000000 )
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMax = 24.000000
	float m_flRadiusMin = 8.000000
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
}

C_INIT_OffsetVectorToVector C_INIT_OffsetVectorToVector_0
{
	string m_Notes = ""
	int m_nFieldOutput = 2
	float(3) m_vecOutputMin = ( -700.000000, -700.000000, -700.000000 )
	float(3) m_vecOutputMax = ( 700.000000, 700.000000, 700.000000 )
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 32
}

C_OP_AttractToControlPoint C_OP_AttractToControlPoint_0
{
	string m_Notes = ""
	float m_fForceAmount = 700.000000
	float m_fFalloffPower = 0.000000
}