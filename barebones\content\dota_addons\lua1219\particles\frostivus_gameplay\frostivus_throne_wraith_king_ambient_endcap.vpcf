<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 120
	string m_pszSnapshotName = ""
	float(3) m_BoundingBoxMax = ( 64.000000, 64.000000, 64.000000 )
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 32.000000
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_Noise_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_VectorNoise_0,
		&C_OP_OscillateVector_0,
		&C_OP_BasicMovement_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_CreateOnModel_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_InitialVelocityNoise_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/frostivus_throne_wraith_king_explode.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	bool m_bDisableZBuffering = true
	string m_hTexture = "materials\\particle\\yellowflare2.vtex"
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_Noise C_OP_Noise_0
{
	string m_Notes = ""
	bool m_bAdditive = true
	float m_flOutputMax = 13.000000
	int m_nFieldOutput = 4
	float m_fl4NoiseScale = 0.001310
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flStartScale = 4.000000
	float m_flBias = 0.750000
	float m_flEndScale = 0.000000
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	bool m_bOffset = true
	bool m_bAdditive = true
	float(3) m_vecOutputMax = ( 128.000000, 128.000000, 128.000000 )
	float(3) m_vecOutputMin = ( -128.000000, -128.000000, -128.000000 )
	int m_nFieldOutput = 0
	string m_Notes = ""
}

C_OP_OscillateVector C_OP_OscillateVector_0
{
	float(3) m_FrequencyMax = ( 2.000000, 2.000000, 2.000000 )
	float(3) m_FrequencyMin = ( 0.200000, 0.200000, 0.200000 )
	float(3) m_RateMax = ( 64.000000, 64.000000, 64.000000 )
	float(3) m_RateMin = ( -64.000000, -64.000000, -64.000000 )
	bool m_bOffset = true
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.500000
	float m_fLifetimeMax = 0.750000
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 80, 227, 173, 255 )
	int(4) m_ColorMax = ( 120, 255, 199, 255 )
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_CreateOnModel C_INIT_CreateOnModel_0
{
	string m_Notes = ""
	float m_flHitBoxScale = 0.900000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 64.000000
	float m_flRadiusMin = 32.000000
	string m_Notes = ""
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	float(3) m_vecOutputMax = ( 64.000000, 64.000000, 64.000000 )
	float(3) m_vecOutputMin = ( -64.000000, -64.000000, -64.000000 )
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
}