<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 64
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flNoDrawTimeToGoToSleep = 12.000000
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_FadeInSimple_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_SpinUpdate_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_PositionLock_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomColor_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_RandomSecondSequence_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRotationSpeed_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0,
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	symbol m_nSequenceCombineMode = SEQUENCE_COMBINE_MODE_ALPHA_FROM0_RGB_FROM_1
	float m_flZoomRate1 = 4.000000
	bool m_bMaxLuminanceBlendingSequence0 = false
	bool m_bMaxLuminanceBlendingSequence1 = true
	float m_flStartFadeSize = 1.000000
	float m_flEndFadeSize = 1.400000
	bool m_bDisableZBuffering = true
	string m_hTexture = "materials\\particle\\smoke3\\smoke3b.vtex"
	int m_nOrientationType = 2
	float m_flAnimationRate = 0.000000
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float(3) m_Gravity = ( 0.000000, 0.000000, 25.000000 )
	float m_fDrag = 0.001000
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flStartScale = 3.000000
	float m_flEndScale = 1.500000
	float m_flBias = 0.750000
	string m_Notes = ""
}

C_OP_SpinUpdate C_OP_SpinUpdate_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.750000
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMin = ( 29, 155, 221, 255 )
	int(4) m_ColorMax = ( 117, 175, 241, 255 )
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMin = 2.000000
	float m_fLifetimeMax = 2.250000
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMin = 50.000000
	float m_flRadiusMax = 75.000000
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	int m_nAlphaMax = 10
	int m_nAlphaMin = 5
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 5
	string m_Notes = ""
}

C_INIT_RandomSecondSequence C_INIT_RandomSecondSequence_0
{
	int m_nSequenceMin = 9
	int m_nSequenceMax = 10
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	float m_fRadiusMin = 0.500000
	float m_fRadiusMax = 1.000000
	float(3) m_vecDistanceBias = ( 1.000000, 1.000000, 0.000000 )
	int m_nScaleCP = 1
	string m_Notes = ""
}

C_INIT_RandomRotationSpeed C_INIT_RandomRotationSpeed_0
{
	float m_flDegreesMin = 14.000000
	float m_flDegreesMax = 24.000000
	bool m_bRandomlyFlipDirection = false
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 30.000000
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 12
	string m_Notes = ""
}