<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 4
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 1.500000
	int(4) m_ConstantColor = ( 0, 0, 0, 255 )
	int m_nConstantSequenceNumber1 = 1
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderProjected_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_BasicMovement_0,
		&C_OP_RampScalarSpline_0,
		&C_OP_RampScalarSpline_2,
		&C_OP_Decay_2,
		&C_OP_FadeOut_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomScalar_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RemapCPtoScalar_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_DistanceToCPInit_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_PositionOffset_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/frostivus_leshrac_split_projected_scortch.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/frostivus_lightbeam.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/frostivus_leshrac_split_pulse.vpcf"
		}
	]
}

C_OP_RenderProjected C_OP_RenderProjected_0
{
	string m_Notes = ""
	string m_hProjectedMaterial = "materials\\particle\\projected_cracks.vmat"
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	bool m_bDisableOperator = true
	float m_fDrag = 0.100000
	string m_Notes = ""
}

C_OP_RampScalarSpline C_OP_RampScalarSpline_0
{
	float m_flStartTime_max = 0.700000
	float m_flStartTime_min = 0.600000
	int m_nField = 13
	float m_RateMin = 2.000000
	float m_RateMax = 3.000000
	float m_flEndTime_min = 0.900000
	string m_Notes = ""
}

C_OP_RampScalarSpline C_OP_RampScalarSpline_2
{
	bool m_bEaseOut = true
	float m_RateMax = -1.800000
	float m_RateMin = -1.000000
	int m_nField = 13
	float m_flEndTime_min = 0.095000
	float m_flEndTime_max = 0.220000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_2
{
	string m_Notes = ""
}

C_OP_FadeOut C_OP_FadeOut_0
{
	bool m_bEaseInAndOut = false
	bool m_bProportional = false
	float m_flFadeOutTimeMax = 1.000000
	float m_flFadeOutTimeMin = 1.000000
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	float m_flDegreesMax = 0.000000
	string m_Notes = ""
}

C_INIT_RandomScalar C_INIT_RandomScalar_0
{
	int m_nFieldOutput = 13
	float m_flMax = 1.310000
	float m_flMin = 1.310000
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 4.500000
	float m_fLifetimeMin = 4.000000
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
}

C_INIT_RemapCPtoScalar C_INIT_RemapCPtoScalar_0
{
	float m_flOutputMax = 900.000000
	float m_flOutputMin = 100.000000
	float m_flInputMax = 900.000000
	float m_flInputMin = 100.000000
	int m_nCPInput = 1
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
}

C_INIT_DistanceToCPInit C_INIT_DistanceToCPInit_0
{
	float m_flOutputMin = 1.000000
	float m_flInputMax = 450.000000
	float m_flInputMin = 1.000000
	bool m_bScaleInitialRange = true
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 5.000000 )
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 5.000000 )
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 3
	string m_Notes = ""
}