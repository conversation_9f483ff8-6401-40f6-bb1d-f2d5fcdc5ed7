<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 100
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 15.000000
	int(4) m_ConstantColor = ( 255, 0, 0, 255 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_ColorInterpolate_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RemapScalar_0,
		&C_INIT_CreateFromParentParticles_0,
		&C_INIT_PositionOffset_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\yellowflare2.vtex"
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 0.000000
	float m_flBias = 0.400000
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.750000
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	float m_flFadeEndTime = 0.600000
	int(4) m_ColorFade = ( 98, 96, 62, 255 )
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.250000
	float m_fLifetimeMax = 0.250000
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 255, 235, 235, 255 )
	int(4) m_ColorMax = ( 255, 20, 14, 255 )
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMin = 80.000000
	float m_flRadiusMax = 80.000000
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
	int m_nAlphaMin = 50
	int m_nAlphaMax = 100
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RemapScalar C_INIT_RemapScalar_0
{
	string m_Notes = ""
	float m_flInputMin = 0.100000
	float m_flInputMax = 0.200000
	float m_flOutputMin = 1.000000
	float m_flOutputMax = 3.000000
	bool m_bScaleInitialRange = true
}

C_INIT_CreateFromParentParticles C_INIT_CreateFromParentParticles_0
{
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMax = ( 15.000000, 0.000000, 0.000000 )
	bool m_bLocalCoords = true
	float(3) m_OffsetMin = ( 15.000000, 0.000000, 0.000000 )
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 350.000000
	float m_flEmissionDuration = 0.150000
}