<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 128
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 100.000000
	int(4) m_ConstantColor = ( 216, 239, 255, 150 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderTrails_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_RampScalarLinear_0,
		&C_OP_FadeInSimple_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_RampScalarLinearSimple_2,
		&C_OP_InterpolateRadius_0,
		&C_OP_InterpolateRadius_2
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomTrailLength_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomColor_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0,
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderTrails C_OP_RenderTrails_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\particle_cone_gradient_2.vtex"
	bool m_bIgnoreDT = true
	string m_Notes = ""
	float m_flAnimationRate = 30.000000
	float m_flLengthFadeInTime = 2.000000
	int(4) m_trailTint = ( 255, 255, 255, 255 )
	float m_flTrailEndAlpha = 0.000000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	float m_flFadeStartTime = 0.500000
	int(4) m_ColorFade = ( 255, 181, 108, 255 )
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.500000
	string m_Notes = ""
}

C_OP_RampScalarLinear C_OP_RampScalarLinear_0
{
	float m_flStartTime_max = 0.350000
	float m_flStartTime_min = 0.250000
	string m_Notes = ""
	int m_nField = 10
	float m_RateMin = -0.500000
	float m_RateMax = -1.000000
	float m_flEndTime_min = 999999.000000
	float m_flEndTime_max = 999999.000000
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	string m_Notes = ""
	int m_nField = 12
	float m_Rate = 5.000000
	float m_flEndTime = 100000000.000000
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_2
{
	int m_nOpEndCapState = 1
	string m_Notes = ""
	int m_nField = 7
	float m_Rate = -1.000000
	float m_flEndTime = 999999.000000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flEndTime = 0.500000
	float m_flEndScale = 2.000000
	string m_Notes = ""
	float m_flStartScale = 0.000000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_2
{
	string m_Notes = ""
	float m_flStartTime = 0.600000
	float m_flStartScale = 2.000000
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 4.000000
	float m_fLifetimeMin = 3.000000
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 1000.000000 )
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 1000.000000 )
	bool m_bLocalCoords = true
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	string m_Notes = ""
	float(3) m_vecOutputMin = ( 0.000000, 0.000000, 800.000000 )
	float(3) m_vecOutputMax = ( 0.000000, 0.000000, 1000.000000 )
	bool m_bLocalSpace = true
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMin = 100.000000
	float m_flRadiusMax = 625.000000
}

C_INIT_RandomTrailLength C_INIT_RandomTrailLength_0
{
	float m_flMaxLength = 80.000000
	string m_Notes = ""
	float m_flMinLength = 60.000000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	float m_fRadiusMax = -25.000000
	int m_nControlPointNumber = 7
	float m_fSpeedMin = 100.000000
	float m_fSpeedMax = 150.000000
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
	int m_nAlphaMin = 25
	int m_nAlphaMax = 50
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 255, 248, 208, 255 )
	int(4) m_ColorMax = ( 255, 209, 137, 255 )
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 4.000000
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 5
}