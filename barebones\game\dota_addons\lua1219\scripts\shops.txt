"dota_shops"
{
	"consumables"
	{
		"item"      "item_tpscroll"
		"item"      "item_clarity"
		"item"      "item_faerie_fire"
		"item"      "item_smoke_of_deceit"
		"item"      "item_ward_observer"
		"item"      "item_ward_sentry"
		"item"      "item_enchanted_mango"
		"item"      "item_flask"
		"item"      "item_tango"
		"item"      "item_tome_of_knowledge"
		"item"      "item_dust"
		"item"      "item_bottle"
		"item"      "item_aghanims_shard"
		//"item"    "item_divine_potion"
		//"item"    "item_telescope"
		//"item"    "item_blinders"
		//"item"    "item_red_mist"
		//"item"    "item_bounty_pact"
	}

	"attributes"
	{
		"item"		"item_branches"

		"item"		"item_gauntlets"
		"item"		"item_slippers"
		"item"		"item_mantle"

		"item"		"item_circlet"

		"item"		"item_belt_of_strength"
		"item"		"item_boots_of_elves"
		"item"		"item_robe"

		"item"		"item_crown"

		"item"		"item_ogre_axe"
		"item"		"item_blade_of_alacrity"
		"item"		"item_staff_of_wizardry"
		"item"		"item_ultimate_orb"

		//"item"	"item_river_painter7"
		//"item"	"item_river_painter6"
		//"item"	"item_river_painter5"
		//"item"	"item_river_painter4"
		//"item"	"item_river_painter3"
		//"item"	"item_river_painter2"
		//"item"	"item_river_painter"
	}

	"weapons_armor"
	{
		"item"      "item_quelling_blade"
		"item"      "item_ring_of_protection"
		"item"      "item_infused_raindrop"
		"item"      "item_orb_of_venom"
		"item"      "item_blight_stone"
		"item"      "item_blades_of_attack"
		"item"      "item_gloves"
		"item"      "item_chainmail"
		"item"      "item_quarterstaff"
		"item"      "item_helm_of_iron_will"
		"item"      "item_broadsword"
		"item"      "item_blitz_knuckles"
		"item"      "item_javelin"
		"item"      "item_claymore"
		"item"      "item_mithril_hammer"
		//"item"	"item_sturdy_vest"
	}

	"misc"
	{
		"item"      "item_ring_of_regen"
		"item"      "item_sobi_mask"
		"item"      "item_magic_stick"
		"item"      "item_fluffy_hat"
		"item"      "item_wind_lace"
		"item"      "item_cloak"
		"item"      "item_boots"
		//"item"    "item_ring_of_health"
		//"item"    "item_void_stone"
		"item"      "item_gem"
		"item"      "item_lifesteal"
		"item"      "item_voodoo_mask"
		"item"      "item_shadow_amulet"
		"item"      "item_ghost"
		"item"      "item_blink"
		//"item"	"item_whip"
		//"item"	"item_telescope"
	}
	
	// Level 1 - Green Recipes
	"basics"
	{
		"item"      "item_magic_wand"
		"item"      "item_null_talisman"
		"item"      "item_wraith_band"
		"item"      "item_bracer"
		"item"      "item_soul_ring"
		"item"      "item_orb_of_corrosion"
		"item"      "item_falcon_blade"
		"item"      "item_power_treads"
		"item"      "item_phase_boots"
		"item"      "item_oblivion_staff"
		"item"      "item_pers"
		"item"      "item_mask_of_madness"
		"item"      "item_hand_of_midas"
		"item"      "item_helm_of_the_dominator"
		"item"      "item_travel_boots"
		"item"      "item_moon_shard"
		"item"      "item_helm_of_the_overlord"
	}

	// Level 2 - Blue Recipes
	"support"
	{
		"item"      "item_buckler"
		"item"      "item_ring_of_basilius"
		"item"      "item_headdress"
		"item"      "item_urn_of_shadows"
		"item"      "item_tranquil_boots"
		"item"      "item_medallion_of_courage"
		"item"      "item_arcane_boots"
		"item"      "item_ancient_janggo"
		"item"      "item_mekansm"
		"item"      "item_holy_locket"
		"item"      "item_vladmir"
		"item"      "item_spirit_vessel"
		"item"      "item_pipe"
		"item"      "item_guardian_greaves"
		"item"		"item_boots_of_bearing"
		"item"		"item_wraith_pact"
		//"item"    "item_natures_mend"
		//"item"    "item_nightfall_striders"
	}

	// Arcane Recipes
	"magics"
	{
		"item"      "item_veil_of_discord"
		"item"      "item_glimmer_cape"
		//"item"      "item_necronomicon"
		"item"      "item_force_staff"
		"item"      "item_aether_lens"
		"item"      "item_witch_blade"
		"item"      "item_cyclone"
		"item"      "item_rod_of_atos"
		"item"      "item_dagon"
		"item"      "item_orchid"
		"item"      "item_solar_crest"
		"item"      "item_ultimate_scepter"
		"item"      "item_refresher"
		"item"      "item_octarine_core"
		"item"      "item_sheepstick"
		"item"      "item_gungir"
		"item"      "item_wind_waker"
		//"item"    "item_rune_breaker"
		//"item"    "item_aether_staff"
	}
		
	// Level 3 - Purple Recipes	
	"defense"
	{
		"item"      "item_hood_of_defiance"
		"item"      "item_vanguard"
		"item"      "item_blade_mail"
		"item"      "item_aeon_disk"
		"item"      "item_soul_booster"
		"item"      "item_eternal_shroud"
		"item"      "item_crimson_guard"
		"item"      "item_lotus_orb"
		"item"      "item_black_king_bar"
		"item"      "item_hurricane_pike"
		"item"      "item_manta"
		"item"      "item_sphere"
		"item"      "item_shivas_guard"
		"item"      "item_heart"
		"item"      "item_assault"
		"item"      "item_bloodstone"
		//"item"    "item_skybreaker"
		//"item"    "item_spirit_helix"
	}

	// Weapon Recipes
	"weapons"
	{
		"item"      "item_lesser_crit"
		"item"      "item_meteor_hammer"
		"item"      "item_armlet"
		"item"      "item_basher"
		"item"      "item_desolator"
		"item"      "item_bfury"
		"item"      "item_ethereal_blade"
		"item"      "item_nullifier"
		"item"      "item_monkey_king_bar"
		"item"      "item_butterfly"
		"item"      "item_radiance"
		"item"      "item_greater_crit"
		"item"      "item_silver_edge"
		"item"      "item_rapier"
		"item"      "item_bloodthorn"
		"item"      "item_abyssal_blade"
		"item"		"item_revenants_brooch"
	}

	// Level 4 - Orange / Orb / Artifacts				
	"artifacts"
	{
		"item"      "item_dragon_lance"
		"item"      "item_sange"
		"item"      "item_yasha"
		"item"      "item_kaya"
		"item"      "item_echo_sabre"
		"item"      "item_maelstrom"
		"item"      "item_diffusal_blade"
		"item"      "item_mage_slayer"
		"item"      "item_heavens_halberd"
		"item"      "item_kaya_and_sange"
		"item"      "item_sange_and_yasha"
		"item"      "item_yasha_and_kaya"
		"item"      "item_satanic"
		"item"      "item_skadi"
		"item"      "item_mjollnir"
		"item"      "item_overwhelming_blink"
 		"item"      "item_swift_blink"
 		"item"      "item_arcane_blink"
		//"item"    "item_venomous_shield"
		//"item"    "item_jade_bow"
	}

	// "sideshop1"
	// {
		// "item"		"item_tpscroll"
		// "item"		"item_ring_of_protection"
		// "item"		"item_magic_stick"
		// "item"		"item_quelling_blade"
		// "item"		"item_boots"
		// "item"		"item_boots_of_elves"
		// "item"		"item_belt_of_strength"
		// "item"		"item_robe"
		// "item"		"item_crown"
	// }

	// "sideshop2"
	// {
		// "item"		"item_gloves"
		// "item"		"item_chainmail"
		// "item"		"item_cloak"
		// "item"		"item_void_stone"
		// "item"		"item_helm_of_iron_will"
		// "item"		"item_energy_booster"
		// "item"		"item_vitality_booster"
		// "item"		"item_lifesteal"
		// "item"		"item_broadsword"
		// "item"		"item_blink"
	// }

	"secretshop"
	{
		"item"		"item_ring_of_health"
		"item"		"item_void_stone"
		"item"		"item_energy_booster"
		"item"		"item_vitality_booster"
		"item"		"item_point_booster"
		"item"		"item_platemail"
		"item"		"item_talisman_of_evasion"
		"item"		"item_hyperstone"
		"item"		"item_ultimate_orb"
		"item"		"item_demon_edge"
		"item"		"item_mystic_staff"
		"item"		"item_reaver"
		"item"		"item_eagle"
		"item"		"item_relic"
	}

	// items during Strategy Time
	"pregame"
	{
		"item"		"item_clarity"
		"item"		"item_faerie_fire"
		"item"		"item_enchanted_mango"
		"item"		"item_tango"
		"item"		"item_flask"
		"item"		"item_smoke_of_deceit"
		"item"		"item_dust"
		"item"		"item_ward_observer"
		"item"		"item_ward_sentry"
		"item"		"item_branches"
		"item"		"item_gauntlets"
		"item"		"item_slippers"
		"item"		"item_mantle"
		"item"		"item_circlet"
		"item"		"item_recipe_magic_wand"
		"item"		"item_ring_of_protection"
		"item"		"item_quelling_blade"
		"item"		"item_blight_stone"
		"item"		"item_orb_of_venom"
		"item"		"item_wind_lace"
		"item"		"item_magic_stick"
		"item"		"item_sobi_mask"
		"item"		"item_ring_of_regen"
		"item"		"item_boots"
		"item"		"item_gloves"
		"item"		"item_crown"
		"item"		"item_wraith_band"
		"item"		"item_null_talisman"
		"item"		"item_bracer"
		"item"      "item_blades_of_attack"
		"item"      "item_ring_of_basilius"
		"item"      "item_buckler"
		"item"      "item_headdress"
	}
}
