<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 100
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 255, 206, 129, 255 )
	int m_nConstantSequenceNumber = 4
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderDeferredLight_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_FadeOutSimple_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_AlphaDecay_0,
		&C_OP_SetControlPointPositions_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomRadius_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_CreateSequentialPath_0,
		&C_INIT_RandomLifeTime_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderDeferredLight C_OP_RenderDeferredLight_0
{
	string m_Notes = ""
	float m_flRadiusScale = 1.500000
	float m_flAlphaScale = 15.000000
	float m_flStartFalloff = 0.100000
	int(4) m_ColorScale = ( 255, 210, 140, 255 )
	string m_hTexture = "materials\\particle\\particle_flares\\aircraft_white.vtex"
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.500000
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flEndScale = 1.500000
	string m_Notes = ""
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	float m_flEndTime = 9999999827968.000000
	float m_Rate = -3.000000
	int m_nField = 7
	string m_Notes = ""
	int m_nOpEndCapState = 1
}

C_OP_AlphaDecay C_OP_AlphaDecay_0
{
	float m_flMinAlpha = 0.001000
	string m_Notes = ""
}

C_OP_SetControlPointPositions C_OP_SetControlPointPositions_0
{
	float(3) m_vecCP4Pos = ( 0.000000, -50.000000, 0.000000 )
	string m_Notes = ""
	int m_nCP1 = 5
	float(3) m_vecCP1Pos = ( 0.000000, -50.000000, 0.000000 )
	int m_nCP2 = 6
	float(3) m_vecCP2Pos = ( 0.000000, 50.000000, 0.000000 )
	int m_nCP3 = 9
	int m_nCP4 = 9
	bool m_bOrient = true
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 100.000000
	float m_flRadiusMin = 100.000000
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMin = ( 75.000000, 0.000000, 50.000000 )
	float(3) m_OffsetMax = ( 75.000000, 0.000000, 50.000000 )
	bool m_bLocalCoords = true
}

C_INIT_CreateSequentialPath C_INIT_CreateSequentialPath_0
{
	float m_flNumToAssign = 3.000000
	string m_Notes = ""
	CPathParameters m_PathParams = CPathParameters
	{
		int m_nStartControlPointNumber = 5
		int m_nEndControlPointNumber = 6
	}
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 6.000000
	float m_fLifetimeMin = 6.000000
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 3
	string m_Notes = ""
}