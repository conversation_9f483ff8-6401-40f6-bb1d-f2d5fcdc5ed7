<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 250
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 0.500000
	int(4) m_ConstantColor = ( 255, 206, 89, 255 )
	int m_nConstantSequenceNumber1 = 1
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderModels_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Orient2DRelToCP_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_LerpEndCapScalar_0,
		&C_OP_EndCapTimedDecay_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RingWave_0,
		&C_INIT_VelocityRandom_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomTrailLength_0,
		&C_INIT_PositionPlaceOnGround_0,
		&C_INIT_RandomSecondSequence_0,
		&C_INIT_RandomScalar_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderModels C_OP_RenderModels_0
{
	float m_flAnimationRate = 60.000000
	string m_Notes = ""
	bool m_bIgnoreNormal = true
	string m_ActivityName = ""
	int m_nManualFrameField = 18
	string m_EconSlotName = ""
	string m_hOverrideMaterial = ""
	ModelReference_t[] m_ModelList = 
	[
		ModelReference_t
		{
			string m_model = "models/particle/legion_duel_banner.vmdl"
		}
	]
	bool m_bAnimated = true
}

C_OP_Orient2DRelToCP C_OP_Orient2DRelToCP_0
{
	string m_Notes = ""
	int m_nFieldOutput = 12
	int m_nCP = 7
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flStartScale = 0.000000
}

C_OP_LerpEndCapScalar C_OP_LerpEndCapScalar_0
{
	string m_Notes = ""
	float m_flLerpTime = 0.250000
	float m_flOutput = 0.000000
}

C_OP_EndCapTimedDecay C_OP_EndCapTimedDecay_0
{
	string m_Notes = ""
	float m_flDecayTime = 0.500000
}

C_INIT_RingWave C_INIT_RingWave_0
{
	float m_flParticlesPerOrbit = 6.000000
	bool m_bEvenDistribution = true
	float m_flInitialRadius = 625.000000
	string m_Notes = ""
	int m_nControlPointNumber = 7
}

C_INIT_VelocityRandom C_INIT_VelocityRandom_0
{
	float(3) m_LocalCoordinateSystemSpeedMax = ( 0.000000, 0.000000, -10000.000000 )
	float(3) m_LocalCoordinateSystemSpeedMin = ( 0.000000, 0.000000, -7000.000000 )
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 0.500000
	float m_fLifetimeMin = 0.500000
	string m_Notes = ""
}

C_INIT_RandomTrailLength C_INIT_RandomTrailLength_0
{
	float m_flMaxLength = 1.500000
	float m_flMinLength = 1.500000
	string m_Notes = ""
}

C_INIT_PositionPlaceOnGround C_INIT_PositionPlaceOnGround_0
{
	string m_Notes = ""
	float m_flOffset = -10.000000
	bool m_bIncludeWater = true
}

C_INIT_RandomSecondSequence C_INIT_RandomSecondSequence_0
{
	int m_nSequenceMax = 2
	int m_nSequenceMin = 1
	string m_Notes = ""
}

C_INIT_RandomScalar C_INIT_RandomScalar_0
{
	string m_Notes = ""
	float m_flMax = 100.000000
	int m_nFieldOutput = 18
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 6
}