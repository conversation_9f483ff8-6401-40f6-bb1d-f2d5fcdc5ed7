<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 4
	string m_pszSnapshotName = ""
	float(3) m_BoundingBoxMax = ( 64.000000, 64.000000, 64.000000 )
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 15.000000
	int(4) m_ConstantColor = ( 255, 255, 255, 54 )
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_Noise_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_RampScalarLinearSimple_2,
		&C_OP_ColorInterpolate_0,
		&C_OP_LockToBone_0,
		&C_OP_Spin_0,
		&C_OP_LerpEndCapScalar_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomColor_0,
		&C_INIT_CreationNoise_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomYaw_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_CreateOnModel_0,
		&C_INIT_RandomSequence_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/frostivus_throne_wraith_king_ambient_c_b.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\smoke\\steam\\steam.vtex"
	float m_flAnimationRate = 0.400000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.500000
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.500000
}

C_OP_Noise C_OP_Noise_0
{
	bool m_bDisableOperator = true
	string m_Notes = ""
	bool m_bAdditive = true
	float m_flOutputMax = 130.000000
	int m_nFieldOutput = 4
	float m_fl4NoiseScale = 0.001310
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	string m_Notes = ""
	float m_flEndTime = 9999.000000
	float m_Rate = 150.000000
	int m_nOpEndCapState = 1
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_2
{
	string m_Notes = ""
	float m_flEndTime = 99999.000000
	float m_Rate = -8.000000
	int m_nField = 16
	int m_nOpEndCapState = 1
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	bool m_bEaseInOut = false
	string m_Notes = ""
	int(4) m_ColorFade = ( 98, 221, 164, 255 )
	float m_flFadeStartTime = 0.200000
}

C_OP_LockToBone C_OP_LockToBone_0
{
	string m_Notes = ""
	string m_HitboxSetName = "head"
	int m_nOpEndCapState = 0
}

C_OP_Spin C_OP_Spin_0
{
	int m_nSpinRateDegrees = 2
	string m_Notes = ""
}

C_OP_LerpEndCapScalar C_OP_LerpEndCapScalar_0
{
	float m_flOutput = 0.000000
	int m_nFieldOutput = 16
	float m_flLerpTime = 0.200000
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 2.000000
	float m_fLifetimeMax = 3.000000
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 0, 152, 96, 255 )
	int(4) m_ColorMax = ( 1, 79, 47, 255 )
}

C_INIT_CreationNoise C_INIT_CreationNoise_0
{
	string m_Notes = ""
	float m_flNoiseScale = 2.000000
	float m_flOutputMin = 12.000000
	float m_flOutputMax = 22.000000
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomYaw C_INIT_RandomYaw_0
{
	string m_Notes = ""
	float m_flDegreesMin = -84.000000
	float m_flDegreesMax = 84.000000
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_CreateOnModel C_INIT_CreateOnModel_0
{
	bool m_bLocalCoords = true
	float(3) m_vecDirectionBias = ( 4.000000, 0.000000, 0.000000 )
	string m_Notes = ""
	float m_flHitBoxScale = 0.700000
	string m_HitboxSetName = "head"
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 1
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 2.000000
}