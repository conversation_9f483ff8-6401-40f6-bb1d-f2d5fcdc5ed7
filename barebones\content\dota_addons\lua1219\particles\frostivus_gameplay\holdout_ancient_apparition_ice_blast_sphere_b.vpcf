<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 96
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 4.000000
	int(4) m_ConstantColor = ( 255, 255, 255, 55 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderModels_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_FadeInSimple_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_BasicMovement_0,
		&C_OP_PositionLock_0,
		&C_OP_OscillateScalar_0,
		&C_OP_OscillateScalar_2
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomScalar_0,
		&C_INIT_RandomScalar_2
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderModels C_OP_RenderModels_0
{
	string m_ActivityName = ""
	int m_nSkin = 7
	string m_Notes = ""
	string m_EconSlotName = ""
	string m_hOverrideMaterial = ""
	ModelReference_t[] m_ModelList = 
	[
		ModelReference_t
		{
			string m_model = "models/particle/sphere.vmdl"
		}
	]
	bool m_bAnimated = true
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 0.100000
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.300000
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float m_fDrag = 0.050000
	float(3) m_Gravity = ( 0.000000, 0.000000, 50.000000 )
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_OP_OscillateScalar C_OP_OscillateScalar_0
{
	float m_FrequencyMin = 0.250000
	float m_RateMax = 25.000000
	float m_RateMin = -25.000000
	int m_nField = 4
	string m_Notes = ""
}

C_OP_OscillateScalar C_OP_OscillateScalar_2
{
	float m_RateMax = 25.000000
	float m_RateMin = -25.000000
	int m_nField = 20
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 0.200000
	float m_fLifetimeMin = 0.050000
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMax = ( 55, 159, 160, 255 )
	int(4) m_ColorMin = ( 42, 60, 238, 255 )
	string m_Notes = ""
}

C_INIT_RandomScalar C_INIT_RandomScalar_0
{
	int m_nFieldOutput = 12
	float m_flMax = 1.000000
	string m_Notes = ""
}

C_INIT_RandomScalar C_INIT_RandomScalar_2
{
	int m_nFieldOutput = 20
	float m_flMax = 1.000000
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 32.000000
	string m_Notes = ""
}