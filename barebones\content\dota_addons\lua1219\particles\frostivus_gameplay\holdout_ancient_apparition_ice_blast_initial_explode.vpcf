<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 10
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 96.000000
	int m_nConstantSequenceNumber = 5
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_FadeInSimple_0,
		&C_OP_OscillateVector_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_BasicMovement_0,
		&C_OP_PositionLock_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_InterpolateRadius_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomSequence_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/holdout_ancient_apparition_ice_blast_explode_b.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/holdout_ancient_apparition_ice_blast_explode_c.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	bool m_bDisableZBuffering = true
	string m_hTexture = "materials\\particle\\lens_flare\\lens_flare.vtex"
	bool m_bDisableOperator = true
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 0.100000
	string m_Notes = ""
}

C_OP_OscillateVector C_OP_OscillateVector_0
{
	float(3) m_FrequencyMax = ( 3.000000, 3.000000, 3.000000 )
	float(3) m_RateMax = ( 3.000000, 3.000000, 3.000000 )
	float(3) m_RateMin = ( -3.000000, -3.000000, -3.000000 )
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.800000
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float m_fDrag = 0.050000
	float(3) m_Gravity = ( 0.000000, 0.000000, 50.000000 )
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	float m_flStartTime_max = 0.000000
	float m_flStartTime_min = 0.000000
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	int(4) m_ColorFade = ( 94, 62, 199, 255 )
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flStartScale = 0.250000
	float m_flBias = 0.750000
	float m_flEndScale = 2.000000
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 0.400000
	float m_fLifetimeMin = 0.150000
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMax = ( 160, 249, 255, 255 )
	int(4) m_ColorMin = ( 160, 249, 255, 255 )
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 4
	int m_nSequenceMin = 2
	bool m_bShuffle = true
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 10
	string m_Notes = ""
}