<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 150
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_BasicMovement_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_VectorNoise_0,
		&C_OP_SetSingleControlPointPosition_0,
		&C_OP_FadeOutSimple_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomColor_0,
		&C_INIT_VelocityRandom_0,
		&C_INIT_CreateWithinSphere_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		&C_OP_AttractToControlPoint_0
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_omnislash_ascension_sparkrays.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\yellowflare.vtex"
	string m_Notes = ""
	float m_flAnimationRate = 1.000000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float m_fDrag = 0.200000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flStartScale = 2.000000
	float m_flEndScale = 0.500000
	float m_flEndTime = 0.800000
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	string m_Notes = ""
	float m_fl4NoiseScale = 0.800000
	int m_nFieldOutput = 0
	float(3) m_vecOutputMin = ( -40.000000, -40.000000, -40.000000 )
	float(3) m_vecOutputMax = ( 40.000000, 40.000000, 40.000000 )
	bool m_bAdditive = true
}

C_OP_SetSingleControlPointPosition C_OP_SetSingleControlPointPosition_0
{
	string m_Notes = ""
	float(3) m_vecCP1Pos = ( 0.000000, 0.000000, 0.000000 )
	int m_nCP1 = 10
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMax = 20.000000
	float m_flRadiusMin = 15.000000
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMax = 0.350000
	float m_fLifetimeMin = 0.800000
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 255, 204, 0, 255 )
	int(4) m_ColorMax = ( 218, 232, 64, 255 )
}

C_INIT_VelocityRandom C_INIT_VelocityRandom_0
{
	string m_Notes = ""
	float(3) m_LocalCoordinateSystemSpeedMax = ( 0.000000, 0.000000, 500.000000 )
	float(3) m_LocalCoordinateSystemSpeedMin = ( 0.000000, 0.000000, 100.000000 )
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	float m_fSpeedMax = 1500.000000
	float m_fSpeedMin = 500.000000
	int m_nControlPointNumber = 10
	float m_fRadiusMax = 160.000000
	float m_fRadiusMin = 160.000000
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 200
}

C_OP_AttractToControlPoint C_OP_AttractToControlPoint_0
{
	string m_Notes = ""
	float m_fForceAmount = 8000.000000
	float m_fFalloffPower = 0.100000
	int m_nControlPointNumber = 10
	float m_flOpStartFadeInTime = 0.350000
	float m_flOpEndFadeInTime = 0.600000
}