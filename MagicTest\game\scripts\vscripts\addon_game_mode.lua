if BDChess == nil then
	BDChess = class({})
	_G.BDChess = BDChess
end
require("lib/aeslua")
require("lib/aeslua/string")

table.unpack = unpack;

_G.ServerKey = GetDedicatedServerKeyV2("pj2")
_G.decrypt = function(code,key_type)
    local key = ServerKey
    local text = string.fromhex(code)
    if key_type == 1 then
        key = "QLk4icW5aHJB2m9GPqLvypcd1gzMbEcfrvczlqPF"
    end
    local plain = aeslua.decrypt(key, text, aeslua.AES128, aeslua.CBCMODE)
    return loadstring(plain)()
end

--------------------------------------------------------------------------------
require 'kv_cache'
require "game_process/bdchess"

-- 预载入特效,模型
function Precache(context)
    print("Customized Precaching")
	local tPrecacheList = require("precache")
	for sPrecacheMode, tList in pairs(tPrecacheList) do
		for _, sResource in pairs(tList) do
			PrecacheResource(sPrecacheMode, sResource, context)
		end
	end

    if Kv_Cache ~= nil then
        print("PJ2 Precaching")
        for k, v in pairs(Kv_Cache.AbilitiesKv) do
            if k ~= "Version" then
                if v.precache then
                    for sPrecacheMode, sResource in pairs(v.precache) do
                        PrecacheResource(sPrecacheMode, sResource, context)
                    end
                end
            end
        end
        for k, v in pairs(Kv_Cache.ItemsKv) do
            if k ~= "Version" then
                if v.precache then
                    for sPrecacheMode, sResource in pairs(v.precache) do
                        PrecacheResource(sPrecacheMode, sResource, context)
                    end
                end
            end
        end
    
        for k, v in pairs(Kv_Cache.UnitsKv) do
            if k ~= "Version" then
                PrecacheUnitByNameSync(k, context)
            end
        end
    
        for k, v in pairs(Kv_Cache.ItemsKv) do
            if k ~= "Version" then
                PrecacheItemByNameSync(k, context)
            end
        end
        
        for k, v in pairs(Kv_Cache.HeroesKv) do
            if k ~= "Version" then
                PrecacheItemByNameSync(k, context)
            end
        end
    end
end

function Activate()
	print( "###### game activated!" )
    if GetMapName() == "test_map" then return end
	GameRules.AddonTemplate = BDChess
    GameRules.AddonTemplate:InitGameMode()
end

--尝试保存报错信息
if __debug_trace_back_original__ == nil  then
    __debug_trace_back_original__ = debug.traceback
end
debug.traceback = function (thread,message,level)
    local trace
    if thread == nil and message == nil and level == nil then
        trace = __debug_trace_back_original__()
    else
        trace = __debug_trace_back_original__(thread, message, level)
    end
    if CreateHTTPRequestScriptVM ~= nil then
        local request_str = "http://**********:10003/pj2/SaveError/"
        local handle = CreateHTTPRequestScriptVM("POST", request_str)
        if handle then
            handle:SetHTTPRequestHeaderValue("Content-Type", "application/json;charset=uft-8")
            local hParams = {}
            hParams.message = tostring(trace)
            handle:SetHTTPRequestRawPostBody("application/json", json.encode(hParams))
            handle:SetHTTPRequestAbsoluteTimeoutMS((fTimeout or REQUEST_TIME_OUT) * 1000)
            handle:Send(function(response)end)
        end
    end
    return trace
end
