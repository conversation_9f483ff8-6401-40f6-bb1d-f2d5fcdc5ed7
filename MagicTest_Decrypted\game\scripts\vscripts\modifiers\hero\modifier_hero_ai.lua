if modifier_hero_ai == nil then
	modifier_hero_ai = class({})
end

local public = modifier_hero_ai

function public:IsHidden()
	return true
end
function public:IsDebuff()
	return false
end
function public:IsPurgable()
	return false
end
function public:IsPurgeException()
	return false
end
function public:AllowIllusionDuplicate()
	return false
end
function public:RemoveOnDeath()
	return false
end

function public:OnCreated(params)
	if not IsServer() then
		return
	end
	self.parent = self:GetParent()
    self.playerid = self.parent:GetPlayerOwnerID()
	self:StartIntervalThink(1)
    self.round_stage = "prepare"
    self.round = 0
    self.round_battle_begin = math.ceil(GameRules:GetGameTime())
    -- self.parent:AddExperience(5000, 0, false, false)

    -- 下一张要释放的卡
    self.next_card = ""
    self.next_card_slot = ""
end

function public:OnIntervalThink()
    if not IsServer() or not self.parent:C_IsAlive() then return end 
    local hero = self.parent
    local playerid = self.playerid
    local game_round = CGameMode:GetCurrentRound() or 0
    if game_round == 0 then
        return
    end
    if not CPlayer:IsPlayerAlive(self.playerid)  then
        self:Destroy()
        return
    end
    if game_round > self.round then
        if game_round < 7 then
            self.parent:AddExperience(3, 0, false, false)
        elseif game_round >= 7 and game_round < 11 then
            self.parent:AddExperience(4, 0, false, false)
        elseif game_round >= 11 and game_round < 15 then
            self.parent:AddExperience(5, 0, false, false)
        elseif game_round >= 15 then
            self.parent:AddExperience(6, 0, false, false)
        end
        -- 新的一轮准备回合
        self.round = game_round
        -- 每个回合刷新八张卡牌，每张卡牌随机两个装备
        local slot = 1
        local cards = {}
        local black_list = {
            ["magic_zuus_thundergods_wrath"] = 1,
            ["magic_arc_warden_tempest_double"] = 1,
            ["magic_zuus_nimbus"] = 1,
            ["magic_keeper_of_the_light_blinding_light"] = 1,
            ["magic_batrider_flaming_lasso"] = 1,
        }
        while slot <= 8 do
            local card = RandomValue(Kv_Cache.card.all)
            if not black_list[card] then
                if game_round < 7 and Kv_Cache.CardOnline.card_level[card] < 3 then
                    if not TableFindKey(cards,card) then
                        cards[slot] = card
                        slot = slot + 1
                    end
                elseif game_round >= 7 and game_round < 11 and Kv_Cache.CardOnline.card_level[card] < 4 then
                    if not TableFindKey(cards,card) then
                        cards[slot] = card
                        slot = slot + 1
                    end
                elseif game_round >= 11 then
                    if not TableFindKey(cards,card) then
                        cards[slot] = card
                        slot = slot + 1
                    end
                end
            end
        end
        for slot, card in pairs(cards) do
            local eq1 = ""
            local eq2 = ""
            if string.find(card,"magic_") then
                if game_round < 11 then
                    eq1 = "equip" .. string.sub(card,6,string.len(card))
                    eq2 = ""
                else
                    eq1 = "equip" .. string.sub(card,6,string.len(card))
                    eq2 = "equip" .. string.sub(card,6,string.len(card)) .."_1"
                end
            elseif string.find(card,"summon_")  then
                if game_round < 11 then
                    eq1 = "equip" .. string.sub(card,7,string.len(card))
                    eq2 = ""
                else
                    eq1 = "equip" .. string.sub(card,7,string.len(card))
                    eq2 = "equip" .. string.sub(card,7,string.len(card)) .."_1"
                end
            end

            local num = 1
            if game_round < 7 then
                if Kv_Cache.CardOnline.card_level[card] == 1 then
                    num = RandomInt(2, 3)
                elseif Kv_Cache.CardOnline.card_level[card] == 2 then
                    num = RandomInt(1, 2)
                end
            end
            if game_round >= 7 and game_round < 11 then
                if Kv_Cache.CardOnline.card_level[card] == 1 then
                    num = RandomInt(5, 6)
                elseif Kv_Cache.CardOnline.card_level[card] == 2 then
                    num = RandomInt(2, 3)
                elseif Kv_Cache.CardOnline.card_level[card] == 3 then
                    num = RandomInt(1, 2)
                end
            end
            if game_round >= 11 and game_round < 15 then
                if Kv_Cache.CardOnline.card_level[card] == 1 then
                    num = RandomInt(5, 6)
                elseif Kv_Cache.CardOnline.card_level[card] == 2 then
                    num = RandomInt(5, 6)
                elseif Kv_Cache.CardOnline.card_level[card] == 3 then
                    num = RandomInt(2, 3)
                elseif Kv_Cache.CardOnline.card_level[card] == 4 then
                    num = RandomInt(1, 2)
                end
            end
            if game_round >= 15 and game_round < 19 then
                if Kv_Cache.CardOnline.card_level[card] == 1 then
                    num = RandomInt(5, 6)
                elseif Kv_Cache.CardOnline.card_level[card] == 2 then
                    num = RandomInt(5, 6)
                elseif Kv_Cache.CardOnline.card_level[card] == 3 then
                    num = RandomInt(4, 6)
                elseif Kv_Cache.CardOnline.card_level[card] == 4 then
                    num = RandomInt(2, 3)
                end
            end
            if game_round >= 19 then
                if Kv_Cache.CardOnline.card_level[card] == 1 then
                    num = RandomInt(5, 6)
                elseif Kv_Cache.CardOnline.card_level[card] == 2 then
                    num = RandomInt(5, 6)
                elseif Kv_Cache.CardOnline.card_level[card] == 3 then
                    num = RandomInt(5, 6)
                elseif Kv_Cache.CardOnline.card_level[card] == 4 then
                    num = RandomInt(1, 6)
                end
            end
            CardAction.cards_all[playerid][slot]={
                ["card"] = card,
                ["num"] = num,
                ["slot"] = slot,
                ["equip_slot"] = {
                    [1] = eq1,
                    [2] = eq2
                }
            }
        end
    else
        if CGameMode:GetCurrentStage() == "battle" then
            if CGameMode.GameStatus == GAMESTATUS_ENUM.PVP and BattleBoard:IsPlayerBye(playerid) then
                return
            end
            if self.round_stage ~= "battle" and CGameMode:GetCurrentStage() == "battle" then
                self.round_battle_begin = math.ceil(GameRules:GetGameTime())
                self.round_stage = "battle"
            end
            if self.round_stage == "battle" and BattleBoard:IsPlayerBattling(playerid) and math.fmod((math.ceil(GameRules:GetGameTime()) - self.round_battle_begin), 3) == 0  then
                -- 随机释放1-4位置的卡牌
                hero:Stop()
                local slot = RandomInt(1, 4)
                -- 根据卡,确定一个位置
                local cardinfo = CardAction.card_battling[playerid][slot]
                if not cardinfo or hero:C_IsAlive() == false or hero:C_IsStunned() or hero:C_IsSilenced() or hero:C_IsFrozen() then
                    return
                end
    
                local abilityname = cardinfo["ability"]
                local ability = hero:FindAbilityByName(abilityname)
                if not IsValid(ability) then return end
                if "magic_refresher_orb" == abilityname then
                    return
                end
                if hero:IsChanneling() and hero.iscasting then
                    return
                end
                -- 判断mana是否足够
                local mana_cost = CardAction:GetBattlingCardMana(playerid,slot)
                if not CMana:IsManaEnough(playerid,mana_cost) then
                    return
                end
                -- CMana:GiveMana(hero,mana_cost)
                hero.currentcard = cardinfo["card"]
                hero.currentcard_type = "battle"
                hero.currentslot = slot
                hero.iscasting = true
                -- 刷新球
                if card == "magic_refresher_orb" then
                    -- 是否有刷新球的等级变更效果
                    if hero:FindModifierByName("equip_refresher_orb_3") then
                        local refresh = hero:FindAbilityByName("magic_refresher_orb")
                        if refresh then
                            local level = refresh:GetLevel()
                            ability:SetLevel(level)
                        end
                    end
                end
                if Kv_Cache.CardOnline.summon_card_max[abilityname] then
                    local max = Kv_Cache.CardOnline.summon_card_max[abilityname] 
                    local unitname = string.sub(abilityname,8,string.len(abilityname))
                    local ulist = BattleBoard:GetPlayerCourtUnits(playerid,unitname)
                    if ulist >= max then
                        return
                    end
                end
                -- 获取施法位置
                -- local enemy_hero = BattleBoard:GetPlayerBattleInfo_EnemyHero(playerid)
                -- local range = CardPool.cardinfo[abilityname]["Range"]
                -- local width = CardPool.cardinfo[abilityname]["Width"]
                -- local radius = CardPool.cardinfo[abilityname]["Radius"]

                if ability:GetAbilityTargetTeam()==1 then   
					local cast_range=ability:GetCastRange(hero:GetOrigin(), hero)
					local targets = FindUnitsInRadius(
						hero:GetTeamNumber(),	-- int, your team number
						hero:GetAbsOrigin(),	-- point, center point
						nil,	-- handle, cacheUnit. (not known)
						cast_range,	-- float, radius. or use FIND_UNITS_EVERYWHERE
						DOTA_UNIT_TARGET_TEAM_FRIENDLY,	-- int, team filter
						DOTA_UNIT_TARGET_HERO + DOTA_UNIT_TARGET_BASIC,	-- int, type filter
						0,	-- int, flag filter
						0,	-- int, order filter
						false	-- bool, can grow cache
					)
					if #targets~=0 then   				
						local target=targets[1]
						local point =target:GetAbsOrigin()
	
						--羁绊特殊处理
						if ability:GetAbilityName()=="magic_wisp_tether" then 
							for _,v in pairs(targets) do 
								if not v:HasModifier("modifier_magic_wisp_tether_ally") then  
									target=v
								end 
							end 
						end 
						hero:CastAbilityOnPosition( point, ability, -1 )
					else 
						local point =hero:GetAbsOrigin()+RandomVector(300)
						hero:CastAbilityOnPosition( point, ability, -1 )
					end 
				else 
					local cast_range=ability:GetCastRange(hero:GetOrigin(), hero)
	
					local targets = FindUnitsInRadius(
						hero:GetTeamNumber(),	-- int, your team number
						hero:GetAbsOrigin(),	-- point, center point
						nil,	-- handle, cacheUnit. (not known)
						cast_range,	-- float, radius. or use FIND_UNITS_EVERYWHERE
						DOTA_UNIT_TARGET_TEAM_ENEMY,	-- int, team filter
						DOTA_UNIT_TARGET_HERO + DOTA_UNIT_TARGET_BASIC,	-- int, type filter
						0,	-- int, flag filter
						0,	-- int, order filter
						false	-- bool, can grow cache
					)
					if #targets~=0 then               
						local target=targets[1]
						local point=target:GetAbsOrigin()
						--可特殊处理：生命汲取，燃烧枷锁
						if ability:GetAbilityName()~="magic_dark_willow_terrorize" then 
							Timers:CreateTimer(0.2,function()
								hero:CastAbilityOnPosition( point, ability, -1 )
							end)
						else 
							Timers:CreateTimer(1.25,function ()
								hero:CastAbilityOnPosition( point, ability, -1 )
							end)
						end 
					else 
						local point =hero:GetAbsOrigin()+RandomVector(300)
						hero:CastAbilityOnPosition( point, ability, -1 )
					end 
				end 

    
                -- local pos = BattleBoard:GetRandomPointInSide_Self(playerid)
                -- local ismagic = TableFindKey(Kv_Cache.card.magic,abilityname)
                -- if ismagic then
                --     pos = BattleBoard:GetRandomPointInSide_Enemy(playerid)
                -- end
                -- local vPos = BattleBoard:GetRealAbilityCastPos(playerid,pos)
                -- print("spell ability:",abilityname)
                -- if CardPool.cardinfo[abilityname]["isNoTarget"] == true then
                --     ExecuteOrderFromTable({
                --         UnitIndex = hero:entindex(),
                --         OrderType = DOTA_UNIT_ORDER_CAST_NO_TARGET,
                --         Position = vPos,
                --         AbilityIndex = ability:entindex(),
                --         Queue = 0,
                --         TargetIndex = nil,
                --     })
                -- else
                --     ExecuteOrderFromTable({
                --         UnitIndex = hero:entindex(),
                --         OrderType = DOTA_UNIT_ORDER_CAST_POSITION,
                --         Position = vPos,
                --         AbilityIndex = ability:entindex(),
                --         Queue = 0,
                --         TargetIndex = nil,
                --     })
                -- end
            else
                if not hero.iscasting then
                    local pos = BattleBoard:GetRandomPointInSide_Self(self.playerid)
                    self.parent:MoveToPosition(pos)
                end
            end
        else
            self.round_stage = "prepare"
        end
    end
end 

function public:DeclareFunctions()
	return {
		MODIFIER_EVENT_ON_ABILITY_FULLY_CAST,
	}
end
function public:OnAbilityFullyCast(params)
	-- 施法完寻找目标攻击
	local ability = params.ability
	if ability:GetCaster() == self.parent then
        self.parent.iscasting = false
		-- local pos = BattleBoard:GetRandomPointInSide_Self(self.playerid)
        -- self.parent:MoveToPosition(pos)
	end
end
