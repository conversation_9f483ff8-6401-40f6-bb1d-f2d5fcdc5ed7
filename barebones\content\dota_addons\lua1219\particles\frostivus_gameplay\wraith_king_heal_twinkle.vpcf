<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 64
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_FadeAndKill_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_SpinUpdate_0,
		&C_OP_SpinYaw_0,
		&C_OP_PositionLock_0,
		&C_OP_ColorInterpolate_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomRotationSpeed_0,
		&C_INIT_RemapSpeedToScalar_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomYaw_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	int m_nOrientationType = 3
	string m_hTexture = "materials\\particle\\particle_flares\\particle_flare_002.vtex"
	float m_flAnimationRate = 0.750000
	float m_flAnimationRate2 = 0.100000
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float m_fDrag = 0.090000
	float(3) m_Gravity = ( 0.000000, 0.000000, 100.000000 )
	string m_Notes = ""
}

C_OP_FadeAndKill C_OP_FadeAndKill_0
{
	float m_flEndFadeInTime = 0.200000
	float m_flStartFadeOutTime = 0.400000
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flBias = 0.865000
	float m_flEndScale = 25.000000
	string m_Notes = ""
}

C_OP_SpinUpdate C_OP_SpinUpdate_0
{
	string m_Notes = ""
}

C_OP_SpinYaw C_OP_SpinYaw_0
{
	int m_nSpinRateDegrees = 40
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	int(4) m_ColorFade = ( 104, 136, 27, 255 )
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMin = 0.400012
	float m_fLifetimeMax = 0.600000
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 6.000000
	float m_flRadiusMin = 3.000000
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMax = ( 255, 253, 196, 255 )
	int(4) m_ColorMin = ( 255, 226, 109, 255 )
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	int m_nAlphaMin = 25
	int m_nAlphaMax = 150
	string m_Notes = ""
}

C_INIT_RandomRotationSpeed C_INIT_RandomRotationSpeed_0
{
	float m_flDegreesMax = 180.000000
	string m_Notes = ""
}

C_INIT_RemapSpeedToScalar C_INIT_RemapSpeedToScalar_0
{
	float m_flOutputMax = 0.500000
	int m_nFieldOutput = 5
	float m_flInputMax = 10.000000
	bool m_bPerParticle = true
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	float m_flDegreesMin = 90.000000
	float m_flDegreesMax = 90.000000
	string m_Notes = ""
}

C_INIT_RandomYaw C_INIT_RandomYaw_0
{
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 3
	string m_Notes = ""
}