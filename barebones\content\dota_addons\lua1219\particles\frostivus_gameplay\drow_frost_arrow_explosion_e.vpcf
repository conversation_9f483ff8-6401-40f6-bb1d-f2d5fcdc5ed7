<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 64
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_MaxVelocity_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_OscillateVector_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_RampScalarSpline_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_RandomSecondSequence_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RingWave_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		&C_OP_AttractToControlPoint_0
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	symbol m_nSequenceCombineMode = SEQUENCE_COMBINE_MODE_ALPHA_FROM0_RGB_FROM_1
	float m_flSelfIllumAmount = 1.000000
	float m_flZoomRate1 = 2.000000
	bool m_bMaxLuminanceBlendingSequence0 = false
	bool m_bMaxLuminanceBlendingSequence1 = true
	string m_hTexture = "materials\\particle\\vistasmokev1\\vistasmokev1.vtex"
	string m_Notes = ""
	float m_flAnimationRate = 2.000000
	float m_flAnimationRate2 = 0.500000
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float(3) m_Gravity = ( 0.000000, 0.000000, 800.000000 )
	float m_fDrag = 0.250000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 3.000000
	float m_flBias = 0.400000
}

C_OP_MaxVelocity C_OP_MaxVelocity_0
{
	string m_Notes = ""
	float m_flMaxVelocity = 764.000000
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	float m_flFadeEndTime = 0.700000
	int(4) m_ColorFade = ( 136, 186, 191, 255 )
}

C_OP_OscillateVector C_OP_OscillateVector_0
{
	string m_Notes = ""
	float(3) m_RateMin = ( -13.000000, -13.000000, -13.000000 )
	float(3) m_RateMax = ( 13.000000, 13.000000, 13.000000 )
	float(3) m_FrequencyMin = ( 0.250000, 0.250000, 0.250000 )
	float(3) m_FrequencyMax = ( 2.000000, 2.000000, 2.000000 )
	float m_flStartTime_min = 1.000000
	float m_flStartTime_max = 1.000000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 1.000000
}

C_OP_RampScalarSpline C_OP_RampScalarSpline_0
{
	string m_Notes = ""
	int m_nField = 4
	float m_RateMin = -3.000000
	float m_RateMax = 3.000000
	bool m_bEaseOut = true
	float m_flBias = 0.750000
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMax = 0.300000
	float m_fLifetimeMin = 0.200000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMax = 22.000000
	float m_flRadiusMin = 10.000000
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMax = ( 0, 130, 173, 255 )
	int(4) m_ColorMin = ( 177, 236, 255, 255 )
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	string m_Notes = ""
	int m_nSequenceMax = 1
}

C_INIT_RandomSecondSequence C_INIT_RandomSecondSequence_0
{
	string m_Notes = ""
	int m_nSequenceMin = 49
	int m_nSequenceMax = 59
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
	int m_nAlphaMin = 24
	int m_nAlphaMax = 40
}

C_INIT_RingWave C_INIT_RingWave_0
{
	string m_Notes = ""
	bool m_bXYVelocityOnly = false
	int m_nControlPointNumber = 3
	float m_flInitialRadius = 2.000000
	float m_flInitialSpeedMin = 628.000000
	float m_flInitialSpeedMax = 892.000000
	bool m_bEvenDistribution = true
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 32
}

C_OP_AttractToControlPoint C_OP_AttractToControlPoint_0
{
	string m_Notes = ""
	float m_fForceAmount = -700.000000
	float m_fFalloffPower = 0.500000
}