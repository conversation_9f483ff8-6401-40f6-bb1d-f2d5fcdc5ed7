<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 32
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_FadeOut_0,
		&C_OP_Decay_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_PercentageBetweenCPs_0,
		&C_OP_PercentageBetweenCPs_2,
		&C_OP_RampScalarSplineSimple_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_CreateSequentialPath_0,
		&C_INIT_RandomAlpha_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\particle_glow_04.vtex"
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_FadeOut C_OP_FadeOut_0
{
	float m_flFadeOutTimeMin = 1.000000
	float m_flFadeOutTimeMax = 1.000000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	float m_flOpStartFadeInTime = 3.000000
	float m_flOpEndFadeInTime = 3.000000
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	int(4) m_ColorFade = ( 229, 221, 149, 255 )
	string m_Notes = ""
}

C_OP_PercentageBetweenCPs C_OP_PercentageBetweenCPs_0
{
	float m_flOutputMax = 0.000000
	float m_flOutputMin = 1.000000
	int m_nFieldOutput = 16
	float m_flInputMin = 0.875000
	string m_Notes = ""
}

C_OP_PercentageBetweenCPs C_OP_PercentageBetweenCPs_2
{
	bool m_bRadialCheck = false
	bool m_bActiveRange = true
	int m_nFieldOutput = 16
	float m_flInputMax = 0.750000
	string m_Notes = ""
}

C_OP_RampScalarSplineSimple C_OP_RampScalarSplineSimple_0
{
	bool m_bEaseOut = true
	float m_Rate = -50.000000
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 1.500000
	float m_fLifetimeMin = 1.500000
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMax = ( 206, 98, 39, 255 )
	int(4) m_ColorMin = ( 163, 61, 61, 255 )
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMin = 150.000000
	float m_flRadiusMax = 150.000000
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 96.000000 )
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 96.000000 )
	string m_Notes = ""
}

C_INIT_CreateSequentialPath C_INIT_CreateSequentialPath_0
{
	float m_flNumToAssign = 25.000000
	string m_Notes = ""
	CPathParameters m_PathParams = CPathParameters
	{
		float m_flMidPoint = 0.150000
		int m_nBulgeControl = 1
		float m_flBulge = 0.150000
	}
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	int m_nAlphaMin = 64
	int m_nAlphaMax = 64
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmissionDuration = 0.050000
	float m_flEmitRate = 500.000000
	string m_Notes = ""
}