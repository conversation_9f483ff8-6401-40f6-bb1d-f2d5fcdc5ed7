<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 256
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 255, 216, 155, 255 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_BasicMovement_0,
		&C_OP_ColorInterpolate_0,
		&C_OP_VectorNoise_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RingWave_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_InitialVelocityNoise_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	float m_flStartFadeSize = 0.800000
	float m_flEndFadeSize = 1.000000
	float m_flMaxSize = 0.400000
	bool m_bDisableZBuffering = true
	string m_hTexture = "materials\\particle\\particle_glow_04.vtex"
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.500000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 0.000000
	float m_flStartScale = 20.000000
	float m_flBias = 0.950000
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float m_fDrag = 0.010000
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	int(4) m_ColorFade = ( 201, 85, 22, 255 )
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	string m_Notes = ""
	int m_nFieldOutput = 0
	float(3) m_vecOutputMin = ( -5.000000, -5.000000, 0.000000 )
	float(3) m_vecOutputMax = ( 5.000000, 5.000000, 5.000000 )
	bool m_bAdditive = true
}

C_INIT_RingWave C_INIT_RingWave_0
{
	string m_Notes = ""
	float m_flInitialRadius = 625.000000
	bool m_bEvenDistribution = true
	float m_flParticlesPerOrbit = 200.000000
	float m_flInitialSpeedMin = 100.000000
	float m_flInitialSpeedMax = 100.000000
	int m_nControlPointNumber = 7
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.350000
	float m_fLifetimeMax = 1.000000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMin = 10.000000
	float m_flRadiusMax = 10.000000
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 5.000000 )
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 5.000000 )
	bool m_bLocalCoords = true
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	string m_Notes = ""
	float m_flNoiseScaleLoc = 0.250000
	float(3) m_vecOutputMin = ( -50.000000, -50.000000, 50.000000 )
	float(3) m_vecOutputMax = ( 50.000000, 50.000000, 100.000000 )
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 200
	string m_Notes = ""
}