<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 10
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 0.800000
	int m_nConstantSequenceNumber = 6
	float m_flMaxRecreationTime = -1.000000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderModels_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_FadeInSimple_0,
		&C_OP_Orient2DRelToCP_0,
		&C_OP_StopAfterCPDuration_0,
		&C_OP_EndCapTimedDecay_0,
		&C_OP_RampScalarSplineSimple_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_SetPerChildControlPoint_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_PositionPlaceOnGround_0,
		&C_INIT_RandomSecondSequence_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomScalar_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_VelocityRandom_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/juggernaut_fs_omnislash_attack_blur_3.vpcf"
		}
	]
}

C_OP_RenderModels C_OP_RenderModels_0
{
	int m_nModelCP = 0
	bool m_bOrientZ = true
	string m_ActivityName = ""
	string m_Notes = ""
	string m_EconSlotName = ""
	string m_hOverrideMaterial = "models\\heroes\\ember_spirit\\ember_spirit_remnant_2_Add.vmt"
	bool m_bManualAnimFrame = true
	int m_nManualFrameField = 18
	ModelReference_t[] m_ModelList = 
	[
		ModelReference_t
		{
			string m_model = "models/heroes/juggernaut/juggernaut.vmdl"
		}
	]
	bool m_bAnimated = true
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float m_fDrag = 0.150000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	int m_nOpEndCapState = 1
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	bool m_bDisableOperator = true
	float m_flFadeInTime = 0.125000
	string m_Notes = ""
}

C_OP_Orient2DRelToCP C_OP_Orient2DRelToCP_0
{
	int m_nFieldOutput = 12
	float m_flRotOffset = 90.000000
	string m_Notes = ""
}

C_OP_StopAfterCPDuration C_OP_StopAfterCPDuration_0
{
	bool m_bDestroyImmediately = true
	bool m_bPlayEndCap = false
	string m_Notes = ""
}

C_OP_EndCapTimedDecay C_OP_EndCapTimedDecay_0
{
	float m_flDecayTime = 0.125000
	string m_Notes = ""
}

C_OP_RampScalarSplineSimple C_OP_RampScalarSplineSimple_0
{
	string m_Notes = ""
	int m_nField = 18
	float m_Rate = 16.000000
	bool m_bEaseOut = true
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	string m_Notes = ""
	int m_nField = 18
	float m_Rate = 5.000000
}

C_OP_SetPerChildControlPoint C_OP_SetPerChildControlPoint_0
{
	string m_Notes = ""
	int m_nFirstControlPoint = 3
	int m_nNumControlPoints = 6
	bool m_bSetOrientation = true
	bool m_bNumBasedOnParticleCount = true
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusRandExponent = 0.800000
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMin = ( -20.000000, -20.000000, 200.000000 )
	float(3) m_OffsetMax = ( 20.000000, 20.000000, 200.000000 )
}

C_INIT_PositionPlaceOnGround C_INIT_PositionPlaceOnGround_0
{
	float m_flMaxTraceLength = 512.000000
	string m_CollisionGroupName = "DEBRIS"
	string m_Notes = ""
}

C_INIT_RandomSecondSequence C_INIT_RandomSecondSequence_0
{
	int m_nSequenceMax = 35
	int m_nSequenceMin = 35
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 24, 255, 0, 255 )
	int(4) m_ColorMax = ( 24, 255, 0, 255 )
}

C_INIT_RandomScalar C_INIT_RandomScalar_0
{
	string m_Notes = ""
	float m_flMin = 6.000000
	float m_flMax = 6.000000
	int m_nFieldOutput = 18
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	int m_nControlPointNumber = 1
	string m_Notes = ""
}

C_INIT_VelocityRandom C_INIT_VelocityRandom_0
{
	float(3) m_LocalCoordinateSystemSpeedMax = ( 600.000000, 600.000000, 0.000000 )
	float(3) m_LocalCoordinateSystemSpeedMin = ( -600.000000, -600.000000, 0.000000 )
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 1
	string m_Notes = ""
}