<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 100
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 0.900000
	int(4) m_ConstantColor = ( 195, 223, 255, 255 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_FadeInSimple_0,
		&C_OP_BasicMovement_0,
		&C_OP_SpinUpdate_0,
		&C_OP_RampScalarLinearSimple_2,
		&C_OP_FadeOutSimple_0,
		&C_OP_Decay_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RingWave_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomRotationSpeed_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_PositionOffset_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0,
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\smoke\\static\\static_smoke.vtex"
	string m_Notes = ""
	float m_flAnimationRate = 0.200000
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	int m_nOpEndCapState = 1
	string m_Notes = ""
	float m_Rate = -2.000000
	float m_flEndTime = 9999999.000000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 1.500000
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.500000
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float(3) m_Gravity = ( 0.000000, 0.000000, 50.000000 )
	float m_fDrag = 0.030000
}

C_OP_SpinUpdate C_OP_SpinUpdate_0
{
	string m_Notes = ""
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_2
{
	float m_flEndTime = 999999.000000
	float m_Rate = -1.500000
	int m_nField = 16
	string m_Notes = ""
	int m_nOpEndCapState = 1
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.750000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 3.000000
	float m_fLifetimeMax = 3.500000
}

C_INIT_RingWave C_INIT_RingWave_0
{
	float m_flInitialSpeedMax = 50.000000
	float m_flInitialRadius = 625.000000
	string m_Notes = ""
	float m_flInitialSpeedMin = 10.000000
	int m_nControlPointNumber = 7
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 75.000000
	float m_flRadiusMin = 50.000000
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
	int m_nAlphaMin = 50
	int m_nAlphaMax = 100
}

C_INIT_RandomRotationSpeed C_INIT_RandomRotationSpeed_0
{
	float m_flDegreesMin = 15.000000
	float m_flDegreesMax = 30.000000
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMax = ( 222, 213, 190, 255 )
	string m_Notes = ""
	int(4) m_ColorMin = ( 237, 219, 188, 255 )
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 4
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 100.000000 )
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 50.000000 )
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 10.000000
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 10
	string m_Notes = ""
}