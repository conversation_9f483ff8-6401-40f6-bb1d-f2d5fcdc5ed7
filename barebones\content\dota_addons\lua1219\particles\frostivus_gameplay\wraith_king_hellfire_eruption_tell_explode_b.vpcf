<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 4
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 90.000000
	int(4) m_ConstantColor = ( 99, 255, 190, 155 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderModels_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_RotateVector_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRotation_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderModels C_OP_RenderModels_0
{
	string m_hOverrideMaterial = ""
	string m_EconSlotName = ""
	string m_ActivityName = ""
	int m_nSkin = 10
	string m_Notes = ""
	ModelReference_t[] m_ModelList = 
	[
		ModelReference_t
		{
			string m_model = "models/particle/sphere.vmdl"
		}
	]
	bool m_bAnimated = true
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flBias = 0.850000
	float m_flStartScale = 0.000000
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 1.000000
}

C_OP_RotateVector C_OP_RotateVector_0
{
	float m_flRotRateMax = 380.000000
	float m_flRotRateMin = -380.000000
	float(3) m_vecRotAxisMax = ( 1.000000, 1.000000, 1.000000 )
	float(3) m_vecRotAxisMin = ( -1.000000, -1.000000, -1.000000 )
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMax = 0.500000
	float m_fLifetimeMin = 0.500000
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 1
}