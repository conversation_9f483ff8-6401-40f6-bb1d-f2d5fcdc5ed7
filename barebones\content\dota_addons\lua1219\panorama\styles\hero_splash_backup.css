/*DOTAHeroSplash
{
    width: 100%;
	height: 100%;
	background-image: url("s2r://panorama/images/backgrounds/herosplash_vignette_png.vtex");
	background-size: contain;
}*/

#HeroPickerButton
{
	margin-top: 55px;
    width: 100%;
	height: 100%;
}

#FeaturedHeroModels
{
	margin-top: -140px;
	horizontal-align: center;
}

#ThreeHeroes
{
	flow-children: right;
	horizontal-align: center;
}

#BackgroundGlow
{
	width: 100%;
	height: 100%;
	horizontal-align: center;
	vertical-align: bottom;
	opacity-mask: url("s2r://panorama/images/masks/softedge_circle_png.vtex");
	background-color: gradient( radial, 50% 50%, 0% 0%, 100% 100%, from( #9efeff99 ), color-stop( 0.25, #466a6022 ), to( #365a50 ) );
}

DOTAScenePanel
{
	margin-left: -150px;
	margin-right: -150px;
	width: 700px;
	height: 1080px;
	opacity-mask: url("s2r://panorama/images/masks/softedge_box_png.vtex");
    opacity: 0.0;
	//saturation: 0.5;
	//wash-color: #445566;

    transition-property: opacity;
    transition-duration: 0.2s;
}

DOTAScenePanel.HeroVisible
{
    opacity: 1.0;
}

#FeaturedHeroModel1
{
	margin-left: -200px;
	margin-right: -200px;
	width: 1025px;
	height: 1080px;
}

#FeaturedHeroModel2
{
	/* Puts the middle hero preview on top of the others*/
	z-index: 1;
	margin-left: -200px;
	margin-right: -200px;
	width: 700px;
	height: 1080px;
	
}

#FeaturedHeroModel3
{
	margin-left: -200px;
	margin-right: -200px;
	width: 1025px;
	height: 1080px;
}

#FeaturedHeroModel1.SceneLoaded
{
	sound: "ui_hero_select_slide_late";
}

#FeaturedHeroModel2.SceneLoaded
{
	sound: "ui_hero_select_slide_late";
}

#FeaturedHeroModel3.SceneLoaded
{
	sound: "ui_hero_select_slide_late";
}


#FeaturedHeroButtons
{
	horizontal-align: middle;
	vertical-align: bottom;
	flow-children: right;

	margin-bottom: 124px;
}


.FeaturedHeroPlaque
{
	width: 400px;
	vertical-align: bottom;
	margin-bottom: 0px;
    padding: 5px;
	margin-left: 44px;
	margin-right: 44px;

	flow-children: down;

	opacity: 0.0;

	transition-property: background-color, opacity;
	transition-duration: 0.1s;
	transition-timing-function: ease-in;    	
}

.AspectRatio4x3 .FeaturedHeroPlaque, .AspectRatio5x4 .FeaturedHeroPlaque
{
	width: 350px;
}

.FeaturedHeroPlaque.HeroVisible
{
    opacity: 1.0;
}

#FeaturedHeroReason
{
	font-size: 16px;
	color: #a5afbc;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-weight: bold;
	background-color: #000000ee;
	width: 100%;
	padding: 8px 12px 4px 12px;
}

.Language_schinese #FeaturedHeroReason, .Language_schinese #FeaturedHeroDescription
{
	font-size: 20px;
}

.Language_schinese #FeaturedHeroName
{
	font-size: 28px;
}

#FeaturedHeroButton
{
	flow-children: right;
	padding: 8px 10px 8px 10px;
	width: 100%;
}

#FeaturedHeroButton.ButtonBevel
{
	transition-property: background-color, box-shadow;
	transition-duration: 0.1s;
	transition-timing-function: linear;
}

#FeaturedHeroButton.ButtonBevel:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5c6162 ), to( #778182 ) );

	border-top-color: #aaaaaa77;
	border-left-color: #aaaaaa33;
	border-bottom-color: #333333;
	border-right-color: #404040;
	
	box-shadow: fill #62b7fb99 0px 0px 30px 0px;
	
	color: red;
}

#FeaturedHeroButton.Activated
{
	sound: "ui_hero_select_slide";
}

.FeaturedHeroButtonLabels
{
	flow-children: down;
	width: fill-parent-flow( 1.0 );
}

.FeaturedHeroButtonLabels Label
{
	text-align: left;
	horizontal-align: left;
	text-shadow: none;
	margin-top: 0px;
	
}

#FeaturedHeroName
{
	font-size: 22px;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-weight: bold;
	color: #dfedfe;
}

#FeaturedHeroDescription
{
    font-size: 16px;
	text-transform: uppercase;
	font-weight: bold;
	letter-spacing: 1px;
	color: #a5afbc;
	white-space: nowrap;
	margin-top: 0px;
}

#FeaturedHeroButton:hover #FeaturedHeroDescription 
{
	color: white;
}

#FeaturedHeroButton:hover .FeaturedHeroButtonArrow
{
	wash-color: white;
}


.FeaturedHeroButtonArrow
{
	wash-color: #272c31;
	vertical-align: bottom;
	margin-bottom: 4px;
	width: 8px;
	height: width-percentage( 200% );

	background-image: url("s2r://panorama/images/control_icons/single_arrow_right_png.vtex");
	background-size: contain;
}


.SplashButtons
{
    vertical-align: top;
    horizontal-align: right;
    margin-right: 50px;
    margin-top: 108px;
	width: 210px;
	flow-children: down;
}

.SplashButton
{
	width: 100%;
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #434b4b55 ), to( #636f6f55 ) );
	margin-bottom: 10px;
    padding: 8px 12px;

	transition-property: background-color, opacity;
	transition-duration: 0.2s;
}


.SplashButtonContents
{
	flow-children: right;
	horizontal-align: center;

	wash-color: #cff8f6;
}

.SplashButton:hover
{
    background-color: gradient( linear, 0% 0%, 0% 100%, from( #434b4b ), to( #636f6f ) );
}

.SplashButton:hover .SplashButtonContents
{
	wash-color: white;
}

.SplashButton:active 
{
	background-color: #aaaaaacc;
}

.SplashButtonContents Label
{
    vertical-align: middle;
    margin-left: 8px;
    padding-top: 3px;
    font-size: 16px;
	font-weight: bold;
    text-transform: uppercase;
	letter-spacing: 1px;
    color: white;
}

.Language_schinese .SplashButtonContents Label
{
	font-size: 24px;
}

.SplashButtonContents Label:hover
{
	color: white;
}


#RefreshSplashButton
{
    opacity: 0.0;
}

.RefreshVisible #RefreshSplashButton
{
    opacity: 1.0;
}



.SplashButtonIcon
{
	vertical-align: middle;
	width: 24px;
    height: 24px;

	background-size: contain;
	background-position: 50% 50%;
	background-repeat: no-repeat;
}

.RefreshSplashIcon
{
	background-image: url("s2r://panorama/images/control_icons/refresh_psd.vtex");
}

.HeroGridIcon
{
	background-image: url("s2r://panorama/images/topbar/icon_hero_view_nav_gridpage_2x_png.vtex");
	background-size: 24px 14px;
	margin-bottom: 1px;
}

.FeaturedHeroesStatusMessage
{
    vertical-align: middle;
    horizontal-align: center;
    padding: 10px;

    opacity: 0.0;

    transition-property: opacity;
    transition-duration: 0.2s;
}

#ErrorLoadingFeaturedHeroes
{
    font-size: 30px;
    color: white;
    text-shadow: 2px 2px 8px 2.0 black;
}

.ErrorVisible #ErrorLoadingFeaturedHeroes
{
    opacity: 1.0;
}

#LoadingFeaturedHeroes
{
    flow-children: right;
}

.LoadingVisible #LoadingFeaturedHeroes
{
    opacity: 1.0;
}

#LoadingFeaturedHeroes .Spinner
{
    vertical-align: middle;
    margin-right: 10px;
}

#LoadingFeaturedHeroes Label
{
    vertical-align: middle;
    font-size: 30px;
    color: white;
    text-shadow: 2px 2px 8px 2.0 black;
}
