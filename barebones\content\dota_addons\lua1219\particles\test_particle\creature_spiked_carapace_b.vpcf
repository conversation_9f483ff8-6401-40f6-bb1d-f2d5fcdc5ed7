<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 75
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 230, 241, 255, 255 )
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderModels_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_InterpolateRadius_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_RemapDirectionToCPToVector_0,
		&C_OP_BasicMovement_0,
		&C_OP_LockToBone_0,
		&C_OP_RadiusDecay_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_CreateOnModel_0,
		&C_INIT_RandomYaw_0,
		&C_INIT_RandomScalar_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomRotation_2
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderModels C_OP_RenderModels_0
{
	string m_hOverrideMaterial = ""
	string m_EconSlotName = ""
	string m_Notes = ""
	string m_ActivityName = ""
	bool m_bOrientZ = true
	ModelReference_t[] m_ModelList = 
	[
		ModelReference_t
		{
			string m_model = "models/particle/quill.vmdl"
		}
	]
	bool m_bAnimated = true
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flStartScale = 0.000000
	float m_flBias = 0.900000
	float m_flEndTime = 0.500000
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	string m_Notes = ""
	float m_flEndTime = 99999.000000
	float m_Rate = -2.000000
	int m_nOpEndCapState = 1
}

C_OP_RemapDirectionToCPToVector C_OP_RemapDirectionToCPToVector_0
{
	string m_Notes = ""
	float m_flOffsetRot = 30.000000
	float(3) m_vecOffsetAxis = ( 1.000000, 0.000000, 0.000000 )
	bool m_bNormalize = true
	int m_nFieldOutput = 21
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_LockToBone C_OP_LockToBone_0
{
	string m_Notes = ""
}

C_OP_RadiusDecay C_OP_RadiusDecay_0
{
	string m_Notes = ""
	float m_flMinRadius = 0.010000
	int m_nOpEndCapState = 1
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMax = 0.500000
	float m_fLifetimeMin = 0.400000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMax = 1.150000
	float m_flRadiusMin = 0.850000
}

C_INIT_CreateOnModel C_INIT_CreateOnModel_0
{
	string m_Notes = ""
	bool m_bLocalCoords = true
	float(3) m_vecDirectionBias = ( -0.250000, 0.000000, 0.500000 )
	float m_flHitBoxScale = 0.850000
}

C_INIT_RandomYaw C_INIT_RandomYaw_0
{
	string m_Notes = ""
}

C_INIT_RandomScalar C_INIT_RandomScalar_0
{
	string m_Notes = ""
	int m_nFieldOutput = 20
	float m_flMax = 1.000000
	float m_flMin = -1.000000
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
	float m_flDegreesMax = 45.000000
	float m_flDegreesMin = -45.000000
}

C_INIT_RandomRotation C_INIT_RandomRotation_2
{
	string m_Notes = ""
	int m_nFieldOutput = 12
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmissionDuration = 0.500000
	float m_flEmitRate = 250.000000
}