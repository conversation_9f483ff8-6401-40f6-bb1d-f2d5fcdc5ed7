/* styles for the social widgets */


@define sectionbg_inactive: #222629;
@define sectionbg_active: gradient( linear, 0% 0%, 0% 100%, from( #3e474b ), to( #282e2d ) );
@define HalloweenHue: -35deg;

DOTAPlay
{
	vertical-align: bottom;
	horizontal-align: right;
	margin-top: 57px;
	height: 100%;
	opacity: 0.9999;
}

//
// Play Button
//

#LowPriorityWarning
{
	visibility: collapse;
}

DOTAPlay.MatchingRestriction #LowPriorityWarning
{
	visibility: visible;
	background-color: red;
}
DOTAPlay.MatchingRestriction #LowPrioritySection
{
	visibility: visible;
}

#SearchReturnToWeekendTourneyButton
{
	background-position: 50% 50%;
	background-image: url("s2r://panorama/images/weekend_tourney/battle_cup_return_psd.vtex");
	background-size: 100% 100%;   
    background-repeat: no-repeat;
	//wash-color: #00000066;
	border: 0px;
	
	width: 0px;
	opacity: 0.0;
	height: 40px;
	transition-property: width, opacity;
	transition-duration: .2s;
	margin: 6px 10px 0px 7px;
}

#ReturnVictoryIcon
{
	margin-top: 8px;
	margin-left: 9px;
	wash-color: #000000ee;
}


.CanReturnToWeekendTourney #SearchReturnToWeekendTourneyButton
{
	
	opacity: 1.0;
	width: 40px;
}

.CanReturnToWeekendTourney #SearchReturnToWeekendTourneyButton:hover
{
	brightness: 2;
}

#WeekendTourneyReturnToBracketButton Label
{
	horizontal-align: center;
}

.StateParticipatingInTourney #AbovePlayButtonStack
{
	margin-bottom: 68px;
}

#AbovePlayButtonStack
{
	flow-children: down;
	width: 330px;
	margin-bottom: 80px;
	margin-right: 58px;
	vertical-align: bottom;
	horizontal-align: right;
	transition-property: margin;
	transition-duration: .2s;
}

DOTAPlay.FindingMatch #AbovePlayButtonStack,
DOTAPlay.Connecting #AbovePlayButtonStack
{
	margin-bottom: 110px;
}

#AbandonButton
{
	//width: 330px;
	width: fill-parent-flow(2);
	height: 33px;
	vertical-align:bottom;
	horizontal-align: right;
	//margin-bottom: 90px;
	//margin-right: 58px;

	visibility: collapse;
}

.StateParticipatingInTourney #AbandonButton
{
	margin-bottom: 10px;
}

#AbandonButton:hover Label
{
	color: red;
}

#AbandonGameLabel,
#AbandonTourneyLabel
{
	visibility: collapse;
}

DOTAPlay.CanAbandonGame #AbandonButton,
DOTAPlay.CanAbandonGame #AbandonGameLabel,
DOTAPlay.CanAbandonTourney #AbandonButton,
DOTAPlay.CanAbandonTourney #AbandonTourneyLabel
{
	visibility: visible;
}

#SafeLeaveButton
{
	width: 330px;
	height: 0px;
	vertical-align:bottom;
	horizontal-align: right;

	opacity: 0;
	
	transition-property: opacity, height;
	transition-duration: 0.2s;
	transition-delay: 0.3s;
}

DOTAPlay.CanSafeLeaveGame #SafeLeaveButton
{
	opacity: 1;
	height: 33px;
}


#DisconnectButton
{
	width: 330px;
	height: 33px;

	min-width: 330px;
	min-height: 48px;

	margin-bottom: 21px;
	margin-right: 58px;
 
	padding: 4px 12px 4px 12px;
		
	vertical-align:bottom;
	horizontal-align: right;
	margin-right: 58px;

	background-color: gradient( linear, 0% 0%, 0% 100%, from( #722217 ), to( #DD4A29 ) );
	background-image: url("s2r://panorama/images/backgrounds/background_play_button_2x_png.vtex");
	background-size: 328px 50px;   

	border-top: 1px solid #ffffff05;
	border-right: 1px solid #00000088;
	border-left: 1px solid #ffffff05;
	border-bottom: 1px solid #00000088;

	visibility: collapse;
}


#DisconnectButton Image
{
	wash-color: red;
	width: 26px;
	height: 26px;
	margin: 2px;
	margin-left: 6px;
}

.DisconnectLabel 
{
	margin-top: 4px;
	vertical-align: middle;
	horizontal-align: center;	
}

#DisconnectButton Label
{
	text-transform: uppercase;
	color: white;
	font-size: 26px;
}

#DisconnectButton:hover Label
{
	color: red;
}

#DisconnectButton:active
{
	sound: "ui_custom_lobby_quit";
}

#ExtraReturnToWeekendTourneyButton
{
	background-position: 50% 50%;
	background-image: url("s2r://panorama/images/weekend_tourney/battle_cup_return_psd.vtex");
	background-size: 100% 100%;   
    background-repeat: no-repeat;
	//wash-color: #00000066;
	border: 0px;
	vertical-align: bottom;
	horizontal-align: right;
	width: 0px;
	height: 49px;
	transition-property: width, brightness;
	transition-duration: .2s;
	margin: 0px 340px 21px 0px;
	

	visibility: collapse;
}

#ExtraReturnToWeekendTourneyButton:hover
{
	brightness: 2;
}

#ExtraReturnVictoryIcon
{
	margin-top: 9px;
	margin-left: 10px;
	wash-color: #000000ee;
	width: 29px;
	height: 29px;
}

.CanReturnToWeekendTourney.CanReconnect #ExtraReturnToWeekendTourneyButton,
.CanReturnToWeekendTourney.CanDisconnect #ExtraReturnToWeekendTourneyButton
{
	visibility: visible;
	width: 49px;
}

DOTAPlay.CanDisconnect #DisconnectButton
{
	visibility: visible;
}

DOTAPlay.Connecting #DisconnectButton
{
	visibility: collapse;
}

.StateParticipatingInTourney DOTAPlay.CanReturnToWeekendTourney.CanDisconnect #DisconnectButton
{
	width: 270px;
	transition-property: width;
	transition-duration: .2s;
	min-width: 270px;
}

#ReconnectButton
{
	vertical-align: bottom;
	horizontal-align: right;
	height: 49px;
	opacity: 0;
	
	transition-property: opacity, background-color, box-shadow, border;
	transition-duration: 0.2s;
	transition-delay: 0.3s, 0s;	
}

DOTAPlay.CanReconnect #ReconnectButton
{
	opacity: 1;
}

.StateParticipatingInTourney DOTAPlay.CanReturnToWeekendTourney.CanReconnect #ReconnectButton
{
	width: 270px;
	transition-property: width;
	transition-duration: .2s;
}

DOTAPlay.Connecting #ReconnectButton
{
	visibility: collapse;
}

DOTAPlay.ReconnectInProgress .ReconnectLabel
{
	visibility: collapse;
}

.Spinner
{	
	horizontal-align: center;
	vertical-align: center;
}

.ReconnectSpinner
{
	visibility: collapse;
	horizontal-align: center;
	vertical-align: center;
	flow-children: right;
	width: fit-children;
}

DOTAPlay.ReconnectInProgress .ReconnectSpinner
{
	visibility: visible;
}

#WeekendTourneySetup
{
	visibility: collapse;
}
.WeekendTourneySetupVisible #WeekendTourneySetup
{
	visibility: visible;
}

#WeekendTourneyPlayStatusContainer
{
	
	vertical-align: bottom;
	horizontal-align: right;
}

#WeekendTourneyPlayStatusContainer Label
{
	text-align: center;
	text-transform: uppercase;
	line-height: 16px;
	color: #888;
	font-size: 16px;
	width:330px;
}

#WeekendTourneyPlayStatus,
#WeekendTourneyQueueDeadlineTooLateLabel,
#WeekendTourneyQueueDeadlineTimeRemainingContainer
{
	horizontal-align: center;
}

#WeekendTourneyQueueDeadlineTimeRemainingContainer
{
	flow-children: right;
	visibility: collapse;
}

.QueueDeadlineTimeRemaining #WeekendTourneyQueueDeadlineTimeRemainingContainer,
.QueueDeadlineExpiringSoon #WeekendTourneyQueueDeadlineTimeRemainingContainer
{
	visibility: visible;
}

.QueueDeadlineExpiringSoon #WeekendTourneyQueueDeadlineTimeRemainingLabel
{
	color: red;
}

#WeekendTourneyQueueDeadlineTooLateLabel
{
	visibility: collapse;
	color: red;
}



.QueueDeadlineTooLate #WeekendTourneyQueueDeadlineTooLateLabel
{
	visibility: visible;
}

.PlayButton
{
	width: 330px;
	max-height: 49px;

	margin-bottom: 21px;
	margin-right: 58px;
 
	padding: 4px 12px 4px 12px;
		
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5A615Ecc ), to( #879695cc ) );
	background-image: url("s2r://panorama/images/backgrounds/background_play_button_2x_png.vtex");
	background-size: 328px 50px;   
    //background-repeat: no-repeat;

		
	box-shadow: fill #002a6644 -4px -4px 8px 9px;
	
	border-top: 1px solid #ffffff44;
	border-right: 1px solid #00000088;
	border-left: 1px solid #ffffff44;
	border-bottom: 1px solid #00000088;

	opacity: 1.0;
	
	transition-property: box-shadow, background-color, opacity, transform;
	transition-delay: 0.0s;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;

}

.FindMatchSpinner
{
	visibility: collapse;
	horizontal-align: center;
	vertical-align: center;
}

.FindMsgInFlight .FindMatchSpinner, .WeekendTourneyParticipationMsgInFlight .FindMatchSpinner
{
	visibility: visible;
}

.FindMsgInFlight .PlayButton Label, .WeekendTourneyParticipationMsgInFlight .PlayButton Label
{
	opacity: 0.0;
}

// de-emphasize the button when on the custom games page
.CustomGamePageVisible #PlayButton
{
	opacity: 0.1;		
}

.CustomGamePageVisible #DeniedPlayButton
{
	opacity: 0.1;		
}

// raise the visibility back up if they have the tab open
.SlideOutVisible.CustomGamePageVisible #PlayButton
{
	opacity: 1.0;
}

.SlideOutVisible.CustomGamePageVisible #DeniedPlayButton
{
	opacity: 1.0;
}

.PlayButton:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5A615E ), to( #b7c6c5 ) );
	box-shadow: fill #414f4daa -4px -4px 8px 8px;
}

.PlayButton:active
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5A615E ), to( #ffffff ) );
}

.PlayButton Label
{
	margin-top: 2px;
	horizontal-align: center;
	font-size:28px;
	font-weight: bold;
	color: white;
	text-transform: uppercase;
	letter-spacing: 3px;
	text-align: center;
	height: 40px;
	text-overflow: shrink;
	
	transition-property: color;
	transition-delay: 0.0s;
	transition-duration: 0.2s;
}


#PlayButton:activationdisabled Label
{
	wash-color: #aaaaaa;
}

#PlayButton
{
	visibility: visible;
	vertical-align: bottom;
	horizontal-align: right;
}

DOTAPlay.FindMsgInFlight #PlayButton
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5Aa15E ), to( #87d69533 ) );  
}

DOTAPlay.FindMsgInFlight #PlayButton:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5Aa15E ), to( #87d695 ) );  
}

DOTAPlay.PlayButtonStartsSearching #PlayButton,
#ReconnectButton
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5Aa15E ), to( #87d69533 ) );  
}

DOTAPlay.PlayButtonStartsSearching #PlayButton:enabled:hover,
#ReconnectButton:enabled:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5Aa15E ), to( #87d695 ) );  
}

DOTAPlay.PlayButtonStartsSearching #PlayButton:activationdisabled
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #5A615Ecc ), to( #879695cc ) );
	saturation: 0.1;
	opacity: 0.5;
	box-shadow: none;
}

DOTAPlay.LobbySelectorVisible.MatchingRestriction #PlayButton,
DOTAPlay.LobbySelectorVisible #PlayButton
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #14161a ), to( #23262b ) );
	box-shadow: fill #00000000 0px 0px 0px 0px;
	border-top: 1px solid #ffffff03;
	border-left: 1px solid #ffffff03;
}

DOTAPlay.LobbyLeaderStart.MatchingRestriction #PlayButton,
DOTAPlay.LobbyLeaderStart #PlayButton
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #2d4881cc ), to( #486ca9cc ) );
	box-shadow: fill #486ca922 -4px -4px 8px 8px;
	border-top: 1px solid #ffffff03;
	border-left: 1px solid #ffffff03;
}

DOTAPlay.LobbyLeaderStart.MatchingRestriction #PlayButton:hover,
DOTAPlay.LobbyLeaderStart #PlayButton:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #2d4881cc ), to( #84acff ) );
	box-shadow: fill #486ca966 -4px -4px 18px 8px;
}

DOTAPlay.LobbySelectorVisible #PlayButton Label
{
	color: #ffffff11;
}

DOTAPlay.LobbySelectorVisible.MatchingRestriction #PlayButton:hover,
DOTAPlay.LobbySelectorVisible #PlayButton:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #14161a ), to( #23262b ) );
	box-shadow: fill #00000000 0px 0px 0px 0px;
}

DOTAPlay.LobbySelectorVisible.LobbyBrowserRowSelected #PlayButton
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #2d4881cc ), to( #486ca9cc ) );
	box-shadow: fill #486ca922 -4px -4px 8px 8px;
}

DOTAPlay.LobbySelectorVisible.LobbyBrowserRowSelected #PlayButton:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #2d4881cc ), to( #84acff ) );
	box-shadow: fill #486ca966 -4px -4px 18px 8px;
}


DOTAPlay.LobbySelectorVisible.LobbyBrowserRowSelected #PlayButton Label
{
	color: white;
}

DOTAPlay.LobbySelectorVisible.LobbyBrowserRowSelected #PlayButton Label
{
	color: white;
}

DOTAPlay.FindingMatch #PlayButton
{
	transform: translateX(-10px);	
}

DOTAPlay.1v1Disallowed.SectionVisible_1v1  #PlayButton
{
	background-color: #444444;
}

DOTAPlay.CanReturnToWeekendTourney #DeniedPlayButton,
DOTAPlay.InReadyUp #DeniedPlayButton,
DOTAPlay.ReturningToQueue #DeniedPlayButton,
DOTAPlay.PlayButtonHidden #DeniedPlayButton,
{
	visibility: collapse;
}

DOTAPlay.InReadyUp #PlayButton,
DOTAPlay.ReturningToQueue #PlayButton,
DOTAPlay.PlayButtonHidden #PlayButton,
{
	visibility: collapse;
}

#Searching
{
	//margin-right: 78px;
	padding-right: 78px;
	margin-bottom: 21px;
	width: 540px;
	vertical-align: bottom;
	horizontal-align: right;
	flow-children: down;
	opacity: 0.0;
	overflow: noclip;

	transition-property: transform, opacity;
	transition-duration: 0.4s;
	transition-delay: 0.0s;
	transition-timing-function: ease-in;

	transform: translatex(200px);
}


DOTAPlay.FindingMatch #Searching,
DOTAPlay.Connecting #Searching
{
	visibility: visible;
	opacity: 1.0;
	transform: none;
}

DOTAPlay.InReadyUp #Searching,
DOTAPlay.ReturningToQueue #Searching
{
	visibility: collapse;
}

DOTAPlay.FindingMatch #FXSearching
{
	opacity: 1.0;
}

DOTAPlay.MatchingRestriction #FXSearching
{
	wash-color: #ff5522;
}

#FXSearching
{
	width: 640px;
	height: 150px;
	horizontal-align: right;
	vertical-align: bottom;
	opacity-mask: url("s2r://panorama/images/masks/softedge_box_png.vtex");
	opacity: 0.0;
	
	transition-property: opacity;
	transition-duration: 0.3s;
	transition-timing-function: ease-in;
}

.HalloweenActive #FXSearching
{
	hue-rotation: HalloweenHue;
}

.StateParticipatingInTourney #FXSearching
{
	hue-rotation: 150deg;
	opacity: .5;
	brightness: .3;
}

#FXSearchingParticles
{
	width: 100%;
	height: 100%;
}

#JoinPlaytestButton
{
	vertical-align: bottom;
	horizontal-align: right;
	margin-bottom: 76px;
	hue-rotation: 300deg;
	saturation: 15;
	visibility: collapse;
}

.MainBranch.PlaytestActive #JoinPlaytestButton
{
	visibility: visible;
}

/* The .PlayButton at the end is so we're more specific than the above. Gross, but it works. */
DOTAPlay.FindingMatch #JoinPlaytestButton.PlayButton,
DOTAPlay.Connecting #JoinPlaytestButton.PlayButton,
DOTAPlay.InReadyUp #JoinPlaytestButton.PlayButton,
DOTAPlay.ReturningToQueue #JoinPlaytestButton.PlayButton,
DOTAPlay.PlayButtonHidden #JoinPlaytestButton.PlayButton,
DOTAPlay.SlideOutVisible #JoinPlaytestButton.PlayButton,
.FindMsgInFlight #JoinPlaytestButton.PlayButton
{
	visibility: collapse;
}

#SearchWeekendTourneyDeadlineMissed
{
	color: red;
}

#SearchWeekendTourneyDontStopWarning,
#SearchWeekendTourneyDeadlineMissed,
#SearchWeekendTourneyEligibleForRefund
{
	visibility: collapse;
	width: 540px;
	padding-right: 57px;
	text-align: right;
	text-transform: uppercase;
	line-height: 16px;
	color: #888;
}

.QueueDeadlineExpiringSoon #SearchWeekendTourneyDontStopWarning,
.QueueDeadlineExpiredOK #SearchWeekendTourneyDontStopWarning,
.QueueDeadlineTooLate #SearchWeekendTourneyDeadlineMissed,
.QueueDeadlineEligibleForRefund #SearchWeekendTourneyEligibleForRefund
{
	visibility: visible;
}

#SearchSettingsContainer
{
	width: 100%;
	height: 26px;
}

#SearchSettingsContainer .LeftRightFlow
{
	flow-children: none;
	horizontal-align: right;
}

DOTAPlay.Connecting #SearchSettingsContainer
{
	visibility: collapse;
}

.SearchGradient
{
	width: 100%;
	height: 100%;
	background-color: gradient( linear, 0% 0%, 100% 0%, from( transparent ), to( #59B0D222 ) );
	opacity-mask: url("s2r://panorama/images/masks/gradient_rightleft_png.vtex")1;
	border-top: 1px solid #59B0D212;
}

.HalloweenActive .SearchGradient
{
	hue-rotation: HalloweenHue;
}

.StateParticipatingInTourney .SearchGradient
{
	hue-rotation: 180deg;
	brightness: .8;
	//saturation: .5;
}

#SearchSettings, #SearchSettingsWeekendTourney
{
	horizontal-align: right;
	text-transform: uppercase;
	margin-right: 117px;
	//width: 90%;
	padding-top: 4px;
	font-size: 16px;
	color: #99dfee;
	vertical-align: bottom;
}

#SearchSettingsWeekendTourney
{
	horizontal-align: right;
	text-transform: uppercase;
	margin-right: 112px;
	//width: 90%;
	padding-top: 4px;
	font-size: 16px;
	color: #c3a771;
	vertical-align: bottom;
}

#SearchSettingsWeekendTourney
{
	visibility: collapse;
}

.SearchingForWeekendTourney #SearchSettings
{
	visibility: collapse;
}

.SearchingForWeekendTourney #SearchSettingsWeekendTourney
{
	visibility: visible;
}

#SearchingTime
{
	font-size: 16px;
	font-weight: thin;
	vertical-align: bottom;
	horizontal-align: right;
	margin-top: 2px;
	margin-left: 12px;
	margin-right: 57px;
	letter-spacing: 1px;
	
	transition-property: opacity;
	transition-duration: 0.3s;
	transition-timing-function: ease-in;
}


DOTAPlay.LANLobby #SearchSettings
{
	visibility: collapse;
}

#SearchingContainer
{
	margin-bottom: 0px;
	height: 50px;
	width: 100%;
	horizontal-align: right;
}

#SearchBackground
{
	background-color: gradient( linear, 0% 0%, 100% 0%, from( transparent ), to( #59B0D24a ) );
	border-top: 1px solid #59B0D277;
	border-bottom: 1px solid #59B0D277;
}

.HalloweenActive #SearchBackground
{
	hue-rotation: HalloweenHue;
}


#SearchControlsContainer
{
	flow-children: right;
	horizontal-align: right;
}

#SearchLabelContainer
{
	//margin-right: 56px;
	animation-name: SearchingAnimation;
	animation-duration: 1.2s;
	animation-timing-function: linear;
	animation-iteration-count: infinite;
	horizontal-align: right;
}

.StateParticipatingInTourney #SearchLabelContainer
{
	animation-name: SearchingAnimation-BattleCup;
}

#MinigameButton
{
	visibility: collapse;
	width: 50px;
	height: 50px;
	//background-color: black;
	horizontal-align: right;
	margin-top: 1px;
	margin-right: 2px;
	z-index: 5;
	background-image: url("s2r://panorama/images/compendium/spring2016/treecutter_psd.vtex");
	background-size: 100% 100%;
	background-position: 50% 50%;
	animation-name: Hop;
	animation-duration: 0.20s;
	animation-timing-function: linear;
	animation-iteration-count: infinite;	
}

.Season_International2016.SelfEventActive #MinigameButton
{
	visibility: visible;
	
}

#MinigameButton:hover
{
	animation-name: HopHover;
	brightness: 1.5;
}


@keyframes 'Hop'
{
	0%
	{
		transform: translateY(-2px);
	}

	50%
	{
		transform: translateY(2px);
	}
	
	100%
	{
		transform: translateY(-2px);
	}
}

@keyframes 'HopHover'
{
	0%
	{
		transform: translateY(-6px);
	}

	50%
	{
		transform: translateY(6px);
	}
	
	100%
	{
		transform: translateY(-6px);
	}
}


#MinigameButton Label
{
	font-size:30px;
}

@keyframes 'SearchingAnimation'
{
	0%
	{
		wash-color: white;
	}

	50%
	{
		wash-color: #7aa8b7;
	}
	
	100%
	{
		wash-color: white;
	}
}

@keyframes 'SearchingAnimation-BattleCup'
{
	0%
	{
		wash-color: white;
	}

	50%
	{
		wash-color: #fdd993cc;
	}
	
	100%
	{
		wash-color: white;
	}
}

#SearchingLabel
{
	opacity: 1.0;
	font-size: 30px;
	font-weight: thin;
	//color: #w;
	text-shadow: 0px 0px 6px 1.0 #c1c0ff;
	text-transform: uppercase;
	letter-spacing: 2px;
	margin-top: 8px;
	//margin-right: 57px;
	
	transition-property: opacity;
	transition-duration: 0.2s;
	transition-timing-function: ease-in;
}

.StateParticipatingInTourney #SearchingLabel
{
	text-shadow: 0px 0px 6px 1.0 #d77b16;
}

#SearchingCount
{
	visibility: collapse;
	margin-top: 40px;
}

DOTAPlay.SearchingCountVisible #SearchingCount
{
	visibility: visible;
	margin-top: 36px;
	opacity: .2;
	horizontal-align: right;
	//margin-right: 57px;
	font-size: 12px;
	color: white;
	text-shadow: 0px 0px 1px 3 black;
}

#CancelSearch
{
	wash-color: #00000066;
	width: 40px;
	height: 40px;
	horizontal-align: right;
	vertical-align: top;
	margin-top: 6px;
	margin-right: 57px;
	background-image: url("s2r://panorama/images/cancel_search_png.vtex");
	background-size: 100% 100%;
	background-position: 50% 50%;
	background-repeat: no-repeat;

}
	
#CancelSearch:hover
{
	wash-color: none;
}

DOTAPlay.ReadyUpAccepted #CancelSearch,
DOTAPlay.ReturningToQueue #CancelSearch
{
	visibility: collapse;
}

.PlayTabBackgroundImage
{
	width: 400px;
	height: 100%;
	background-size: 400px 667px;
	background-position: 0% 0%;
	background-repeat: no-repeat;
	opacity: 0.35;
	saturation: 0.55;

	transition-property: opacity, background-image;
	transition-duration: .4s;
	transition-timing-function: ease-in-out;
}

.SectionVisible_Normal .PlayTabBackgroundImage
{
	background-image: url("s2r://panorama/images/textures/playtabbg_normal_psd.vtex");
}

.SectionVisible_Ranked .PlayTabBackgroundImage
{
	background-image: url("s2r://panorama/images/textures/playtabbg_ranked_psd.vtex");
}

.SectionVisible_SeasonalRanked .PlayTabBackgroundImage
{
	background-image: url("s2r://panorama/images/textures/playtabbg_ranked_psd.vtex");
}

.SectionVisible_1v1 .PlayTabBackgroundImage
{
	background-image: url("s2r://panorama/images/textures/playtabbg_1v1_psd.vtex");
}

.SectionVisible_PracticeBots .PlayTabBackgroundImage
{
	background-image: url("s2r://panorama/images/textures/playtabbg_bots_psd.vtex");
}

.DarkMoonActive .SectionVisible_CustomGames .PlayTabBackgroundImage
{
	background-image: url("s2r://panorama/images/textures/playtabbg_darkmoon_psd.vtex");
	opacity: .6;
	saturation: .8;
}


.DarkMoonActive .SectionVisible_CustomGames #CustomGameDetailsBackground
{
	background-image: url("s2r://panorama/images/textures/playtabbg_custom_game_details_darkmoon_psd.vtex");
	background-size: contain;
	background-repeat: no-repeat;
	width: 100%;
	height: 792px;
	opacity: .6;
	saturation: .8;
	z-index: -1;
}



#CustomGameDetails
{
	horizontal-align: right;
	width: 323px;
	height: 100%;

	margin-right: 760px;
	padding-right: 4px;

	background-color: #291210;
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #3D2A36 ), to( #000000 ) );	

	flow-children: none;

	transform: translateX( 40px );
	opacity: 0.0;

	transition-property: transform, opacity;
	transition-duration: 0.2s;
}

.SectionVisible_CustomGames.SlideOutVisible #CustomGameDetails
{
	transform: translateX( 0px );
	opacity: 1.0;
}


.SectionVisible_CustomGames.LobbySelectorVisible #CustomGameDetails,
.SectionVisible_CustomGames.LobbyVisible #CustomGameDetails,
.SectionVisible_CustomGames.WeekendTourneySetupVisible #CustomGameDetails
{
	transform: translateX( 40px );
	opacity: 0.0;
}

#CustomGameDetailsTitleGlow,
#CustomGameDetailsTitle
{
	width: 100%;
	text-overflow: shrink;
	font-size: 44px;
	horizontal-align: center;
	text-transform: uppercase;
	text-align: center;
	margin-top: 20px;
	text-overflow: shrink;
}



#TitleContainer
{
	width: 100%;
	flow-children: none;
}

.DarkMoon #CustomGameDetailsTitleGlow,
.DarkMoon #CustomGameDetailsTitle
{
	letter-spacing: 5px;
	height: 42px;
	font-size: 32px;

	color: black;
	horizontal-align: center;
	text-align: center;
	text-shadow: 0px 0px 22px 2.0 #D26364;		
}

.DarkMoon #CustomGameDetailsTitleGlow
{
	text-shadow: 0px 0px 3px 2.0 #F3A998;	
}

#CustomGameDescription
{
	text-align: left;
	horizontal-align: center;

	padding: 16px;
	color: #999999;
	font-weight: thin;
}

#CustomGameStatsContainer
{
	width: 100%;
	height: fit-children;
	vertical-align: bottom;
	horizontal-align: left;
	flow-children: down;
}

.LobbySelectorVisible .PlayTabBackgroundImage
{
	opacity: 0;
}

#PlaySlideOutBackground
{
	background-color: gradient( linear, 100% 0%, 100% 100%, from( #111111 ), color-stop( 0.01, #202327 ), color-stop( 0.2, #141619 ), to( #000000 ) );
	box-shadow: black -4px -4px 8px 8px;
	height: 100%;
	width: 760px;
	horizontal-align: right;
	//border: 2px solid #ff000012;
	transition-property: transform, opacity, background-color;
	transition-duration: 0.4s, 0.0s;
	transition-delay: 0.0s, 0.4s;
	transition-timing-function: ease-in-out;
}

.AspectRatio5x4.DBLobbyVisible #PlaySlideOutBackground, .AspectRatio5x4.DBLobbySelectorVisible #PlaySlideOutBackground
{
	width: 670px;
}

#PlaySlideOut
{
	background-color: transparent;	
	height: 100%;
	width: 760px;
	width: 1484px;
	horizontal-align: right;
	padding-bottom: 0px;

	transform: translatex(760px);
	opacity: 0.0;

	transition-property: transform, opacity, background-color;
	transition-duration: 0.4s, 0.0s;
	transition-delay: 0.0s, 0.4s;
	transition-timing-function: ease-in-out;

	flow-children: none;
	margin-left: 320px;
}

#Lobby
{
	opacity: 0.0;
	pre-transform-scale2d: 1.0;
	transition-property: opacity, pre-transform-scale2d;
	transition-duration: 0.4s;
	//transition-delay: 0.4s;
	transition-timing-function: ease-in-out;
}

DOTAPlay.SlideOutVisible.LobbyVisible #Lobby
{
	opacity: 1.0;
	pre-transform-scale2d: 1.0;
	transition-delay: 0.0s;
}

DOTAPlay.SlideOutVisible #PlaySlideOut
{
	transform: translatex(361px);
	sound: "ui_find_match_slide_in";
	sound-out: "ui_find_match_slide_out";
	opacity: 1.0;

	transition-property: transform, opacity;
	transition-duration: 0.4s, 0.4s;
	transition-delay: 0.0s, 0.4s;
}

DOTAPlay.LobbySelectorVisible #PlaySlideOut
{
	transform: translatex(44px);
	margin-left: 16px;
}

DOTAPlay.WeekendTourneySetupVisible #PlaySlideOut
{
	transform: translatex(44px);
	margin-left: 16px;
}

DOTAPlay.SlideOutVisible.LobbyVisible #PlaySlideOut
{
	transform: translatex(44px);
	margin-left: 16px;
	margin-left: 316px;
}

.AspectRatio16x10 DOTAPlay.SlideOutVisible.LobbyVisible #PlaySlideOut
{
	transform: translatex(44px);
	margin-left: 16px;
	margin-left: 252px;
}
.AspectRatio5x4 DOTAPlay.SlideOutVisible.LobbyVisible #PlaySlideOut
{
	transform: translatex(0px);
	margin-left: 0px;
}

DOTAPlay.SlideOutVisible #CloseButton
{
	opacity: 1.0;
	transition-delay: 0.0s;
}

DOTAPlay.LobbyVisible #CloseButton
{
	//visibility: collapse;
}

#CloseButton
{
	opacity: 0.0;
	height: 100%;
	horizontal-align: right;
	padding-right: 8px;
	background-color: gradient( radial, 100% 25%, 0% 15%, 90% 50%, from( #00000000 ), to( #00000000 ) );
	transition-property: background-color, opacity;
	transition-duration: 0.15s;
	transition-delay: 0.55s;
	transition-timing-function: ease-in-out;
}

#CloseButton:hover
{
	background-color: gradient( radial, 100% 25%, 0% 15%, 90% 50%, from( #48575e44 ), to( #00000000 ) );
}

	
#CloseButtonIcon
{
	margin-left: 6px;
	vertical-align: center;
	width: 32px;
	height: 32px;
	
	background-image: url("s2r://panorama/images/control_icons/arrow_solid_right_png.vtex");
	background-repeat: no-repeat;
	background-position: 50% 50%;
	background-size: 100% 100%;
	
	transform: translateX(0px);
	wash-color: #445255;
	
	transition-property: wash-color, transform;
	transition-duration: 0.15s;
	transition-timing-function: ease-in-out;
}

#CloseButton:hover #CloseButtonIcon
{
	transform: translateX(4px);
	wash-color: #445255dd;
}

#LobbyBrowser
{
	width: 100%;
	height: 860px;
}

#LobbySelectorContent
{
	background-color: gradient( linear, 100% 0%, 100% 100%, from( #111111 ), color-stop( 0.01, #202327 ), color-stop( 0.2, #141619 ), to( #000000 ) );
	box-shadow: black -4px -4px 8px 8px;
	width: 720px;
	opacity: 0.0;
	padding-top: 16px;
	margin-right: 46px;
	padding-right: 40px;
	flow-children: down;
	horizontal-align: right;
	transition-property: opacity;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
}

DOTAPlay.LobbySelectorVisible #LobbySelectorContent
{
	opacity: 1.0;
}

DOTAPlay.LobbyVisible #LobbySelectorContent
{
	opacity: 0.0;
}

#WeekendTourneySetup
{
	//background-color: gradient( linear, 100% 0%, 100% 100%, from( #111111 ), color-stop( 0.01, #202327 ), color-stop( 0.2, #141619 ), to( #000000 ) );
	background-color: gradient( linear, 100% 0%, 100% 100%, from( #111111 ), color-stop( 0.01, #1c1e20 ), color-stop( 0.06, #1b1b1b ), color-stop( 0.9, #151515 ), to( #000000 ) );
	box-shadow: black -4px -4px 8px 8px;
	height: 920px;
	width: 715px;
	opacity: 0.0;
	padding-top: 16px;
	margin-right: 46px;
	padding-right: 40px;
	horizontal-align: right;
	transition-property: opacity;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
	//border-left:  1px solid #ffffff02;
}

DOTAPlay.WeekendTourneySetupVisible #WeekendTourneySetup
{
	opacity: 1.0;
}

#PlaySlideOutContent
{
	width: 330px;
	vertical-align: bottom;
	horizontal-align: center;
	margin-left: 320px;
	margin-bottom: 70px;
	opacity: 1.0;

	margin-top: 20px;
	
	transition-property: opacity, transform;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
}

.AspectRatio4x3 #PlaySlideOutContent
{
	margin-left: 0px;
	margin-right: 50px;
}

.AspectRatio5x4 #PlaySlideOutContent
{
	margin-left: 0px;
	margin-right: 140px;
}

.AspectRatio16x10 #PlaySlideOutContent
{
	margin-left: 240px;
}

DOTAPlay.LobbySelectorVisible #PlaySlideOutContent,
DOTAPlay.WeekendTourneySetupVisible #PlaySlideOutContent
{
	opacity: 0.0;
	//transform: translateX(430px);
}

DOTAPlay.LobbyVisible #PlaySlideOutContent
{
	opacity: 0.0;
}

Label
{
	color: white;
	font-size: 16px;
}


#SeasonalRankedSectionHeader
{
	width: 100%;
}

#SeasonalRankedSectionContent
{
	opacity: 0.0;
	width: 100%;

	transition-property: opacity;
	transition-duration: 0.2s;
	transition-timing-function: ease-in;
	visibility: collapse;
}

.SectionVisible_SeasonalRanked.EligibleForSeasonalRanked #SeasonalRankedSectionContent
{
	opacity: 1.0;
	visibility: visible;
}

#SeasonalRankedSection
{
	width: 100%;
	margin-bottom: 8px;

	visibility: collapse;
}

// This is the locked state
DOTAPlay.SectionVisible_SeasonalRanked #SeasonalRankedSection
{
	background-color: sectionbg_active;
	height: fit-children;
	opacity: 1.0;
}

DOTAPlay.SectionVisible_SeasonalRanked.EligibleForSeasonalRanked #SeasonalRankedSection
{
	background-color: sectionbg_active;
	opacity: 1.0;
}

.SeasonalRankedMMRSection
{
	padding: 16px;
	padding-bottom: 8px;
	margin-top: 10px;
	margin-bottom: 10px;
	margin-left: 12px;
	margin-right: 12px;
	background-color: #15171944;
	box-shadow: inset #00000066 0px 2px 6px 0px;
	
	width: 100%;
	padding-top: 12px;

	flow-children: down;
	
	transition-property: height;
	transition-duration: 0.2s;
	transition-delay: 0.2s;
	transition-timing-function: ease-in;
}

DOTAPlay.SectionVisible_SeasonalRanked.EligibleForSeasonalRanked .SeasonalRankedMMRSection
{
	margin-bottom: 0px;
}

.SeasonalRankedMMRSection Label
{
	horizontal-align: center;
	horizontal-align: left;
}

.SeasonalRankedMMRSection .SectionHeaderLabel
{
	color: #d6d6d6;
}

#SeasonalRankedSection .InfoIcon
{
	wash-color: #888888FF;
	width: 12px;
	height: width-percentage( 100% );
	margin-left: 1px;
	margin-top: 1px;
}

#SeasonalRankedLocked
{
	margin-left: 2px;
	margin-top: -2px;
	width: 25px;
	height: 28px;
	wash-color: #e54e42;
}

#SeasonalRankedSectionContent_Locked
{
	opacity: 0.0;
	width: 100%;

	transition-property: opacity;
	transition-duration: 0.2s;
	transition-timing-function: ease-in;
	visibility: collapse;
}

#SeasonalRankedSectionContent_Locked Label
{
	width: 274px;
}

#SeasonalRankedSectionContent_Locked Label a
{
	color: #B1CBC6;
}

.SectionVisible_SeasonalRanked #SeasonalRankedSectionContent_Locked
{
	opacity: 1.0;
	visibility: visible;
}

.EligibleForSeasonalRanked #SeasonalRankedSectionContent_Locked
{
	visibility: collapse;
}

#SeasonalRankedGameModes ToggleButton:selected .TickBox
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #e7f6f5 ), to( #a0d6d7 ) );
	border: 3px solid #000000;
	box-shadow: #5b62bf77 -4px -4px 8px 8px;
}

#SeasonalRankedGameModes ToggleButton Label
{
	margin-top: 4px;
	font-size: 16px;
	margin-left: 8px;
	color: baseText;

	transition-property: color;
	transition-duration: 0.2s;
	transition-timing-function: linear;
}

#SeasonalRankedGameModes ToggleButton:disabled:hover Label
{
	color: #d2d6d1;
}

#SeasonalRankedGameModes ToggleButton:disabled Label
{
	color: white;
}

.SeasonalRankedTransfer #SeasonalRankedGameModes
{
	visibility: collapse;
}

.SeasonalRankedTransfer #SeasonalRankedSectionContent .InfoBlock
{
	visibility: collapse;
}

ToggleButton:disabled Label
{
	color: black;
}

ToggleButton:disabled:selected .TickBox
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #222222 ), to( #333333 ) );
	border: 3px solid #000000;
	box-shadow: #33333377 -4px -4px 8px 8px;
}

#SeasonalSoloMMRLabel
{
	margin-right: 8px;
	color: #959596;
}

#SeasonalPartyMMRLabel
{
	margin-right: 8px;
	color: #959596;
}

#SeasonalSoloMMRValue
{
	visibility: collapse;
}

#SeasonalSoloMMRCalibrating
{
	visibility: collapse;
	color: white;
}

#SeasonalSoloMMRNoData
{
	visibility: collapse;
	color: white;
}

DOTAPlay.SeasonalSoloMMRCalibrated #SeasonalSoloMMRValue
{
	visibility: visible;
}

DOTAPlay.SeasonalSoloMMRCalibrating #SeasonalSoloMMRCalibrating
{
	visibility: visible;
}

DOTAPlay.SeasonalSoloMMRNoData #SeasonalSoloMMRNoData
{
	visibility: visible;
}

#SeasonalPartyMMRValue
{
	visibility: collapse;
	color: white;
}

#SeasonalPartyMMRCalibrating
{
	visibility: collapse;
	color: white;
}

#SeasonalPartyMMRNoData
{
	visibility: collapse;
	color: white;
}

DOTAPlay.SeasonalPartyMMRCalibrated #SeasonalPartyMMRValue
{
	visibility: visible;
}

DOTAPlay.SeasonalPartyMMRCalibrating #SeasonalPartyMMRCalibrating
{
	visibility: visible;
}

DOTAPlay.SeasonalPartyMMRNoData #SeasonalPartyMMRNoData
{
	visibility: visible;
}

#SeasonalPartyMMRValue i, #SeasonalSoloMMRValue i
{
	text-decoration: none;
	font-style: normal;
	color: #757576;
	font-weight: lighter;
}

#SeasonalRankedSection TextButton Label
{
	color: #B1CBC6;
}

#SeasonalRankedSection TextButton:disabled Label
{
	color: #717B76;
}

#SeasonalRankedSection TextButton:enabled:hover Label
{
	text-decoration: underline;
	color: #FFFFFF;
}

#SeasonalRankedLeaderboardsButton
{
	horizontal-align: center;
	margin-top: 15px;
	margin-bottom: 10px;
	margin-left: 10px;
	margin-right: 10px;
}

#SeasonalSoloMMRTransfer
{
	margin-top: 4px;
	margin-bottom: 12px;
}

#SeasonalPartyMMRTransfer
{
	margin-top: 4px;
	margin-bottom: 4px;
}

#RankedSectionHeader
{
	width: 100%;
}

#RankedSectionContent
{
	opacity: 0.0;
	width: 100%;

	transition-property: opacity;
	transition-duration: 0.2s;
	transition-timing-function: ease-in;
	visibility: collapse;
}

.SectionVisible_Ranked.EligibleForRanked #RankedSectionContent
{
	opacity: 1.0;
	visibility: visible;
}

.RankedMMRSection
{
	padding: 16px;
	padding-bottom: 8px;
	margin-top: 10px;
	margin-bottom: 0px;
	margin-left: 12px;
	margin-right: 12px;
	background-color: #15171944;
	box-shadow: inset #00000066 0px 2px 6px 0px;
	
	width: 100%;
	padding-top: 12px;

	flow-children: down;
	
	transition-property: height;
	transition-duration: 0.2s;
	transition-delay: 0.2s;
	transition-timing-function: ease-in;
}

.RankedMMRSection Label
{
	horizontal-align: center;
	horizontal-align: left;
}

.RankedMMRSection .SectionHeaderLabel
{
	color: #d6d6d6;
}

#RankedPhoneStatusContainer
{
    width: 100%;
    height: 100px;
}

.PhoneStatusPerfectWorld #RankedPhoneStatusContainer
{
    visibility: collapse;
}

.PhoneStatusAnchored #RankedPhoneStatusContainer
{
    height: 75px;
}

.PhoneStatusAnchored #RankedGameModes
{
    padding-bottom: 25px;
}

#RankedPhoneStatusContainer Panel
{
    width: 100%;
    visibility: collapse;
}

.PhoneStatusNone #RankedPhoneStatusNone
{
    visibility: visible;
}

.PhoneStatusSteam #RankedPhoneStatusSteam
{
    visibility: visible;
}

.PhoneStatusAnchored #RankedPhoneStatusAnchored
{
    visibility: visible;
}

#RankedPhoneStatusAnchored Label
{
    color: baseText;
}

#RankedPhoneStatusAnchored Label a
{
    color: #555D52;
}

#RankedPhoneStatusContainer Button
{
    margin-left: 0px;
    margin-right: 0px;
    width: 100%;
}

#RankedPhoneStatusContainer Button Label
{
    horizontal-align: center;
}

#RankedPhoneStatusNone
{
    margin-top: 8px;
    margin-left: 12px;
    margin-right: 12px;
    width: 100%;
}

#RankedPhoneStatusNone > Label
{
    width: 306px;
}

#RankedPhoneStatusNone > Label
{
    color: #d75a52;
}

#RankedPhoneStatusSteam
{
    padding-left: 12px;
    padding-right: 12px;
}

#RankedPhoneStatusSteam > Label
{
    color: #d75a52;
}

#RankedPhoneStatusSteam Button
{
    border: 1px solid #33cc6655;
    background-color: #33cc6611;
}

#RankedPhoneStatusSteam Button Label
{
    color: #66ffaa;
}

#RankedPhoneStatusSteam Button:enabled:hover Label
{
	color: white;
}

#RankedPhoneStatusSteam Button:enabled:hover
{
	border: 1px solid #22ffaaaa;
	background-color: #33aa6644;
}

#RankedPhoneStatusSteam Button:active
{
	background-color: #238a4644;
	sound: "ui_select_blue";
}

#RankedPhoneStatusSteam Button:active Label
{
	color: #76edba;
}

#RankedPhoneStatusAnchored
{
    padding-top: 12px;
    padding-left: 14px;
    padding-right: 14px;
}

#SoloMMRLabel
{
	margin-right: 8px;
	color: #959596;
}

#PartyMMRLabel
{
	margin-right: 8px;
	color: #959596;
}

#TeamMMRLabel
{
	margin-right: 8px;
	color: #959596;
}

#SoloMMRValue
{
	visibility: collapse;
}

#SoloMMRCalibrating
{
	visibility: collapse;
	color: white;
}

#SoloMMRNoData
{
	visibility: collapse;
	color: white;
}

DOTAPlay.SoloMMRCalibrated #SoloMMRValue
{
	visibility: visible;
}

DOTAPlay.SoloMMRCalibrating #SoloMMRCalibrating
{
	visibility: visible;
}

DOTAPlay.SoloMMRNoData #SoloMMRNoData
{
	visibility: visible;
}

#PartyMMRValue
{
	visibility: collapse;
	color: white;
}

#PartyMMRCalibrating
{
	visibility: collapse;
	color: white;
}

#PartyMMRNoData
{
	visibility: collapse;
	color: white;
}

DOTAPlay.PartyMMRCalibrated #PartyMMRValue
{
	visibility: visible;
}

DOTAPlay.PartyMMRCalibrating #PartyMMRCalibrating
{
	visibility: visible;
}

DOTAPlay.PartyMMRNoData #PartyMMRNoData
{
	visibility: visible;
}

#TeamMMRValue
{
	visibility: collapse;
	color: white;
}

#TeamMMRCalibrating
{
	visibility: collapse;
	color: white;
}

#TeamMMRNoData
{
	visibility: collapse;
	color: white;
}

DOTAPlay.TeamMMRCalibrated #TeamMMRValue
{
	visibility: visible;
}

DOTAPlay.TeamMMRCalibrating #TeamMMRCalibrating
{
	visibility: visible;
}

DOTAPlay.TeamMMRNoData #TeamMMRNoData
{
	visibility: visible;
}

#RankedSection
{
	width: 100%;
	margin-bottom: 8px;
}

// This is the locked state
DOTAPlay.SectionVisible_Ranked #RankedSection
{
	background-color: sectionbg_active;
	height: 320px;
	opacity: 1.0;
}

DOTAPlay.SectionVisible_Ranked.EligibleForRanked #RankedSection
{
	background-color: sectionbg_active;
	height: 400px;
	opacity: 1.0;
}

DOTAPlay.SectionVisible_Ranked.EligibleForRanked.ValidTeamIdentity #RankedSection
{
	background-color: sectionbg_active;
	height: 440px;
	opacity: 1.0;
}

DOTAPlay.SectionVisible_Ranked.EligibleForRanked.PhoneStatusPerfectWorld #RankedSection
{
    height: 300px;
}

DOTAPlay.SectionVisible_Ranked.EligibleForRanked.ValidTeamIdentity.PhoneStatusPerfectWorld #RankedSection
{
    height: 340px;
}

DOTAPlay.SectionVisible_Normal #GameModeSection
{
	background-color: sectionbg_active;
	opacity: 1.0;
}

DOTAPlay.SectionVisible_1v1 #1v1Section
{
	background-color: sectionbg_active;
	height: 245px;
	opacity: 1.0;
}

DOTAPlay.SectionVisible_1v1 #1v1Section
{
	background-color: sectionbg_active;
	height: 145px;
	opacity: 1.0;
}

DOTAPlay.SectionVisible_1v1.1v1Disallowed #1v1Section
{
	background-color: sectionbg_active;
	height: 175px;
	opacity: 1.0;
}

#1v1PartyWarning
{
	margin-top: 8px;
	visibility: collapse;
	color: red;
}

DOTAPlay.1v1Disallowed #1v1PartyWarning
{
	visibility: visible;
}

DOTAPlay.SectionVisible_PracticeBots #PracticeBotsSection
{
	background-color: sectionbg_active;
	height: 324px;
}

.DarkMoonActive DOTAPlay.SectionVisible_CustomGames #CustomGamesSection
{
	background-color: sectionbg_active;
	height: 142px;
}

.PlayDividerLine
{
	background-color: #20223088;
	margin-left: 4px;
	margin-right: 4px;
	margin-top: 12px;
	margin-bottom: 12px;
	height: 2px;
	width: 100%;
}

.PlayTabRadio
{
	margin-left: 4px;
	margin-bottom: 6px;
}

.CoopOrSolo
{
	margin-left: 4px;
	margin-bottom: 6px;
	text-transform: uppercase;
}

.PlayBotsLabel
{
	margin-left: 32px;
	color: #999999;
}

#RankedCheckBox
{
	margin-top: 5px;
	margin-bottom: 5px;
}

#RankedOff
{    
	visibility: visible;
	margin-left: 8px;
	margin-top: 14px;
	color: #dafef6;
}

DOTAPlay.RankedMode #RankedOff
{
	visibility: collapse;
}

#RankedOn
{
	visibility: collapse;
	margin-left: 8px;
	margin-top: 14px;
	color: #dafef6;
}

DOTAPlay.RankedMode #RankedOn
{
	visibility: visible;
}

#RankedLocked
{    
	margin-left: 8px;
	margin-top: 5px;
	visibility: visible;
	width: 14px;
	height: 14px;
	wash-color: #91ABA6;
}

// enable when we unlock ranked
.EligibleForRanked #RankedLocked
{    
	visibility: collapse;
}

#RankedSectionContent_Locked
{
	opacity: 0.0;
	width: 100%;

	transition-property: opacity;
	transition-duration: 0.2s;
	transition-timing-function: ease-in;
	visibility: collapse;
}

.SectionVisible_Ranked #RankedSectionContent_Locked
{
	opacity: 1.0;
	visibility: visible;
}

.EligibleForRanked #RankedSectionContent_Locked
{
	visibility: collapse;
}

ToggleButton Label
{
	text-transform: uppercase;
}

RadioButton Label
{
	text-transform: uppercase;
}

#CustomGamesSectionContent RadioButton Label,
#1v1SectionContent RadioButton Label
{
	text-transform: none;
}

#1v1SectionContent
{
	padding: 8px;
	width: 100%;
	visibility: collapse;
}

#GameStyle_1v1 Label
{
	text-transform: none;
}

DOTAPlay.SectionVisible_1v1 #1v1SectionContent
{
	visibility: visible;
}

#PracticeBotsSectionContent
{
	width: 100%;
	height: 397px;
	padding: 8px;
	opacity: 0.0;
	transition-property: opacity;
	transition-duration: 0.2s;
	transition-timing-function: ease-in;
}

#PlayBotsSolo, #PlayBotsCoop
{
}

#BotSectionContainer
{
	width: 100%;
}

.BotSectionColumn
{
	width: fill-parent-flow( 1.0 );
}

#CustomGamesSection
{
	visibility: collapse;
}

DOTAPlay.MatchmadeCustomGameAvailable #CustomGamesSection
{
	visibility: visible;
}

#CustomGamesSectionContent
{
	width: 100%;
	height: 100%;
	padding: 8px;
	flow-children: down;
	visibility: collapse;
}

.CurrentCustomGamePointContainer
{
	flow-children: right;
	horizontal-align: center;
}

#CurrentPoints
{
	flow-children: down;
}

.CurrentCustomGamePointContainer.Multiplier
{
	visibility: collapse;
}



.DarkMoon .CustomGamePointContainer.Multiplier
{
	horizontal-align: center;
	vertical-align: bottom;
	margin-bottom: 32px;
	flow-children: right;
}

.DarkMoon .CustomGamePointContainer.Multiplier Label
{
	font-size: 26px;
	color: #57A6D8;
	text-shadow: 0px 0px 8px 1 #57A6D8;
	text-overflow: shrink;
	height: 32px;
	text-align: center;
}

.CustomGamePointContainer.Multiplier
{
	animation-name: BonusActive;
	animation-duration: 0.81s;
	animation-timing-function: linear;
	animation-iteration-count: infinite;
	tooltip-position: left;
	tooltip-body-position: 50%;
	opacity: 0;
	
	transition-property: opacity;
	transition-duration: .2s;
	transition-timing-function: ease-in-out;
}


.CustomGamePointsMultiplierActive .CustomGamePointContainer.Multiplier
{
	opacity: 1;
}

@keyframes 'BonusActive'
{
	0%
	{
		pre-transform-scale2d: 1;
		brightness: 2;
	}
	50%
	{
		pre-transform-scale2d: 0.98;
		brightness: 1;
	}
	100%
	{
		pre-transform-scale2d: 1;
		brightness: 2;
	}
}



.CustomGamePointContainer .InfoIcon
{
	margin-bottom: 4px;
	width: 16px;
	height: 16px;
	wash-color: #57A6D8;
}


DOTAPlay.CustomGamePointsMultiplierActive .CurrentCustomGamePointContainer.Multiplier
{
	visibility: visible;
}

.DimPlayText
{
	color: #DDAA9C44;
	font-size: 14px;
	font-weight: thin;
	text-transform: uppercase;
	text-align: center;
	horizontal-align: center;
	letter-spacing: 1px;
}

.DimPlayText.Multiplier
{
	color: #BB7D73;
	font-size: 26px;
	margin-bottom: 16px;
	padding-right: 16px;
	padding-left: 16px;
	text-shadow: 0px 0px 8px 1.0 #D2636488;		
}

.DimPlayText.CustomGamePoints
{
	color: white;
	font-size: 32px;
}


#ClaimReward
{
	width: 330px;
	min-height: 36px;
	box-shadow: fill transparent 0px 0px 0px 0px;
	background-color: black;
	//border: 1px solid #484c4944;
	horizontal-align: right;
	vertical-align: bottom;
	margin-bottom: 20px;
	margin-top: 8px;
	margin-left: 32px;
	margin-right: 32px;
	padding: 0px;
	flow-children: down;
	
	background-image: url("s2r://panorama/images/textures/glassbutton_darkmoon_psd.vtex");
	background-size: 100%;
	background-position: 50% 50%;
	background-repeat: no-repeat;
	
	transition-property: box-shadow, background-image, background-color;
	transition-duration: .16s;
	transition-timing-function: ease-in-out;
	
	border-radius: 6px;
	visibility: collapse;
}

#ClaimReward:enabled
{
	box-shadow: #F78F9005 -9px -9px 18px 18px;
	visibility: visible;
}

#ClaimReward:enabled:hover
{
	background-image: url("s2r://panorama/images/textures/glassbutton_darkmoon_hover_psd.vtex");
	box-shadow: #F78F9015 -4px -4px 8px 8px;
}

.ClaimLabel
{
	width: 100%;
	text-align: center;
	margin-top: 3px;
	font-size: 20px;   
	font-weight: thin;
	text-transform: uppercase;
	letter-spacing: 2px;
	font-size: 22px;
	color: grey;
}

.ClaimLabel.PointAmount
{
	font-size: 16px;   
}

#ClaimReward:enabled .ClaimLabel
{
	color: white;
}

DOTAPlay.DarkMoon #ClaimReward:enabled
{
	border: 1px solid #62451144;
}

DOTAPlay.DarkMoon .ClaimLabel
{
	margin: 0px;
	padding-top: 8px;	
}

DOTAPlay.DarkMoon .ClaimLabel.PointAmount
{
	margin-top: -4px;
	padding-top: 0px;	
	padding-bottom: 8px;	
}


/* Dark Moon color overrides */
DOTAPlay.DarkMoon #GameStyle_CustomGames
{
	background-color: #7F3432;
}

DOTAPlay.DarkMoon #GameStyle_CustomGames:hover
{
	background-color: #BE474C;
}

DOTAPlay.DarkMoon #GameStyle_CustomGames:hover Label
{
	color: white;
}


DOTAPlay.DarkMoon #GameStyle_CustomGames Label
{
	color: #FFBEA0;
}

DOTAPlay.DarkMoon #CustomGamesSectionContent .RadioBox
{
	background-color: gradient( radial, 50% 50%, 0% 0%, 50% 50%, from( #FFB1AB ), to( #B26469 ) );
	box-shadow: #D87271aa -3px -3px 6px 6px;
}


DOTAPlay.DarkMoon #GameStyle_CustomGames:selected
{
	background-color: gradient( linear, 0% 0%, 100% 0%, from( #FFd9c6 ), color-stop( 0.015, #FFC9B6 ), color-stop( 0.02, #7F3432 ), to( #7F3432 ) );
}

DOTAPlay.DarkMoon #CustomGamesSectionContent
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #55262F ), to( #2A131B ) );
}

#BabyRoshan
{
	width: 512px;
	height: 512px;
	transform: translateY(-42px) translateX(-82px);
	z-index: 3;
	transition-property: opacity, pre-transform-scale2d;
	transition-duration: .32s;
	transition-timing-function: ease-in-out;	
	opacity: 0;
	pre-transform-scale2d: .9;
}

DOTAPlay.DarkMoon.SectionVisible_CustomGames #BabyRoshan
{
	opacity: 1;
	pre-transform-scale2d: 1;
}


DOTAPlay.SectionVisible_CustomGames #CustomGamesSectionContent
{
	visibility: visible;
}

DOTAPlay.SectionVisible_PracticeBots #PracticeBotsSectionContent
{
	opacity: 1.0;
}

DOTAPlay.SectionVisible_CustomGames #CustomGamesSectionContent
{
	opacity: 1.0;
}


.GameStyle
{
	background-color: #222629;
	background-color: #222629f4;
	width: 100%;
	height: 50px;
	padding-top: 12px;
	padding-bottom: 8px;
	border: 1px solid #000000DD;
}

.GameStyle:hover
{
	background-color: #3e474b;
}

.GameStyle:selected
{
	background-color: gradient( linear, 0% 0%, 100% 0%, from( #d8efee ), color-stop( 0.015, #b1dddd ), color-stop( 0.02, #3e474b ), to( #3e474b ) );
	padding-left: 12px;
	border: 1px solid #222629;
	box-shadow: fill #00000066 -4px -2px 16px 8px;
	flow-children: none;
	width: 100%;
	height: fit-children;
	padding-top: 12px;
	font-weight: bold;
	transition-property: background-color;
	transition-duration: 0.3s;
	transition-timing-function: ease-in;
	sound: "ui_find_match_change_options";
}

.GameStyle Label
{
	font-size: 20px;
	font-weight: thin;
	color: #666666;
	text-transform: uppercase;
	letter-spacing: 2px;
	margin-left: 20px;
}

.GameStyle:selected Label
{
	font-size: 20px;
	font-weight: bold;
	margin-left: 8px;
	
}

.GameStyle .RadioBox
{
	visibility: collapse;
}

.PlayTabSection 
{
	height: 50px;
	width: 100%;   
	background-color: sectionbg_inactive;
	background-color: none;
	
	margin-bottom: 8px;
	transition-property: height, opacity;
	transition-duration: 0.3s;
	transition-timing-function: ease-in;
}

#GameModeSection
{
	flow-children: down;
	margin-bottom: 12px;
}

DOTAPlay.SectionVisible_Normal #GameModeSection
{
	height: 340px;
}

#NormalSectionContent
{
	opacity: 0.0;
	width: 100%;

	transition-property: opacity;
	transition-duration: 0.2s;
	transition-timing-function: ease-in;
}

DOTAPlay.SectionVisible_Normal #NormalSectionContent
{
	opacity: 1.0;
}

.GameModeCheckBox
{
	tooltip-position: left;
	tooltip-body-position: 0% 50%;
}


#RankedGameModes, #SeasonalRankedGameModes
{
	padding-top: 8px;
	padding-left: 8px;
}

#UnrankedGameModes
{
	visibility: visible;
	width: 100%;
	padding-top: 8px;
	padding-left: 8px;
}

DOTAPlay.RankedMode #UnrankedGameModes
{
	visibility: collapse;
}

.SectionHeader
{
	width: 100%;
}

.LowPriWarningText
{
	color: #960e0e;
}

#ExpandGameModesButton
{
	horizontal-align: right;
	width: 20px;
	height: 20px;

	background-image: url("s2r://panorama/images/control_icons/icon_collapse_png.vtex");
	background-size: 100% 100%;
	background-position: 50% 50%;
	background-repeat: no-repeat;

	visibility: collapse;
}

#CollapseGameModesButton
{
	horizontal-align: right;
	width: 20px;
	height: 20px;

	background-image: url("s2r://panorama/images/control_icons/icon_collapse_png.vtex");
	background-size: 100% 100%;
	background-position: 50% 50%;
	background-repeat: no-repeat;

	visibility: visible;
}

.Divider
{
	height: 0px;
	background-color: #404b4a;
	width: 100%;
	margin-top: 0px;
	margin-bottom: 4px;
}

#RegionLanguageSection
{
	width: 100%;
	flow-children: none;
	margin-bottom: 0px;
}

#RegionLanguageSection Label
{
	color: white;
}

#LanguageButtonLabel
{
	text-transform: uppercase;
	padding: 2px 4px 0px 4px;
	border: 2px solid #aaaaaa00;
	wash-color: #657373;
	margin-right: 16px;
	
	transition-property: border, wash-color, background-color;
	transition-delay: 0.0s;
	transition-duration: 0.15s;
	transition-timing-function: linear;
}

#LanguageButtonLabel:hover
{
	border: 2px solid #65737322;
	wash-color: white;
	background-color: #65737301;
}

#RegionButton 
{
	horizontal-align: right;
	padding: 2px 4px 0px 4px;
	border: 2px solid #aaaaaa00;
	wash-color: #657373;
	
	transition-property: border, wash-color, background-color;
	transition-delay: 0.0s;
	transition-duration: 0.15s;
	transition-timing-function: linear;
}

#RegionButton Label
{
	text-transform: uppercase;
}

#RegionButton:hover
{
	border: 2px solid #65737322;
	wash-color: white;
	background-color: #65737301;
}

#CustomGamesButton
{
	horizontal-align: center;
	margin-bottom: 4px;
	width: 70%;
	margin-top: 10px;
	background-color: #3e464b;
	
	transition-property: wash-color;
	transition-delay: 0.0s;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
	flow-children: right;
}

#CustomGamesButtonIcon
{
	width: 14px;
	height: 14px;
	margin-bottom: 13px;
	margin-left: 6px;
	vertical-align: middle;
	background-image: url("s2r://panorama/images/control_icons/arrow_top_right_png.vtex");
	background-size: contain;
	background-repeat: no-repeat;
	transform: scaleX(-1) translateX( 5px ) translateY( 5px );
	wash-color: #91aba6;
	
	
	transition-property: wash-color, transform;
	transition-delay: 0.0s;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
}

.CustomGamesButton:hover #CustomGamesButtonIcon
{
	wash-color: white;
	transform: scaleX(-1) translateX( 0px )  translateY( 0px );
}

.LobbyButton
{
	margin-bottom: 4px;
	width: 100%;
	height: fit-children;

	padding-top: 10px;
	padding-bottom: 10px;
	background-color: sectionbg_inactive;
	
	margin-bottom: 8px;
	
	transition-property: wash-color;
	transition-delay: 0.0s;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
}

#WeekendTourneyButton
{
	flow-children: down;
}

.ExpandWeekendTourney #WeekendTourneyButton
{
	background-color: green;
	background-image: url("s2r://panorama/images/weekend_tourney/battle_cup_tooltip_backer_play_psd.vtex");
	background-size: 100% 100%;
	box-shadow: black -4px -4px 8px 8px;
	transition-property: brightness;
	transition-duration: .2s;
	padding-bottom: 0px;
}

.SelectedTourneyDivisionIsChampionsCup #WeekendTourneyButton #BattleCupRegularSeasonContainer,
#WeekendTourneyButton #ChampionsCupContainer
{
    visibility:collapse;
}

.SelectedTourneyDivisionIsChampionsCup #WeekendTourneyButton #ChampionsCupContainer
{
    visibility:visible;
}

.ExpandWeekendTourney #WeekendTourneyButton:hover
{
	brightness: 2;
}

#WeekendTourneyTitle
{
	width: 100%;
	flow-children: right;
}
.BattleCupVictoryIcon
{
	margin-top: 4px;
	margin-left: 2px;
	width: 22px;
	height: 22px;
	margin-left: 6px;
}

#WeekendTourneyStatus
{
	width: 100%;

	background-color: none;
	
	//margin-bottom: 8px;
	
	transition-property: wash-color;
	transition-delay: 0.0s;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
	visibility: collapse;
	
	padding-bottom: 12px;
	padding-top: 5px;
}

.ExpandWeekendTourney #WeekendTourneyStatus
{
	visibility: visible;
}

#PrivateLobbyButton
{
}

#CreateLobbyShortcutButton
{
}

.PrivateLobbyButtonIcon
{
	width: 16px;
	height: 16px;
	vertical-align: middle;
	background-image: url("s2r://panorama/images/control_icons/double_arrow_left_png.vtex");
	background-position: 1.5% 50%;
	background-size: 16px 16px;
	background-repeat: no-repeat;
	wash-color: #91aba6;
	
	transform: translateX( 6px );
	
	transition-property: transform, wash-color;
	transition-delay: 0.0s;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
}

.ExpandWeekendTourney #WeekendTourneyButton .PrivateLobbyButtonIcon
{
	wash-color: none;
}

.LobbyButton:hover
{
}

.LobbyButton:hover .PrivateLobbyButtonIcon
{
	background-position: 0% 50%;
	transform: translateX( 0px );
	wash-color: white;
}

.LobbyButton Label
{
	font-size: 20px;
	font-weight: thin;
	color: #999999;
	text-transform: uppercase;
	letter-spacing: 2px;
	margin-top: 4px;
	margin-left: 32px;
	vertical-align: center;
	
	transition-property: color;
	transition-delay: 0.0s;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
}

.ExpandWeekendTourney #WeekendTourneyButton.LobbyButton Label
{
	color: #ffe0a3;
	text-shadow: 0px 0px 6px 1.0 #fca64a;
	font-weight: bold;
}


.LobbyButton:hover Label
{
	color: white;
}

.Lobbydivider
{
	height: 1px;
	background-color: #404b4aaa;
	width: 100%;
	margin-bottom: 10px;
}

#CreateLobby
{
	horizontal-align: right;
	padding: 8px;
	padding-bottom: 0px;
	width: 100%;
	margin-top: 0px;
}

#CreateLobbyControls
{
	flow-children: right;
	horizontal-align: right;
	vertical-align: bottom;
	margin-top: 16px;
	margin-right: 8px;
}

#HostLocallyCheckBox
{
	margin-right: 22px;
}

#CreateLobbyButton
{
	width: 330px;
	min-height: 36px;
	box-shadow: fill transparent 0px 0px 0px 0px;
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #102149cc ), to( #1e3565cc ) );
	border-top: 1px solid #486ca944;
	border-left: 1px solid #486ca944;
	horizontal-align: right;
	margin: 0px;
	padding: 0px;
}

#CreateLobbyButton:hover
{
	box-shadow: fill transparent 0px 0px 0px 0px;
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #102149ff ), to( #345288 ) );
}


#CreateLobbyButton Label
{
	//color: #486ca9;
}

#CreateLobbyButton:disabled
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #444444cc ), to( #888888cc ) );
}

#CreateLobbyText
{
	width: 100%;
	text-align: center;
	margin-top: 3px;
	font-size: 20px;   
	font-weight: thin;
	text-transform: uppercase;
	letter-spacing: 2px;
	font-size: 22px;

}

#LowPrioritySection
{
	width: 100%;
	height: fit-children;
	margin-bottom: 8px;
	padding: 12px;    
	padding-left: 12px;
	background-color: none;

	visibility: collapse;
}


#LowPriGlow
{
	vertical-align: bottom;
	height: 50%;
	width: 100%;
	background-color: gradient( radial, 50% 90%, 0% 0%, 50% 50%, from( #88000055 ), to( #00000000 ) );    
	opacity: 0;

	transition-property: opacity;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
}

DOTAPlay.SlideOutVisible.LobbySelectorVisible #LowPriGlow, DOTAPlay.SlideOutVisible.LobbyVisible #LowPriGlow
{
	opacity: 0;
}

DOTAPlay.MatchingRestriction.SlideOutVisible.LobbySelectorVisible #PlaySlideOut, DOTAPlay.MatchingRestriction.SlideOutVisible.LobbyVisible #PlaySlideOut
{
	background-color: gradient( linear, 100% 0%, 100% 100%, from( #111111 ), color-stop( 0.01, #202327 ), color-stop( 0.2, #141619 ), to( #000000 ) );
}

DOTAPlay.MatchingRestriction #LowPriGlow
{
	opacity: 1;
}

#ReadyUp
{
	visibility: collapse;
	flow-children: down;
}

.ReadyUpSlot
{
	background-color: #222222;
	width: 16px;
	height: 16px;
	margin-bottom: 8px;
}

.ReadyUpSlot.Declined
{
	background-color: #732a2d;
}

.ReadyUpSlot.Accepted
{
	background-color: green;
}

// lobby settings edit button
#SettingsEditButton
{
	horizontal-align: right;
	margin-right: 100px;
}

#SettingsEditButton Label
{
	color: #DDDDDD;
	margin-top: 2px;
}

#SettingsEditButton:hover Label
{
	color: white;
}

#SettingsEditButton Image
{
	margin-top: -1px;
}

#LowPrioritySection Label
{
	color: #d04c23;
	visibility: collapse;
}

.MatchDenied_AccountDisabled #MatchDeniedReason_AccountDisabled
{
	visibility: visible;
}

.MatchDenied_MatchDisabledParty #MatchDeniedReason_MatchDisabledParty
{
	visibility: visible;
}

.MatchDenied_MatchDisabledLocal #MatchDeniedReason_MatchDisabledLocal
{
	visibility: visible;
}

.MatchDenied_LowPriorityParty #MatchDeniedReason_LowPriorityParty
{
	visibility: visible;
}

.MatchDenied_LowPriorityLocal #MatchDeniedReason_LowPriorityLocal
{
	visibility: visible;
}


.MatchDenied #PlayButton
{
	background-color: #530202;
	border-top: 1px solid #6D2929;
	border-left: 1px solid #6D2929;
}

DOTAPlay.MatchDenied #PlayButton:hover
{
	background-color: #6D2929;
}

DOTAPlay.SlideOutVisible.LobbySelectorVisible.MatchDenied_AccountDisabled #DeniedPlayButton, DOTAPlay.SlideOutVisible.LobbyLeaderStart.MatchDenied_AccountDisabled #DeniedPlayButton, DOTAPlay.SlideOutVisible.SectionVisible_PracticeBots #DeniedPlayButton
{
	visibility: collapse;
}

DOTAPlay.SlideOutVisible.MatchDenied_AccountDisabled #DeniedPlayButton, DOTAPlay.SlideOutVisible.MatchDenied_AccountDisabled.SectionVisible_PracticeBots.CoopBotSelected #DeniedPlayButton
{
	visibility: visible;
}

#DeniedPlayButton
{
	visibility: visible;
	vertical-align: bottom;
	horizontal-align: right;
	background-color: #530202;
 
	border-top: 1px solid #330000;
	border-left: 1px solid #330000;
	box-shadow: #******** -4px -4px 8px 8px;
	opacity: 0;

	transition-property: opacity;
	transition-duration: 0.0s;
}

#DeniedPlayButton Label
{
	color: #330000;
}

DOTAPlay.MatchDenied_LowPriorityParty #PlayButton
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #576f3f ), to( #554422 ) );  
}

DOTAPlay.MatchDenied_LowPriorityParty #PlayButton:hover
{
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #779950 ), to( #735E2C ) );  
}

// Leaver Consequences

.LeaverConsequences
{
	margin-right: 420px;
	margin-bottom: 72px;
	width: 630px;
	height: fit-children;
	vertical-align: bottom;
	horizontal-align: right;
	flow-children: right;
	padding-top: 5px;
	padding-left: 10px;
	padding-right: 10px;

	transition-property: opacity;
	transition-duration: 0.4s;
	transition-delay: 0.3s;
	transition-timing-function: ease-in;

	tooltip-position: top;
	tooltip-body-position: 100% 50%;
	tooltip-arrow-position: 96% 50%;

	opacity: 0;
}

.Warning
{
	height: 32px;
}


.LeaverConsequences Label
{
	text-align: right;
}

DOTAPlay.CanDisconnect .LeaverConsequences
{
	margin-right: 58px;
}

.Warning
{
	color: #b63b21;
}

.InfoIcon
{
	wash-color: #b63b21dd;
	margin-bottom: 4px;
}

.Consequences
{
	text-align: right;
	//width: 100%;
	font-size: 18px;
}

#ConsequencesContainer
{
	flow-children: right;
	horizontal-align: right;
}

.LeaverConsequence_Safe #LeaverConsequences
{
	opacity: 1;
	visibility: visible;
}

DOTAPlay.Connecting #LeaverConsequences
{
	visibility: collapse;
}

.LeaverConsequence_Safe #LeaverConsequences Label
{
	text-shadow: 0px 0px 6px 1.0 #00ff00aa;
	text-align: right;
	horizontal-align: right;
	color: #bbffdd;
}

.LeaverConsequence_Warning #LeaverConsequences_Warning
{
	opacity: 1;
	visibility: visible;
}

DOTAPlay.Connecting #LeaverConsequences_Warning
{
	visibility: collapse;
}

#BackgroundClickCatcher
{
	width: 100%;
	height: 100%;
	opacity: 1;
	color: red;
	//visibility: collapse;
}

#TeamIdentity
{
	visibility: collapse;
	flow-children: right;

	padding: 16px;
	padding-bottom: 8px;
	margin-top: 10px;
	margin-bottom: 0px;
	margin-left: 12px;
	margin-right: 12px;
	background-color: #15171944;
	box-shadow: inset #00000066 0px 2px 6px 0px;
	
	width: 100%;
	padding-top: 12px;
}

DOTAPlay.ValidTeamIdentity.EligibleForRanked #TeamIdentity
{
	visibility: visible;
}

#UseTeamIdentityCheckbox
{
	width: fill-parent-flow( 1.0 );
}

#TeamPrevious
{
	vertical-align: center;
}

#TeamNext
{
	vertical-align: center;
}

#TeamMMRRow
{
	visibility: collapse;
}

DOTAPlay.ValidTeamIdentity.EligibleForRanked #TeamMMRRow
{
	visibility: visible;
}

#NoValidTeamIdentitiesLabel
{
	visibility: visible;
	width: 100%;
	text-align: center;
	margin-top: 10px;
	color: baseText;
	font-size: 16px;
}

DOTAPlay.ValidTeamIdentity.EligibleForRanked #NoValidTeamIdentitiesLabel
{
	visibility: collapse;
}

.ViolatorTag
{
	margin-top: -64px;
	margin-bottom: 42px;
	horizontal-align: right;
	margin-right: 6px;
	background-color: #FADB5E;
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #FADB5E ), to( #FF937D ) );
	border-radius: 4px;
	border: 1px solid black;
	padding-left: 6px;
	padding-top: 2px;
	padding-right: 4px;
	text-transform: uppercase;
	letter-spacing: 1px;
	text-align: center;
	font-size: 14px;
	color: #800;
	font-weight: bold;
	transform: translateY(0px);
	opacity: 0;

	transition-property: transform, opacity;
	transition-duration: .2s;
}

.GCStatusVisible .ViolatorTag
{
	opacity: 0;
}

.MatchmadeCustomGameAvailable .ViolatorTag
{
	opacity: 1;
}


.SectionVisible_CustomGames .ViolatorTag
{
	transform: translateY(26px);
}

#TeamLogo
{
	height: 50px;
    width: 50px;
    margin: 5px;
    border-radius: 7px 7px;
    background-color: #000000;
}

#IntroInformationPanel
{
	width: 100%;
	height: 364px;

	horizontal-align: center;
	vertical-align: bottom;
	background-color: #00000066;
	margin-bottom: 16px;
	border: 1px solid #8B414A33;
	padding: 4px;
	margin: 8px;
}


.DetailsQuad
{
	width: 285px;
	height: 31%;
	margin: 4px;
	vertical-align: middle;
	border: 1px solid #8B414A12;
	background-color: #4D211A22;
	padding-right: 12px;
	padding-left: 12px;
}

.DetailsQuad:hover .DetailsTextContainer
{
//	opacity: 1;
}

.DetailsQuad:hover .DetailsTitle
{
	//color: #ffffff00;
}

.DetailsText
{
	opacity: 1;
	background-color: none;
	vertical-align: middle;
	horizontal-align: center;
	padding: 0px;
	padding-left: 16px;
	padding-right: 16px;
	width: fit-children;
}

.DetailsTextContainer
{
	width: 100%;
	height: 100%;
	/* TI6 */
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #321F25aa ), color-stop( 0.5, #321F25ee), to( #321F25ee) );
	
	/* Fall 2016 */
	background-color: gradient( linear, 0% 0%, 0% 100%, from( #4e6425aa ), color-stop( 0.5, #4e6425ee), to( #4e6425ee) );
	opacity: 0;	
	transition-property: opacity;
	transition-timing-function: ease-in;
	transition-duration: 0.2s;	
}


.DetailsTitle
{
	margin: 0;
	vertical-align: middle;
	horizontal-align: center;
	text-align: center;
	font-size: 16px;
}

.DetailsNext
{
	transform: rotateZ(90deg);
	width: 42px;
	horizontal-align: center;
	height: 48px;
	hue-rotation: -6deg;
	saturation: .8;
	background-image: url("s2r://panorama/images/compendium/spring2016/wagerdesc_next_psd.vtex");
	background-repeat: no-repeat;
	background-size: cover;		
}

#DetailNext1
{
	margin-top: 94px;
}

#DetailNext2
{
	margin-top: 212px;
}

.DetailsQuad .InfoIcon
{
	horizontal-align: right;
	margin: 8px;
	opacity: .1;
}



	//static CPanoramaSymbol symLeaveConsequencesVisible( "LeaverConsequence" );
	//static CPanoramaSymbol symLeaveConsequence_Warning( "LeaverConsequence_Warning" );
	//static CPanoramaSymbol symLeaveConsequence_Safe( "LeaverConsequence_Safe" );
	//static CPanoramaSymbol symLeaveConsequence_Neutral( "LeaverConsequence_Neutral" );
