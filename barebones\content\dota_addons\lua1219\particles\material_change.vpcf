<!-- kv3 encoding:text:version{e21c7f3c-8a33-41c5-9977-a76d3a32aa0d} format:generic:version{7412167c-06e9-4698-aff2-e63eb59037e7} -->
{
	_class = "CParticleSystemDefinition"
	m_bShouldHitboxesFallbackToRenderBounds = false
	m_nMaxParticles = 1
	m_flConstantRadius = 1.000000
	m_ConstantColor =
	[
		0,
		0,
		0,
		255,
	]
	m_nConstantSequenceNumber1 = 18
	m_bShouldSort = false
	m_Renderers =
	[
		{
			_class = "C_OP_RenderModels"
			m_nManualFrameField = 18
			m_nSubModelField = 19
			m_nBodyGroupField = 19
			m_bManualAnimFrame = true
			m_bOrientZ = true
			m_nSkin = 1
			m_nModelCP = 0
			m_hOverrideMaterial = resource:"materials/models/blob/pedestal_ice_surface.vmat"
			m_bSuppressTint = true
			m_ModelList =
			[
				{
					m_model = resource:"models/heroes/storm_spirit/storm_spirit.vmdl"
				},
			]
			m_bAnimated = true
			m_bForceDrawInterlevedWithSiblings = true
			m_nLOD = 1
			m_bIgnoreNormal = true
			m_nAnimationScaleField = 18
		},
	]
	m_Operators =
	[
		{
			_class = "C_OP_BasicMovement"
		},
		{
			_class = "C_OP_PositionLock"
		},
		{
			_class = "C_OP_Orient2DRelToCP"
			m_nFieldOutput = 12
			m_flRotOffset = 90.000000
			m_flOpStartFadeOutTime = 0.100000
			m_flOpEndFadeOutTime = 0.100000
			m_bDisableOperator = true
		},
		{
			_class = "C_OP_RemapCPtoScalar"
			m_flOutputMax = 10.000000
			m_flInputMax = 10.000000
			m_nCPInput = 2
			m_nField = 1
		},
		{
			_class = "C_OP_OscillateScalarSimple"
			m_nField = 3
			m_Rate = 1.000000
			m_Frequency = 255.000000
			m_bDisableOperator = true
		},
		{
			_class = "C_OP_EndCapTimedDecay"
		},
		{
			_class = "C_OP_RampScalarLinearSimple"
			m_flEndTime = 0.500000
			m_Rate = 30.000000
			m_nField = 18
			m_bDisableOperator = true
		},
		{
			_class = "C_OP_OscillateScalarSimple"
			m_Rate = 15.000000
			m_Frequency = 0.500000
			m_nField = 18
			m_flOscMult = 1.000000
			m_flOscAdd = 0.000000
			m_bDisableOperator = true
		},
		{
			_class = "C_OP_RemapCPOrientationToRotations"
		},
		{
			_class = "C_OP_SequenceFromModel"
			m_nFieldOutput = 13
			m_nFieldOutputAnim = 18
			m_flInputMax = 4000.000000
			m_flOutputMax = 4000.000000
		},
		{
			_class = "C_OP_RemapScalar"
			m_nFieldInput = 18
			m_nFieldOutput = 18
			m_flInputMax = 100.000000
			m_flOutputMax = 3000.000000
		},
	]
	m_Initializers =
	[
		{
			_class = "C_INIT_RandomLifeTime"
			m_fLifetimeMin = 1.000000
			m_fLifetimeMax = 1.000000
		},
		{
			_class = "C_INIT_CreateWithinSphere"
		},
		{
			_class = "C_INIT_PositionOffset"
			m_OffsetMin =
			[
				2.000000,
				0.000000,
				0.000000,
			]
			m_OffsetMax =
			[
				2.000000,
				0.000000,
				0.000000,
			]
			m_bLocalCoords = true
			m_bDisableOperator = true
		},
		{
			_class = "C_INIT_RandomAlphaWindowThreshold"
			m_flMin = 18.000000
			m_flMax = 18.000000
			m_bDisableOperator = true
		},
		{
			_class = "C_INIT_RemapCPtoScalar"
			m_nCPInput = 2
			m_flInputMax = 1000.000000
			m_nFieldOutput = 13
			m_flOutputMax = 1000.000000
		},
	]
	m_Emitters =
	[
		{
			_class = "C_OP_InstantaneousEmitter"
			m_nParticlesToEmit = 1
		},
	]
}