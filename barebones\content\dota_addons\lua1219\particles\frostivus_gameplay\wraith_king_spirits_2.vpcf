<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 3
	string m_pszSnapshotName = ""
	float(3) m_BoundingBoxMin = ( -30.000000, -30.000000, -30.000000 )
	float(3) m_BoundingBoxMax = ( 30.000000, 30.000000, 30.000000 )
	float m_flCullRadius = -1.000000
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 0.750000
	int(4) m_ConstantColor = ( 0, 248, 146, 255 )
	int m_nConstantSequenceNumber1 = 1
	float m_flMaxDrawDistance = 4000.000000
	float m_flNoDrawTimeToGoToSleep = 0.100000
	bool m_bShouldSort = false
	int m_nMinCPULevel = 1
	float m_flMaxRecreationTime = -1.000000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderDeferredLight_0,
		&C_OP_RenderModels_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_VelocityMatchingForce_0,
		&C_OP_SetControlPointToCenter_0,
		&C_OP_VectorNoise_0,
		&C_OP_BasicMovement_0,
		&C_OP_MaxVelocity_0,
		&C_OP_SetPerChildControlPoint_0,
		&C_OP_OscillateVector_0,
		&C_OP_RemapSpeed_0,
		&C_OP_Spin_0,
		&C_OP_SpinYaw_0,
		&C_OP_EndCapTimedDecay_0,
		&C_OP_LerpEndCapScalar_0,
		&C_OP_PositionLock_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_RandomTrailLength_0,
		&C_INIT_NormalOffset_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0,
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		&C_OP_AttractToControlPoint_0,
		&C_OP_TurbulenceForce_0,
		&C_OP_TwistAroundAxis_0,
		&C_OP_AttractToControlPoint_2,
		&C_OP_AttractToControlPoint_4
	]
	CParticleOperator*[] m_Constraints = 
	[
		&C_OP_ConstrainDistance_0
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/wraith_king_spirit_trail_c.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/wraith_king_spirit_trail_c.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/wraith_king_spirit_trail_c.vpcf"
		}
	]
}

C_OP_RenderDeferredLight C_OP_RenderDeferredLight_0
{
	float m_flStartFalloff = 0.100000
	string m_Notes = ""
	float m_flRadiusScale = 3.000000
	float m_flAlphaScale = 4.000000
	string m_hTexture = "materials\\particle\\particle_flares\\aircraft_yellow.vtex"
}

C_OP_RenderModels C_OP_RenderModels_0
{
	bool m_bOrientZ = true
	string m_Notes = ""
	int m_nSkin = 1
	string m_ActivityName = ""
	string m_EconSlotName = ""
	string m_hOverrideMaterial = ""
	ModelReference_t[] m_ModelList = 
	[
		ModelReference_t
		{
			string m_model = "models/particle/sphere3.vmdl"
		}
	]
	bool m_bAnimated = true
}

C_OP_Decay C_OP_Decay_0
{
	int m_nOpEndCapState = 1
	string m_Notes = ""
}

C_OP_VelocityMatchingForce C_OP_VelocityMatchingForce_0
{
	float m_flDirScale = 0.013129
	float m_flSpdScale = 0.002500
	string m_Notes = ""
}

C_OP_SetControlPointToCenter C_OP_SetControlPointToCenter_0
{
	int m_nCP1 = 5
	float(3) m_vecCP1Pos = ( 0.000000, 0.000000, 32.000000 )
	string m_Notes = ""
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	float(3) m_vecOutputMax = ( 10.000000, 10.000000, 10.000000 )
	int m_nFieldOutput = 0
	float(3) m_vecOutputMin = ( -10.000000, -10.000000, -10.000000 )
	bool m_bAdditive = true
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float(3) m_Gravity = ( 0.000000, 0.000000, 300.000000 )
	float m_fDrag = 0.080000
	string m_Notes = ""
}

C_OP_MaxVelocity C_OP_MaxVelocity_0
{
	float m_flMaxVelocity = 1750.000000
	string m_Notes = ""
}

C_OP_SetPerChildControlPoint C_OP_SetPerChildControlPoint_0
{
	string m_Notes = ""
	int m_nNumControlPoints = 3
}

C_OP_OscillateVector C_OP_OscillateVector_0
{
	string m_Notes = ""
	bool m_bOffset = true
	float(3) m_RateMin = ( -200.000000, -200.000000, -200.000000 )
	float(3) m_RateMax = ( 200.000000, 200.000000, 200.000000 )
	float(3) m_FrequencyMax = ( 2.000000, 2.000000, 2.000000 )
	bool m_bProportional = false
	float m_flEndTime_min = 99999998430674944.000000
	float m_flEndTime_max = 99999998430674944.000000
}

C_OP_RemapSpeed C_OP_RemapSpeed_0
{
	bool m_bScaleInitialRange = true
	float m_flOutputMax = 1.500000
	float m_flOutputMin = 0.750000
	float m_flInputMax = 1500.000000
	string m_Notes = ""
}

C_OP_Spin C_OP_Spin_0
{
	string m_Notes = ""
	int m_nSpinRateDegrees = 12
}

C_OP_SpinYaw C_OP_SpinYaw_0
{
	string m_Notes = ""
	int m_nSpinRateDegrees = 100
}

C_OP_EndCapTimedDecay C_OP_EndCapTimedDecay_0
{
	string m_Notes = ""
	float m_flDecayTime = 0.250000
}

C_OP_LerpEndCapScalar C_OP_LerpEndCapScalar_0
{
	string m_Notes = ""
	float m_flLerpTime = 0.250000
	float m_flOutput = 0.000000
}

C_OP_PositionLock C_OP_PositionLock_0
{
	string m_Notes = ""
	float m_flPrevPosScale = 3.000000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	float m_fRadiusMin = 25.000000
	float m_fRadiusMax = 25.000000
	float(3) m_vecDistanceBias = ( 0.010000, 0.010000, 0.000000 )
	string m_Notes = ""
	float m_fSpeedMax = 100.000000
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMin = 8.000000
	float m_fLifetimeMax = 22.000000
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 5
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	float m_flDegreesMin = -5.000000
	float m_flDegreesMax = 5.000000
	string m_Notes = ""
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	float(3) m_vecOutputMin = ( -100.000000, -100.000000, 0.000000 )
	float(3) m_vecOutputMax = ( 100.000000, 100.000000, 0.000000 )
	string m_Notes = ""
}

C_INIT_RandomTrailLength C_INIT_RandomTrailLength_0
{
	float m_flMinLength = 0.500000
	float m_flMaxLength = 1.000000
	string m_Notes = ""
}

C_INIT_NormalOffset C_INIT_NormalOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMin = ( -1.000000, -1.000000, -1.000000 )
	float(3) m_OffsetMax = ( 1.000000, 1.000000, 1.000000 )
	bool m_bNormalize = true
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 10.000000
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 3
}

C_OP_AttractToControlPoint C_OP_AttractToControlPoint_0
{
	float m_fForceAmount = 80.000000
	float m_fFalloffPower = -0.625000
	float(3) m_vecComponentScale = ( 1.000000, 1.000000, 0.500000 )
	string m_Notes = ""
	int m_nControlPointNumber = 3
}

C_OP_TurbulenceForce C_OP_TurbulenceForce_0
{
	float(3) m_vecNoiseAmount0 = ( 450.000000, 450.000000, 450.000000 )
	float m_flNoiseCoordScale1 = 0.100000
	float(3) m_vecNoiseAmount1 = ( -510.000000, -510.000000, -510.000000 )
	float m_flNoiseCoordScale2 = 3.000000
	float(3) m_vecNoiseAmount2 = ( 100.000000, 100.000000, 100.000000 )
	float m_flNoiseCoordScale3 = 5.000000
	float(3) m_vecNoiseAmount3 = ( -300.000000, -300.000000, -300.000000 )
	string m_Notes = ""
}

C_OP_TwistAroundAxis C_OP_TwistAroundAxis_0
{
	float(3) m_TwistAxis = ( 0.100000, 0.000000, 1.000000 )
	bool m_bLocalSpace = true
	float m_fForceAmount = -150.000000
	string m_Notes = ""
	int m_nControlPointNumber = 3
}

C_OP_AttractToControlPoint C_OP_AttractToControlPoint_2
{
	int m_nControlPointNumber = 5
	float m_fFalloffPower = -0.010000
	float m_fForceAmount = -150.000000
	string m_Notes = ""
}

C_OP_AttractToControlPoint C_OP_AttractToControlPoint_4
{
	int m_nControlPointNumber = 5
	float m_fFalloffPower = 0.500000
	float m_fForceAmount = -500.000000
	string m_Notes = ""
}

C_OP_ConstrainDistance C_OP_ConstrainDistance_0
{
	float m_fMaxDistance = 56.000000
	string m_Notes = ""
	float m_fMinDistance = 15.000000
	int m_nControlPointNumber = 3
}