<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	int m_nInitialParticles = 0
	float(3) m_BoundingBoxMin = ( -10.000000, -10.000000, -10.000000 )
	float(3) m_BoundingBoxMax = ( 10.000000, 10.000000, 10.000000 )
	int m_nSnapshotControlPoint = 0
	string m_pszSnapshotName = ""
	string m_pszTargetLayerID = ""
	string m_hReferenceReplacement = ""
	string m_pszCullReplacementName = ""
	float m_flCullRadius = 0.000000
	float m_flCullFillCost = 1.000000
	int m_nCullControlPoint = 0
	float m_flMaxRecreationTime = 0.000000
	string m_hFallback = ""
	int m_nFallbackMaxCount = -1
	string m_hLowViolenceDef = ""
	uint(4) m_ConstantColor = ( 255, 255, 255, 255 )
	float(3) m_ConstantNormal = ( 0.000000, 0.000000, 1.000000 )
	float m_flConstantRadius = 5.000000
	float m_flConstantRotation = 0.000000
	float m_flConstantRotationSpeed = 0.000000
	float m_flConstantLifespan = 1.000000
	int m_nConstantSequenceNumber = 0
	int m_nConstantSequenceNumber1 = 0
	int m_nGroupID = 0
	float m_flMaximumTimeStep = 0.100000
	float m_flMaximumSimTime = 0.000000
	float m_flMinimumSimTime = 0.000000
	float m_flMinimumTimeStep = 0.000000
	int m_nMinimumFrames = 0
	int m_nMinCPULevel = 0
	int m_nMinGPULevel = 0
	bool m_bViewModelEffect = false
	bool m_bScreenSpaceEffect = false
	float m_flMaxDrawDistance = 100000.000000
	float m_flStartFadeDistance = 200000.000000
	float m_flNoDrawTimeToGoToSleep = 8.000000
	int m_nMaxParticles = 1000
	int m_nSkipRenderControlPoint = -1
	int m_nAllowRenderControlPoint = -1
	int m_nAggregationMinAvailableParticles = 0
	float m_flAggregateRadius = 0.000000
	float m_flStopSimulationAfterTime = 1000000000.000000
	float(3) m_vControlPoint1DefaultOffsetRelativeToControlPoint0 = ( 0.000000, 0.000000, 0.000000 )
	string m_Name = ""
	CParticleOperatorInstance*[] m_Operators = 
	[
		&C_OP_SetPerChildControlPoint_0
	]
	CParticleOperatorInstance*[] m_Renderers = 
	[
		
	]
	CParticleOperatorInstance*[] m_Initializers = 
	[
		&C_INIT_RingWave_0
	]
	CParticleOperatorInstance*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperatorInstance*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperatorInstance*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_linear.vpcf"
			bool m_bPreventNameBasedLookup = false
			float m_flDelay = 0.000000
			bool m_bEndCap = false
			bool m_bDisableChild = false
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_linear.vpcf"
			bool m_bPreventNameBasedLookup = false
			float m_flDelay = 0.000000
			bool m_bEndCap = false
			bool m_bDisableChild = false
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_linear.vpcf"
			bool m_bPreventNameBasedLookup = false
			float m_flDelay = 0.000000
			bool m_bEndCap = false
			bool m_bDisableChild = false
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_linear.vpcf"
			bool m_bPreventNameBasedLookup = false
			float m_flDelay = 0.000000
			bool m_bEndCap = false
			bool m_bDisableChild = false
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_linear.vpcf"
			bool m_bPreventNameBasedLookup = false
			float m_flDelay = 0.000000
			bool m_bEndCap = false
			bool m_bDisableChild = false
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_core_linear.vpcf"
			bool m_bPreventNameBasedLookup = false
			float m_flDelay = 0.000000
			bool m_bEndCap = false
			bool m_bDisableChild = false
		}
	]
	bool m_bShouldSort = true
	bool m_bShouldBatch = false
}

C_OP_SetPerChildControlPoint C_OP_SetPerChildControlPoint_0
{
	int m_nChildGroupID = 0
	int m_nFirstControlPoint = 0
	int m_nNumControlPoints = 3
	int m_nFirstSourcePoint = 0
	bool m_bSetOrientation = false
	bool m_bNumBasedOnParticleCount = true
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_INIT_RingWave C_INIT_RingWave_0
{
	int m_nControlPointNumber = 0
	int m_nOverrideCP = -1
	int m_nOverrideCP2 = -1
	float m_flParticlesPerOrbit = 5.000000
	float m_flInitialRadius = 75.000000
	float m_flThickness = 0.000000
	float m_flInitialSpeedMin = 0.000000
	float m_flInitialSpeedMax = 0.000000
	float m_flRoll = 0.000000
	float m_flPitch = 0.000000
	float m_flYaw = 0.000000
	bool m_bEvenDistribution = true
	bool m_bXYVelocityOnly = true
	bool m_bRunForParentApplyKillList = true
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 5
	int m_nMinParticlesToEmit = -1
	float m_flStartTime = 0.000000
	float m_flStartTimeMax = -1.000000
	float m_flInitFromKilledParentParticles = 0.000000
	int m_nMaxEmittedPerFrame = -1
	int m_nScaleControlPoint = -1
	int m_nScaleControlPointField = 0
	int m_nSnapshotControlPoint = -1
	bool m_bDisableOperator = false
	float m_flOpStartFadeInTime = 0.000000
	float m_flOpEndFadeInTime = 0.000000
	float m_flOpStartFadeOutTime = 0.000000
	float m_flOpEndFadeOutTime = 0.000000
	float m_flOpFadeOscillatePeriod = 0.000000
	float m_flOpTimeOffsetMin = 0.000000
	float m_flOpTimeOffsetMax = 0.000000
	int m_nOpTimeOffsetSeed = 0
	int m_nOpStrengthScaleSeed = 0
	float m_flOpStrengthMinScale = 1.000000
	float m_flOpStrengthMaxScale = 1.000000
	int m_nOpTimeScaleSeed = 0
	float m_flOpTimeScaleMin = 1.000000
	float m_flOpTimeScaleMax = 1.000000
	int m_nOpEndCapState = -1
	int m_nOpScaleCP = -1
	string m_Notes = ""
}