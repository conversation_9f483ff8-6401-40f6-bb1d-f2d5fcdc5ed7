<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 4
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 1.650000
	bool m_bShouldSort = false
	float m_flMaxRecreationTime = -1.000000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_SetControlPointsToParticle_0,
		&C_OP_CPOffsetToPercentageBetweenCPs_0,
		&C_OP_RampScalarLinear_0,
		&C_OP_SpinUpdate_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomSecondSequence_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_VelocityFromCP_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_explosion_linear.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_trail_linear.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_trail_c_linear.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/queen_shadow_strike_glow_linear.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	string m_hTexture = "materials\\models\\heroes\\queenofpain\\queenofpain_weapon.vtex"
	string m_Notes = ""
	float m_flAnimationRate = 0.750000
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
	int m_nOpEndCapState = 1
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flEndScale = 2.000000
	string m_Notes = ""
	float m_flStartScale = 0.500000
}

C_OP_SetControlPointsToParticle C_OP_SetControlPointsToParticle_0
{
	string m_Notes = ""
	int m_nFirstControlPoint = 3
	bool m_bSetOrientation = true
}

C_OP_CPOffsetToPercentageBetweenCPs C_OP_CPOffsetToPercentageBetweenCPs_0
{
	string m_Notes = ""
	float m_flInputMin = 1.000000
	float m_flInputMax = 0.000000
	int m_nOffsetCP = 1
	float(3) m_vecOffset = ( 0.000000, 0.000000, 0.150000 )
	float m_flInputBias = 0.125000
	bool m_bScaleOffset = true
}

C_OP_RampScalarLinear C_OP_RampScalarLinear_0
{
	string m_Notes = ""
	int m_nField = 12
	float m_RateMin = 30.000000
	float m_RateMax = 30.000000
}

C_OP_SpinUpdate C_OP_SpinUpdate_0
{
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	float m_fRadiusMax = 85.000000
	float(3) m_vecDistanceBias = ( 0.050000, 1.000000, 0.000000 )
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMax = 0.100000
	float m_fLifetimeMin = 0.100000
}

C_INIT_RandomSecondSequence C_INIT_RandomSecondSequence_0
{
	string m_Notes = ""
	int m_nSequenceMin = 1
	int m_nSequenceMax = 1
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
	bool m_bDisableOperator = true
	float m_flPercent = 1.000000
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
	float m_flDegrees = 90.000000
	float m_flDegreesMax = 0.000000
}

C_INIT_VelocityFromCP C_INIT_VelocityFromCP_0
{
	int m_nControlPoint = 1
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 1
}