# animations.lua ChangeLog

### Version 1.00
- Updated version number to reflect Barebones 1.0 release

### Version 0.84
- Added FreezeAnimation function to allow for animations to be paused at any time
- Added UnfreezeAnimation function to allow animations to be unpaused at any time

### Version 0.83
- Fixed an issue where perfectly sequential animations would not play
- Added several missing translate activity modifiers
- Added AddAnimationTranslate and RemoveAnimationTranslate commands to allow for easily adding/removing permanent translates like "injured"/"haste", etc

### Version 0.80
- Added animations.lua library