<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 32
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 25.000000
	int(4) m_ConstantColor = ( 255, 255, 255, 55 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderDeferredLight_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_PositionLock_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomColor_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderDeferredLight C_OP_RenderDeferredLight_0
{
	float m_flStartFalloff = 0.100000
	float m_flAlphaScale = 5.000000
	float m_flRadiusScale = 3.000000
	string m_Notes = ""
	string m_hTexture = "materials\\particle\\particle_glow_01.vtex"
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	int m_nOpEndCapState = 1
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 3.000000
}

C_OP_PositionLock C_OP_PositionLock_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 3
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.400000
	float m_fLifetimeMax = 0.600000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 3
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMax = 125.000000
	float m_flRadiusMin = 100.000000
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 234, 89, 106, 255 )
	int(4) m_ColorMax = ( 162, 9, 9, 255 )
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	int m_nParticlesToEmit = 1
	string m_Notes = ""
}