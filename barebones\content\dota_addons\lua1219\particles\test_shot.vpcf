<!-- kv3 encoding:text:version{e21c7f3c-8a33-41c5-9977-a76d3a32aa0d} format:generic:version{7412167c-06e9-4698-aff2-e63eb59037e7} -->
{
	_class = "CParticleSystemDefinition"
	m_bShouldHitboxesFallbackToRenderBounds = false
	m_nMaxParticles = 16
	m_flConstantRadius = 25.000000
	m_bShouldSort = false
	m_Renderers =
	[
		{
			_class = "C_OP_RenderSprites"
			m_nOrientationType = 2
		},
	]
	m_Operators =
	[
		{
			_class = "C_OP_BasicMovement"
		},
		{
			_class = "C_OP_SetControlPointsToParticle"
			m_nFirstControlPoint = 3
			m_bSetOrientation = true
		},
		{
			_class = "C_OP_MovementPlaceOnGround"
			m_nLerpCP = 3
			m_flTolerance = 96.000000
			m_flOffset = 120.000000
			m_bIncludeWater = true
			m_flMaxTraceLength = 1024.000000
			m_flTraceOffset = 256.000000
			m_CollisionGroupName = "DEBRIS"
			m_nRefCP1 = 3
			m_bDisableOperator = true
		},
		{
			_class = "C_OP_LagCompensation"
			m_nLatencyCP = 10
			m_nDesiredVelocityCP = 1
			m_bDisableOperator = true
		},
		{
			_class = "C_OP_Decay"
		},
		{
			_class = "C_OP_RemapCPtoVelocity"
			m_nCPInput = 1
		},
	]
	m_Initializers =
	[
		{
			_class = "C_INIT_CreateWithinSphere"
		},
		{
			_class = "C_INIT_RandomLifeTime"
			m_fLifetimeMax = 3.000000
			m_fLifetimeMin = 3.000000
		},
		{
			_class = "C_INIT_NormalAlignToCP"
		},
		{
			_class = "C_INIT_VelocityFromCP"
			m_nControlPoint = 1
		},
		{
			_class = "C_INIT_PositionOffset"
			m_OffsetMin =
			[
				0.000000,
				0.000000,
				80.000000,
			]
			m_OffsetMax =
			[
				0.000000,
				0.000000,
				80.000000,
			]
		},
		{
			_class = "C_INIT_RemapCPtoVector"
			m_nCPInput = 4
			m_nFieldOutput = 6
			m_vInputMax =
			[
				255.000000,
				255.000000,
				255.000000,
			]
			m_vOutputMax =
			[
				1.000000,
				1.000000,
				1.000000,
			]
		},
	]
	m_Emitters =
	[
		{
			_class = "C_OP_InstantaneousEmitter"
			m_nParticlesToEmit = 1
		},
	]
}