// No spaces in event names, max length 32
// All strings are case sensitive
//
// valid data key types are:
//   string : a zero terminated string
//   bool   : unsigned int, 1 bit
//   byte   : unsigned int, 8 bit
//   short  : signed int, 16 bit
//   long   : signed int, 32 bit
//   float  : float, 32 bit
//   uint64 : unsigned int 64 bit
//   local  : any data, but not networked to clients
//
// following key names are reserved:
//   local      : if set to 1, event is not networked to clients
//   unreliable : networked, but unreliable
//   suppress   : never fire this event
//   time	: firing server time
//   eventid	: holds the event ID

"CustomEvents"
{
	"CustomMousePressed"
	{
		"local"                     "1"
	}
	"CustomShowErrMsgCtrl"
	{
		"msg"                       "string"
		"local"                     "1"
	}
	"DraggingCard"
	{
		"local"                     "1"
	}
	"DraggingCard2Delete"
	{
		"local"                     "1"
		"flag"                      "bool"
	}
	"DraggingCard_Assist"
	{
		"local"                     "1"
	}
	"DraggingCard_Blink"
	{
		"local"                     "1"
	}
	"DraggingCardEnd"
	{
		"local"                     "1"
		"flag"                      "bool"
	}
	"DraggingCardEnd_Assist"
	{
		"local"                     "1"
		"flag"                      "bool"
	}
	"DraggingCardEnd_Blink"
	{
		"local"                     "1"
		"flag"                      "bool"
	}
	"CustomToggleInventoryWindow"
	{
		"local"                     "1"
		"flag"                      "short"
	}
	"PlayerOpenShop"
	{
		"local"                     "1"
		"flag"                      "short"
	}
	"QueryEquipCombins"
	{
		"local"                     "1"
		"item"                      "string"
	}
	"CustomSPELLABILITY_START"
	{
		"local"                     "1"
		"slot"                      "short"
	}
	"CustomSPELLABILITY_END"
	{
		"local"                     "1"
	}
	"CustomSPELLABILITY_START_ASSIST"
	{
		"local"                     "1"
		"slot"                      "short"
	}
	"CustomSPELLABILITY_END_ASSIST"
	{
		"local"                     "1"
	}
	"CustomSPELLABILITY_START_BLINK"
	{
		"local"                     "1"
		"slot"                      "short"
	}
	"CustomSPELLABILITY_END_BLINK"
	{
		"local"                     "1"
	}
	// 快捷键释放天赋技能
	"CustomSPELLABILITY_Talent"
	{
		"local"                     "1"
		"slot"                      "short"
	}
	// 快捷键释放天赋技能-开始
	"CustomSPELLABILITY_START_Talent"
	{
		"local"                     "1"
		"slot"                      "short"
	}
	// 快捷键释放天赋技能-结束
	"CustomSPELLABILITY_END_Talent"
	{
		"local"                     "1"
	}
	// 选中信使
	"SetCourierToCenter"
	{
		"local"                     "1"
	}
	// 选中英雄
	"SetSelectHero"
	{
		"local"                     "1"
	}
	// TAB查看/关闭详细信息
	"QueryPlayerDetail"
	{
		"local"                     "1"
	}
	"Openljcz"
	{
		"local"                     "1"
		"flag"                      "short"
	}
	"Open_mall_main"
	{
		"local"                     "1"
		"flag"                      "string"
	}
	"Open_mall_recharge"
	{
		"local"                     "1"
		"good"                      "string"
	}
	"Openplus"
	{
		"local"                     "1"
		"flag"                      "short"
	}
	"Open_mall_main_bp"
	{
		"local"                     "1"
		"flag"                      "short"
	}
	"OnChannelBtnPressed"
	{
		"local"                     "1"
		"mode"                      "string"
	}
	"Open_mall_tanchuang"
	{
		"local"                     "1"
		"flag"                      "string"
	}
	"Open_mall_main_code"
	{
		"local"                     "1"
		"flag"                      "short"
	}
	"Mall_gr_others"
	{
		"local"                     "1"
		"good"                      "string"
	}
	"Mall_gr_hero"
	{
		"local"                     "1"
		"good"                      "string"
	}
	"Open_Exchange_code"
	{
		"local"                     "1"
		"flag"                      "short"
	}
	"CustomRechargeQCCtrl"	
	{
		"local"                     "1"
	}
	"GameEndPanelClose"	
	{
		"local"                     "1"
	}
	"CustomBuyItemPopCtrl"	
	{
		"local"                     "1"
		"category"                      "short"    //0:props表中物品
		"id"                        "string"
		"price"                     "short"
		"action"                    "string"
	}
}
