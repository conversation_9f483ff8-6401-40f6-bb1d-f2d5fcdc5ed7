<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 16
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 1.000000
	bool m_bShouldSort = false
	float m_flMaxRecreationTime = -1.000000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderModels_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_SetControlPointsToParticle_0,
		&C_OP_RampScalarLinear_0,
		&C_OP_FadeInSimple_0,
		&C_OP_MovementPlaceOnGround_0,
		&C_OP_RemapVelocityToVector_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomColor_0,
		&C_INIT_NormalAlignToCP_0,
		&C_INIT_VelocityFromCP_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/drow_base_attack_launch.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/drow_base_attack_trail_c.vpcf"
		},
		ParticleChildrenInfo_t
		{
			bool m_bEndCap = true
			string m_ChildRef = "particles/frostivus_gameplay/drow_base_attack_explosion_flash.vpcf"
		}
	]
}

C_OP_RenderModels C_OP_RenderModels_0
{
	string m_hOverrideMaterial = ""
	string m_EconSlotName = ""
	string m_Notes = ""
	string m_ActivityName = ""
	ModelReference_t[] m_ModelList = 
	[
		ModelReference_t
		{
			string m_model = "models/projectiles/drow_arrow.vmdl"
		}
	]
	bool m_bAnimated = true
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
	int m_nOpEndCapState = 1
}

C_OP_SetControlPointsToParticle C_OP_SetControlPointsToParticle_0
{
	string m_Notes = ""
	int m_nFirstControlPoint = 3
	bool m_bSetOrientation = true
}

C_OP_RampScalarLinear C_OP_RampScalarLinear_0
{
	string m_Notes = ""
	int m_nField = 4
	float m_RateMin = 1.000000
	float m_RateMax = 2.000000
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 1.000000
}

C_OP_MovementPlaceOnGround C_OP_MovementPlaceOnGround_0
{
	string m_Notes = ""
	int m_nLerpCP = 3
	float m_flTolerance = 96.000000
	float m_flOffset = 70.000000
	bool m_bIncludeWater = true
	float m_flMaxTraceLength = 1024.000000
	float m_flTraceOffset = 256.000000
	string m_CollisionGroupName = "DEBRIS"
	int m_nRefCP1 = 3
}

C_OP_RemapVelocityToVector C_OP_RemapVelocityToVector_0
{
	string m_Notes = ""
	bool m_bNormalize = true
	int m_nFieldOutput = 21
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMax = 0.200000
	float m_fLifetimeMin = 0.200000
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMax = ( 213, 240, 246, 255 )
	int(4) m_ColorMin = ( 213, 240, 246, 255 )
}

C_INIT_NormalAlignToCP C_INIT_NormalAlignToCP_0
{
	string m_Notes = ""
}

C_INIT_VelocityFromCP C_INIT_VelocityFromCP_0
{
	string m_Notes = ""
	int m_nControlPoint = 1
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 1
}