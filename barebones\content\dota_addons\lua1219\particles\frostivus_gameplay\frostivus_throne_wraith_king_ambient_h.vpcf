<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 8
	string m_pszSnapshotName = ""
	float(3) m_BoundingBoxMax = ( 64.000000, 64.000000, 64.000000 )
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 15.000000
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderDeferredLight_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_Noise_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_RampScalarLinearSimple_2,
		&C_OP_LockToBone_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomColor_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_CreateOnModel_0,
		&C_INIT_PositionOffset_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderDeferredLight C_OP_RenderDeferredLight_0
{
	bool m_bDisableOperator = true
	string m_Notes = ""
	float m_flRadiusScale = 3.000000
	float m_flAlphaScale = 5.000000
	float m_flStartFalloff = 0.300000
	int(4) m_ColorScale = ( 0, 158, 93, 255 )
	string m_hTexture = "materials\\particle\\softglow.vtex"
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.500000
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.500000
}

C_OP_Noise C_OP_Noise_0
{
	string m_Notes = ""
	bool m_bAdditive = true
	float m_flOutputMax = 13.000000
	int m_nFieldOutput = 4
	float m_fl4NoiseScale = 0.001310
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flStartScale = 0.250000
	float m_flBias = 0.750000
	float m_flEndScale = 3.000000
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	string m_Notes = ""
	float m_flEndTime = 9999.000000
	float m_Rate = 150.000000
	int m_nOpEndCapState = 1
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_2
{
	string m_Notes = ""
	float m_flEndTime = 99999.000000
	float m_Rate = -8.000000
	int m_nField = 16
	int m_nOpEndCapState = 1
}

C_OP_LockToBone C_OP_LockToBone_0
{
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 1.250000
	float m_fLifetimeMax = 1.500000
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 0, 191, 121, 255 )
	int(4) m_ColorMax = ( 0, 158, 93, 255 )
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_CreateOnModel C_INIT_CreateOnModel_0
{
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMin = ( 64.000000, 0.000000, 50.000000 )
	float(3) m_OffsetMax = ( 64.000000, 0.000000, 50.000000 )
	bool m_bLocalCoords = true
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 5.000000
}