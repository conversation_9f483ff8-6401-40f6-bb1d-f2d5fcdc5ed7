<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 512
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flNoDrawTimeToGoToSleep = 12.000000
	bool m_bShouldSort = false
	CParticleOperator*[] m_Renderers = 
	[
		
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_Decay_0,
		&C_OP_OscillateVector_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomColor_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_RingWave_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		&C_OP_PlanarConstraint_0
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/maiden_holdout_arcane_buff_snow_c.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_herofx/maiden_holdout_arcane_buff_caster.vpcf"
		}
	]
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float m_fDrag = 0.001000
	float(3) m_Gravity = ( 0.000000, 0.000000, -100.000000 )
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.500000
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 0.500000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_OscillateVector C_OP_OscillateVector_0
{
	float(3) m_RateMin = ( -2.000000, -2.000000, -1.000000 )
	float(3) m_RateMax = ( 2.000000, 2.000000, 1.000000 )
	float(3) m_FrequencyMax = ( 3.000000, 3.000000, 3.000000 )
	float m_flEndTime_min = 0.700000
	float m_flEndTime_max = 0.700000
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMax = ( 117, 175, 241, 255 )
	int(4) m_ColorMin = ( 155, 214, 245, 255 )
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 2.000000
	float m_fLifetimeMin = 1.000000
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 3.000000
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	float(3) m_OffsetMax = ( 24.000000, 24.000000, 824.000000 )
	float(3) m_OffsetMin = ( -24.000000, -24.000000, 634.000000 )
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 63
	string m_Notes = ""
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	float m_flNoiseScaleLoc = 0.100000
	float(3) m_vecOutputMin = ( -64.000000, -64.000000, -64.000000 )
	float(3) m_vecOutputMax = ( 64.000000, 64.000000, 64.000000 )
	string m_Notes = ""
}

C_INIT_RingWave C_INIT_RingWave_0
{
	float m_flInitialRadius = 0.600000
	float m_flThickness = 0.600000
	float m_flInitialSpeedMax = 0.200000
	int m_nOverrideCP = 1
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 400.000000
	string m_Notes = ""
}

C_OP_PlanarConstraint C_OP_PlanarConstraint_0
{
	string m_Notes = ""
}