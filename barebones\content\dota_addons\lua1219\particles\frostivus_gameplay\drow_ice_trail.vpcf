<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 256
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int m_nConstantSequenceNumber = 2
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_OscillateVector_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_RandomColor_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_InheritVelocity_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_RandomColor_2
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/drow_ice_trail_b.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/drow_ice_trail_c.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	float m_flMinSize = 0.000750
	float m_flMaxSize = 0.002500
	string m_hTexture = "materials\\particle\\impact\\fleks3.vtex"
	string m_Notes = ""
	float m_flAnimationRate = 1.000000
	float m_flAnimationRate2 = 0.100000
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float m_fDrag = 0.050000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 0.500000
	float m_flBias = 0.250000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.100000
}

C_OP_OscillateVector C_OP_OscillateVector_0
{
	string m_Notes = ""
	float(3) m_RateMin = ( -4.000000, -4.000000, -4.000000 )
	float(3) m_RateMax = ( 4.000000, 4.000000, 4.000000 )
	float(3) m_FrequencyMin = ( 0.500000, 0.500000, 0.500000 )
	float(3) m_FrequencyMax = ( 3.000000, 3.000000, 3.000000 )
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.300000
	float m_fLifetimeMax = 1.000000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMin = 0.200000
	float m_flRadiusMax = 0.200000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 3
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	bool m_bProportional = true
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 1.000000 )
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 1.000000 )
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMax = ( 74, 189, 255, 255 )
	int(4) m_ColorMin = ( 12, 109, 255, 255 )
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	string m_Notes = ""
	float m_flNoiseScaleLoc = 5.000000
	float(3) m_vecOutputMin = ( -132.000000, -132.000000, -132.000000 )
	float(3) m_vecOutputMax = ( 132.000000, 132.000000, 132.000000 )
	float m_flNoiseScale = 3.000000
}

C_INIT_InheritVelocity C_INIT_InheritVelocity_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 3
	float m_flVelocityScale = 0.100000
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	string m_Notes = ""
	int m_nSequenceMax = 63
}

C_INIT_RandomColor C_INIT_RandomColor_2
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 50, 84, 183, 255 )
	int(4) m_ColorMax = ( 150, 236, 255, 255 )
	int m_nFieldOutput = 22
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 300.000000
}