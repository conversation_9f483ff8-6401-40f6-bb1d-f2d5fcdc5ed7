"Attachments"
{
	"models/heroes/juggernaut/juggernaut.vmdl"
	{
		"attach_hitloc"
		{
			"models/items/furion/hat_holiday_1.vmdl"
			{
				"scale"		"1"
				"pitch"		"20"
				"roll"		"105"
				"ZPos"		"-159"
				"YPos"		"-10"
				"yaw"		"-35"
				"XPos"		"19"
			}
		}
	}
	"models/heroes/mirana/mirana.vmdl"
	{
		"attach_hitloc"
		{
			"models/items/furion/hat_holiday_1.vmdl"
			{
				"scale"		"0.89999997615814"
				"pitch"		"-105"
				"roll"		"-35"
				"ZPos"		"-146"
				"YPos"		"-6"
				"yaw"		"0"
				"XPos"		"14"
			}
		}
	}
	"models/heroes/antimage/antimage.vmdl"
	{
		"attach_hitloc"
		{
			"models/props_gameplay/red_box.vmdl"
			{
				"scale"		"1.1000000238419"
				"pitch"		"-70"
				"roll"		"0"
				"yaw"		"-135"
				"YPos"		"-17"
				"ZPos"		"0"
				"XPos"		"-13"
			}
			"models/particle/snowball.vmdl"
			{
				"scale"		"0.30000001192093"
				"pitch"		"-113"
				"roll"		"131"
				"yaw"		"45"
				"YPos"		"-27"
				"ZPos"		"112"
				"XPos"		"-65"
			}
		}
		"attach_h2"
		{
			"models/particle/snowball.vmdl"
			{
				"scale"		"0.30000001192093"
				"pitch"		"105"
				"roll"		"-90"
				"yaw"		"-90"
				"YPos"		"-50"
				"ZPos"		"-19"
				"XPos"		"-80"
			}
		}
		"attach_h1"
		{
			"models/particle/snowball.vmdl"
			{
				"scale"		"0.30000001192093"
				"pitch"		"-113"
				"roll"		"0"
				"yaw"		"0"
				"YPos"		"97"
				"ZPos"		"-19"
				"XPos"		"60"
			}
		}
	}
	"models/heroes/dragon_knight/dragon_knight.vmdl"
	{
		"attach_attack1"
		{
			"models/items/faceless_void/battlefury/battlefury.vmdl"
			{
				"scale"		"1.7999999523163"
				"pitch"		"180"
				"roll"		"10"
				"yaw"		"190"
				"YPos"		"120"
				"ZPos"		"-90"
				"XPos"		"0"
			}
		}
	}
	"models/heroes/axe/axe.vmdl"
	{
		"attach_weapon"
		{
			"models/items/wraith_king/winterblight_weapon/winterblight_weapon.vmdl"
			{
				"scale"		"2.2999999523163"
				"YPos"		"0"
				"pitch"		"-140"
				"yaw"		"180"
				"roll"		"30"
				"ZPos"		"0"
				"XPos"		"70"
			}
			"models/items/faceless_void/battlefury/battlefury.vmdl"
			{
				"scale"		"2"
				"pitch"		"-50"
				"roll"		"28"
				"yaw"		"0"
				"YPos"		"111"
				"ZPos"		"-63"
				"XPos"		"0"
			}
		}
		"attach_hitloc"
		{
			"models/props_gameplay/red_box.vmdl"
			{
				"scale"		"1.2999999523163"
				"pitch"		"-70"
				"roll"		"0"
				"ZPos"		"0"
				"YPos"		"0"
				"yaw"		"-135"
				"XPos"		"-13"
			}
			"models/heroes/tiny_04/tiny04_deathsim.vmdl"
			{
				"Animation"		"tiny04_deathsim_anim"
				"scale"		"1"
				"pitch"		"0"
				"roll"		"0"
				"yaw"		"0"
				"YPos"		"0"
				"ZPos"		"0"
				"XPos"		"0"
			}
		}
		"attach_origin"
		{
			"models/props_gameplay/red_box.vmdl"
			{
				"scale"		"2"
				"pitch"		"0"
				"roll"		"0"
				"ZPos"		"0"
				"YPos"		"0"
				"yaw"		"0"
				"XPos"		"0"
			}
		}
	}
	"models/heroes/sniper/sniper.vmdl"
	{
		"attach_hitloc"
		{
			"models/items/furion/hat_holiday_1.vmdl"
			{
				"scale"		"0.89999997615814"
				"pitch"		"-165"
				"roll"		"169"
				"ZPos"		"-159"
				"YPos"		"5"
				"yaw"		"186"
				"XPos"		"39"
			}
		}
	}
	"models/heroes/windrunner/windrunner.vmdl"
	{
		"attach_hitloc"
		{
			"models/items/furion/hat_holiday_1.vmdl"
			{
				"scale"		"0.89999997615814"
				"pitch"		"-111"
				"roll"		"-6"
				"ZPos"		"-144"
				"YPos"		"-5"
				"yaw"		"-19"
				"XPos"		"9"
			}
		}
	}
	"models/heroes/shadow_fiend/shadow_fiend_arcana.vmdl"
	{
		"attach_hitloc"
		{
			"models/items/furion/hat_holiday_1.vmdl"
			{
				"scale"		"0.89999997615814"
				"pitch"		"-165"
				"roll"		"169"
				"ZPos"		"-99"
				"YPos"		"5"
				"yaw"		"186"
				"XPos"		"39"
			}
		}
		"attach_head"
		{
			"models/items/furion/hat_holiday_1.vmdl"
			{
				"scale"		"1.2000000476837"
				"pitch"		"-95"
				"roll"		"-90"
				"ZPos"		"-174"
				"YPos"		"0"
				"yaw"		"0"
				"XPos"		"14"
			}
		}
	}
	"Particles"
	{
		"models/items/wraith_king/winterblight_weapon/winterblight_weapon.vmdl"
		{
			"particles/econ/items/wraith_king/wraith_king_winterblight_weapon/wraith_king_winterblight_ambient.vpcf"
			{
				"2"		"attach_mouth"
				"1"		"attach_eye_l"
				"0"		"attach_eye_r"
			}
		}
	}
	"ExtraKeys"		"Can Be Added Wherever"
}
