"DOTAAbilities"
{
	//=================================================================================================================
	// Chaos Knight: Reality Rift - ability_lua example
	//=================================================================================================================
	"chaos_knight_reality_rift_barebones"
	{
		"BaseClass"						"ability_lua"
		"ScriptFile"                	"ability_example/chaos_knight_reality_rift.lua"
		"AbilityTextureName"			"chaos_knight_reality_rift"
		"AbilityBehavior"				"DOTA_ABILITY_BEHAVIOR_UNIT_TARGET | DOTA_ABILITY_BEHAVIOR_ROOT_DISABLES"
		"AbilityUnitTargetTeam"			"DOTA_UNIT_TARGET_TEAM_ENEMY"
		"AbilityUnitTargetType"			"DOTA_UNIT_TARGET_HERO | DOTA_UNIT_TARGET_BASIC"
		"SpellImmunityType"				"SPELL_IMMUNITY_ENEMIES_NO"
		"SpellDispellableType"			"SPELL_DISPELLABLE_YES"
		"FightRecapLevel"				"1"
		"AbilitySound"					"Hero_ChaosKnight.RealityRift"

		// Casting
		//-------------------------------------------------------------------------------------------------------------
		"AbilityCastRange"				"550 600 650 700"
		"AbilityCastPoint"				"0.3"

		// Time		
		//-------------------------------------------------------------------------------------------------------------
		"AbilityCooldown"				"18.0 14.0 10.0 6.0"

		// Cost
		//-------------------------------------------------------------------------------------------------------------
		"AbilityManaCost"				"50"
		
		// Special
		//-------------------------------------------------------------------------------------------------------------
		"AbilitySpecial"
		{
			"01"
			{
				"var_type"					"FIELD_INTEGER"
				"armor_reduction"			"-1 -3 -5 -7"
				"LinkedSpecialBonus"		"special_bonus_unique_chaos_knight_2"
			}

			"02"
			{
				"var_type"					"FIELD_FLOAT"
				"reduction_duration"		"8.0"
			}

			"03"
			{
				"var_type"					"FIELD_INTEGER"
				"illusion_search_radius"	"1375"
			}

			"04" // 30% of the distance between
			{
				"var_type"					"FIELD_FLOAT"
				"min_range"					"0.3"
			}

			"05" // 80% of the distance between
			{
				"var_type"					"FIELD_FLOAT"
				"max_range"					"0.8"
			}
		}
	}
}
