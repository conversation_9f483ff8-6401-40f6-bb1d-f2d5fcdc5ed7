<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 150
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderTrails_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_FadeOut_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_VectorNoise_0,
		&C_OP_DistanceToCP_0,
		&C_OP_BasicMovement_0,
		&C_OP_FadeInSimple_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomAlpha_0,
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_CreateFromParentParticles_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomColor_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		&C_OP_AttractToControlPoint_0
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderTrails C_OP_RenderTrails_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\particle_flares\\aircraft_white.vtex"
	string m_Notes = ""
}

C_OP_FadeOut C_OP_FadeOut_0
{
	string m_Notes = ""
	float m_flFadeOutTimeMax = 1.000000
	float m_flFadeOutTimeMin = 0.400000
	float m_flFadeBias = 0.800000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 0.500000
	float m_flStartScale = 2.000000
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	string m_Notes = ""
	bool m_bAdditive = true
	float(3) m_vecOutputMax = ( 40.000000, 40.000000, 40.000000 )
	float(3) m_vecOutputMin = ( -40.000000, -40.000000, -40.000000 )
	int m_nFieldOutput = 0
	float m_fl4NoiseScale = 0.800000
}

C_OP_DistanceToCP C_OP_DistanceToCP_0
{
	string m_Notes = ""
	int m_nStartCP = 10
	float m_flInputMin = 150.000000
	float m_flInputMax = 1600.000000
	int m_nFieldOutput = 10
	float m_flOutputMin = 0.250000
	float m_flOutputMax = 0.050000
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float m_fDrag = 0.100000
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.500000
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.500000
	float m_fLifetimeMax = 0.600000
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_CreateFromParentParticles C_INIT_CreateFromParentParticles_0
{
	string m_Notes = ""
	float m_flVelocityScale = 1.000000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMax = 25.000000
	float m_flRadiusMin = 16.000000
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 255, 228, 0, 255 )
	int(4) m_ColorMax = ( 252, 255, 0, 255 )
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	bool m_bInitFromKilledParentParticles = true
	float m_flEmitRate = 2000.000000
}

C_OP_AttractToControlPoint C_OP_AttractToControlPoint_0
{
	string m_Notes = ""
	float m_fFalloffPower = 0.100000
	float m_fForceAmount = 8000.000000
	int m_nControlPointNumber = 10
}