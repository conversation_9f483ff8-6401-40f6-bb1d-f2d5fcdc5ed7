<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 32
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 48.000000
	int(4) m_ConstantColor = ( 91, 241, 158, 15 )
	float m_flMaxRecreationTime = -1.000000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderRopes_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_PositionLock_0,
		&C_OP_FadeInSimple_0,
		&C_OP_RemapParticleCountOnScalarEndCap_0,
		&C_OP_RemapParticleCountOnScalarEndCap_2
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderRopes C_OP_RenderRopes_0
{
	float m_flSelfIllumAmount = 1.000000
	string m_hTexture = "materials\\particle\\beam_plasma_06.vtex"
	string m_Notes = ""
	float m_flTextureVScrollRate = -0.170000
	float m_flTextureVWorldSize = 250.000015
	int m_nMaxTesselation = 3
	int m_nMinTesselation = 3
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flEndScale = 0.000000
	string m_Notes = ""
	float m_flBias = 0.750000
}

C_OP_PositionLock C_OP_PositionLock_0
{
	float m_flStartTime_max = -10.000000
	float m_flStartTime_min = -10.000000
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
}

C_OP_RemapParticleCountOnScalarEndCap C_OP_RemapParticleCountOnScalarEndCap_0
{
	string m_Notes = ""
	bool m_bBackwards = true
	int m_nInputMax = 10
	int m_nFieldOutput = 16
}

C_OP_RemapParticleCountOnScalarEndCap C_OP_RemapParticleCountOnScalarEndCap_2
{
	string m_Notes = ""
	bool m_bBackwards = true
	int m_nInputMax = 25
	bool m_bScaleCurrent = true
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 32.000000
	string m_Notes = ""
}