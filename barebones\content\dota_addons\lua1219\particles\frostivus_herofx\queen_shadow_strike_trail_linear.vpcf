<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 32
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 255, 107, 107, 255 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderRopes_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_PositionLock_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_SetControlPointPositions_0,
		&C_OP_DistanceToCP_0,
		&C_OP_VectorNoise_0,
		&C_OP_ColorInterpolate_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_InheritVelocity_0,
		&C_INIT_RandomAlpha_0,
		&C_INIT_RemapScalar_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderRopes C_OP_RenderRopes_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\beam_generic_2.vtex"
	string m_Notes = ""
	float m_flTextureVWorldSize = 999.999939
	int m_nMaxTesselation = 3
	int m_nMinTesselation = 3
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float m_fDrag = 0.250000
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flEndScale = 0.000000
	int m_nOpEndCapState = 0
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	float m_flEndTime_max = 0.100000
	int m_nControlPointNumber = 5
	float m_flEndTime_min = 0.100000
	float m_flStartTime_max = 0.050000
	float m_flStartTime_min = 0.050000
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.900000
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 0.100000
	string m_Notes = ""
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	int m_nOpEndCapState = 1
	float m_Rate = -12.000000
	float m_flEndTime = 99999.000000
	string m_Notes = ""
}

C_OP_SetControlPointPositions C_OP_SetControlPointPositions_0
{
	int m_nCP1 = 5
	float(3) m_vecCP1Pos = ( 0.000000, 0.000000, 0.000000 )
	int m_nCP2 = 6
	float(3) m_vecCP2Pos = ( 0.000000, 0.000000, 0.000000 )
	int m_nCP3 = 6
	float(3) m_vecCP3Pos = ( 0.000000, 0.000000, 0.000000 )
	int m_nCP4 = 6
	float(3) m_vecCP4Pos = ( 0.000000, 0.000000, 0.000000 )
	int m_nHeadLocation = 3
	string m_Notes = ""
}

C_OP_DistanceToCP C_OP_DistanceToCP_0
{
	int m_nStartCP = 5
	int m_nFieldOutput = 16
	float m_flInputMax = 48.000000
	float m_flInputMin = 12.000000
	int m_nOpEndCapState = 1
	string m_Notes = ""
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	bool m_bAdditive = true
	float(3) m_vecOutputMax = ( 2.000000, 2.000000, 2.000000 )
	float(3) m_vecOutputMin = ( -2.000000, -2.000000, -2.000000 )
	int m_nFieldOutput = 0
	float m_fl4NoiseScale = 0.900000
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	int(4) m_ColorFade = ( 129, 0, 0, 255 )
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMin = 0.750000
	float m_fLifetimeMax = 0.750000
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMin = 8.000000
	float m_flRadiusMax = 8.000000
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	float m_fRadiusMax = 10.000000
	float m_fRadiusMin = 10.000000
	int m_nControlPointNumber = 5
	string m_Notes = ""
}

C_INIT_InheritVelocity C_INIT_InheritVelocity_0
{
	int m_nControlPointNumber = 5
	float m_flVelocityScale = 0.010000
	string m_Notes = ""
}

C_INIT_RandomAlpha C_INIT_RandomAlpha_0
{
	string m_Notes = ""
}

C_INIT_RemapScalar C_INIT_RemapScalar_0
{
	bool m_bScaleInitialRange = true
	float m_flInputMax = 0.100000
	float m_flEndTime = 0.100000
	float m_flStartTime = 0.000000
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 32.000000
	string m_Notes = ""
}