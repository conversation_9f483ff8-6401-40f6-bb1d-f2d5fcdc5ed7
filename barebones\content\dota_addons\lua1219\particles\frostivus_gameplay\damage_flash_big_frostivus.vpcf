<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 8
	string m_pszSnapshotName = ""
	float(3) m_BoundingBoxMin = ( -50.000000, -10.000000, -10.000000 )
	float(3) m_BoundingBoxMax = ( 50.000000, 300.000000, 1000.000000 )
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 254, 255, 143, 255 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_PositionLock_0,
		&C_OP_Decay_0,
		&C_OP_FadeInSimple_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_ColorInterpolate_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_RandomColor_0,
		&C_INIT_PositionOffset_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/damage_flash_b_frostivus.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/damage_flash_c_frostivus.vpcf"
		},
		ParticleChildrenInfo_t
		{
			string m_ChildRef = "particles/frostivus_gameplay/damage_flash_d_frostivus.vpcf"
		}
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	float m_flStartFadeSize = 0.475000
	float m_flEndFadeSize = 0.550000
	float m_flMaxSize = 0.650000
	string m_hTexture = "materials\\particle\\water_splash\\water_splash.vtex"
	string m_Notes = ""
	float m_flAnimationRate = 3.000000
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float m_fDrag = 0.050000
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flStartScale = 3.000000
	float m_flEndScale = 0.250000
	float m_flBias = 0.240000
}

C_OP_PositionLock C_OP_PositionLock_0
{
	string m_Notes = ""
	float m_flEndTime_max = 0.200000
	float m_flEndTime_min = 0.100000
	float m_flStartTime_max = 0.000000
	float m_flStartTime_min = 0.000000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.750000
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	string m_Notes = ""
	int(4) m_ColorFade = ( 176, 178, 126, 255 )
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.175000
	float m_fLifetimeMax = 0.350000
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMin = 20.000000
	float m_flRadiusMax = 22.000000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	float m_fRadiusMax = 12.000000
	float m_fSpeedMax = 350.000000
	float(3) m_LocalCoordinateSystemSpeedMin = ( 20.000000, 0.000000, 0.000000 )
	float(3) m_LocalCoordinateSystemSpeedMax = ( 130.000000, 0.000000, 0.000000 )
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	string m_Notes = ""
	float(3) m_vecOutputMax = ( 34.000000, 34.000000, 34.000000 )
	float(3) m_vecOutputMin = ( -34.000000, -34.000000, -34.000000 )
	float m_flNoiseScaleLoc = 4.000000
	float m_flNoiseScale = 3.000000
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	string m_Notes = ""
	int m_nSequenceMax = 8
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	string m_Notes = ""
	int(4) m_ColorMin = ( 229, 206, 50, 255 )
	int(4) m_ColorMax = ( 205, 178, 0, 255 )
	int m_nFieldOutput = 22
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 100.000000 )
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 100.000000 )
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 8
}