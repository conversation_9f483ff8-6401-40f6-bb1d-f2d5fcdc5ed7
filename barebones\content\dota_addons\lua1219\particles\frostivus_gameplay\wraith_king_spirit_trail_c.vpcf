<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 32
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 24.000000
	float m_flConstantLifespan = 0.800000
	int(4) m_ConstantColor = ( 23, 217, 119, 155 )
	float m_flMaxRecreationTime = -1.000000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderRopes_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_FadeInSimple_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_Decay_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_PositionLock_0,
		&C_OP_VectorNoise_0,
		&C_OP_RemapParticleCountOnScalarEndCap_0,
		&C_OP_RemapParticleCountOnScalarEndCap_2
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_CreationNoise_0,
		&C_INIT_RemapCPtoVector_0,
		&C_INIT_RemapParticleCountToScalar_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderRopes C_OP_RenderRopes_0
{
	float m_flSelfIllumAmount = 14.000000
	string m_hTexture = "materials\\particle\\beam_plasma_03.vtex"
	float m_flTextureVOffset = 0.300000
	string m_Notes = ""
	float m_flTextureVScrollRate = -1.250000
	float m_flTextureVWorldSize = 142.857132
	int m_nMaxTesselation = 3
	int m_nMinTesselation = 3
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.750000
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	string m_Notes = ""
	float m_flEndScale = 0.100000
}

C_OP_PositionLock C_OP_PositionLock_0
{
	string m_Notes = ""
	float m_flStartTime_min = -10.000000
	float m_flStartTime_max = -10.000000
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	string m_Notes = ""
	float m_fl4NoiseScale = 0.080000
	int m_nFieldOutput = 0
	float(3) m_vecOutputMin = ( -16.000000, -16.000000, -16.000000 )
	float(3) m_vecOutputMax = ( 16.000000, 16.000000, 16.000000 )
	bool m_bAdditive = true
}

C_OP_RemapParticleCountOnScalarEndCap C_OP_RemapParticleCountOnScalarEndCap_0
{
	string m_Notes = ""
	bool m_bBackwards = true
	int m_nInputMax = 4
	int m_nFieldOutput = 16
}

C_OP_RemapParticleCountOnScalarEndCap C_OP_RemapParticleCountOnScalarEndCap_2
{
	string m_Notes = ""
	bool m_bBackwards = true
	int m_nInputMax = 4
	bool m_bScaleCurrent = true
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
}

C_INIT_CreationNoise C_INIT_CreationNoise_0
{
	string m_Notes = ""
	int m_nFieldOutput = 7
	float m_flOutputMax = 0.200000
	float m_flNoiseScaleLoc = 0.100000
}

C_INIT_RemapCPtoVector C_INIT_RemapCPtoVector_0
{
	float(3) m_vOutputMax = ( 1.000000, 1.000000, 1.000000 )
	int m_nFieldOutput = 6
	float(3) m_vInputMax = ( 255.000000, 255.000000, 255.000000 )
	int m_nCPInput = 15
	string m_Notes = ""
	int m_nOpScaleCP = 16
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_0
{
	bool m_bActiveRange = true
	bool m_bScaleInitialRange = true
	int m_nInputMax = 2
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 32.000000
}