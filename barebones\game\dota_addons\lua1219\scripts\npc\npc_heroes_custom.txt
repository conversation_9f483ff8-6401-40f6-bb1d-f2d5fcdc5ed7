// NOTE: This can be used for creating new heroes, or for taking a currently existing hero as a template and overriding
// the specified key-value combinations.  Use override_hero <hero_to_override> for this.
// Tutorial how to use #base:
// https://moddota.com/tools/combining-kv-files-using-base
//#base "heroes/chaos_knight.txt"

"DOTAHeroes"
{
	// Example of overriding a hero (Ancient Apparition -> Detonator)
	"npc_dota_hero_ancient_apparition_barebones"
	{
		"override_hero"                          "npc_dota_hero_ancient_apparition"

		"AbilityLayout"                          "6"
		"AbilityTalentStart"                     "10"

		"Ability1"        	"example_ability"
		"Ability2"			"ancient_apparition_cold_feet"
		"Ability3"			"ancient_apparition_ice_vortex" 
		"Ability4"        	"detonator_conjure_image"
		"Ability5"			"ancient_apparition_chilling_touch"
		"Ability6"			"batrider_flaming_lasso"
		"Ability7"			"generic_hidden"
		"Ability8"        	""
		"Ability9"        	""
		"Ability10"         "special_bonus_unique_ancient_apparition_7"
		"Ability11"         "special_bonus_spell_amplify_8"
		"Ability12"         "special_bonus_unique_ancient_apparition_3"
		"Ability13"         "special_bonus_hp_regen_12"
		"Ability14"         "special_bonus_unique_ancient_apparition_4"
		"Ability15"         "special_bonus_unique_ancient_apparition_2"
		"Ability16"         "special_bonus_unique_ancient_apparition_6"
		"Ability17"         "special_bonus_unique_batrider_6"
		"Ability18"         ""
		"Ability19"         ""
		"Ability20"         ""
		"Ability21"         ""
		"Ability22"         ""
		"Ability23"         ""
		"Ability24"         ""

		"ArmorPhysical"       		"0"                 	// Physical protection.
		"MagicalResistance"       	"0"                 	// Magical protection (percentage).

		"AttackCapabilities"    	"DOTA_UNIT_CAP_NO_ATTACK"
		"AttackDamageMin"     		"1"                   	// Damage range min.
		"AttackDamageMax"     		"1"                   	// Damage range max.

		"AttributePrimary"      	"DOTA_ATTRIBUTE_STRENGTH"
		"AttributeBaseStrength"   	"0"                   	// Base strength
		"AttributeStrengthGain"   	"0.1"                   // Strength bonus per level.
		"AttributeBaseIntelligence" "0"                   	// Base intelligence
		"AttributeIntelligenceGain" "0.1"                   // Intelligence bonus per level.
		"AttributeBaseAgility"    	"0"                   	// Base agility
		"AttributeAgilityGain"    	"0.1"                   // Agility bonus per level.

		"MovementCapabilities"    	"DOTA_UNIT_CAP_MOVE_GROUND"
		"MovementSpeed"       		"360"
		"MovementTurnRate"      	"1.0"                 	// Turning rate.

		"StatusHealth"        		"1000"                 	// Base health.
		"StatusHealthRegen"     	"1.5"                 	// Base health regeneration
		"StatusMana"        		"400"                   // Base mana.
		"StatusManaRegen"     		"100.0"                 // Base mana regeneration 

		"VisionDaytimeRange"    	"1800"                  // Range of vision at night time.
		"VisionNighttimeRange"    	"1800"                  // Range of vision at night time.
	}
}
