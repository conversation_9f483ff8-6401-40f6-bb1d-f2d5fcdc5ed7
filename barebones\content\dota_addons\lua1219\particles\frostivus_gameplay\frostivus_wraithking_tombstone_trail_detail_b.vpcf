<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 128
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 15.000000
	int(4) m_ConstantColor = ( 255, 255, 255, 55 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_Noise_0,
		&C_OP_Noise_2,
		&C_OP_InterpolateRadius_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_RampScalarLinearSimple_2,
		&C_OP_ColorInterpolate_0,
		&C_OP_PositionLock_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomColor_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomYaw_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_RandomSequence_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	int m_nOrientationType = 3
	float m_flMaxSize = 0.400000
	string m_hTexture = "materials\\particle\\lava_blasts\\lava_glow.vtex"
	float m_flAnimationRate = 1.000000
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float(3) m_Gravity = ( 0.000000, 0.000000, -100.000000 )
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.900000
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	float m_flFadeInTime = 0.100000
	string m_Notes = ""
}

C_OP_Noise C_OP_Noise_0
{
	bool m_bAdditive = true
	float m_flOutputMax = 130.000000
	int m_nFieldOutput = 4
	float m_fl4NoiseScale = 0.001310
	string m_Notes = ""
}

C_OP_Noise C_OP_Noise_2
{
	float m_fl4NoiseScale = 0.001100
	int m_nFieldOutput = 12
	float m_flOutputMax = 90.000000
	bool m_bAdditive = true
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flBias = 0.750000
	float m_flEndScale = 4.000000
	string m_Notes = ""
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	float m_flEndTime = 9999.000000
	float m_Rate = 100.000000
	int m_nOpEndCapState = 1
	string m_Notes = ""
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_2
{
	float m_flEndTime = 99999.000000
	float m_Rate = -2.000000
	int m_nField = 16
	int m_nOpEndCapState = 1
	string m_Notes = ""
}

C_OP_ColorInterpolate C_OP_ColorInterpolate_0
{
	int(4) m_ColorFade = ( 0, 255, 108, 255 )
	string m_Notes = ""
}

C_OP_PositionLock C_OP_PositionLock_0
{
	float m_flEndTime_max = 0.100000
	float m_flEndTime_min = 0.100000
	float m_flStartTime_max = 0.000000
	float m_flStartTime_min = 0.000000
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMin = 0.150000
	float m_fLifetimeMax = 0.180000
	string m_Notes = ""
}

C_INIT_RandomColor C_INIT_RandomColor_0
{
	int(4) m_ColorMin = ( 222, 255, 245, 255 )
	int(4) m_ColorMax = ( 50, 255, 156, 255 )
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	float(3) m_LocalCoordinateSystemSpeedMax = ( 40.000000, 0.000000, 0.000000 )
	float(3) m_LocalCoordinateSystemSpeedMin = ( 50.000000, 0.000000, 0.000000 )
	float m_fSpeedMax = 64.000000
	float m_fRadiusMax = 1.000000
	int m_nControlPointNumber = 3
	string m_Notes = ""
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	bool m_bLocalSpace = true
	float(3) m_vecOutputMin = ( -24.000000, -24.000000, -24.000000 )
	float(3) m_vecOutputMax = ( 24.000000, 24.000000, 24.000000 )
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomYaw C_INIT_RandomYaw_0
{
	float m_flDegreesMin = -4.000000
	float m_flDegreesMax = 4.000000
	string m_Notes = ""
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMin = 6.000000
	float m_flRadiusMax = 12.000000
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 3
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 256.000000
	string m_Notes = ""
}