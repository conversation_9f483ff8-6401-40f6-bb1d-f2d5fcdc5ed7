<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 8
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 250.000000
	int(4) m_ConstantColor = ( 55, 255, 182, 100 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_FadeInSimple_0,
		&C_OP_PositionLock_0,
		&C_OP_RampScalarLinear_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_RandomRotation_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0,
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\particle_flares\\aircraft_white.vtex"
	string m_Notes = ""
	float m_flAnimationRate = 2.000000
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float m_fDrag = 0.050000
	float(3) m_Gravity = ( 0.000000, 0.000000, 25.000000 )
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.125000
}

C_OP_PositionLock C_OP_PositionLock_0
{
	float m_flStartTime_max = 0.000000
	float m_flStartTime_min = 0.000000
	string m_Notes = ""
	int m_nControlPointNumber = 3
}

C_OP_RampScalarLinear C_OP_RampScalarLinear_0
{
	string m_Notes = ""
	float m_RateMax = 5.000000
	float m_RateMin = -5.000000
	int m_nField = 4
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 0.400000
	float m_fLifetimeMax = 0.400000
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
	int m_nControlPointNumber = 3
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	string m_Notes = ""
	float m_flEmitRate = 9.000000
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	int m_nParticlesToEmit = 1
}