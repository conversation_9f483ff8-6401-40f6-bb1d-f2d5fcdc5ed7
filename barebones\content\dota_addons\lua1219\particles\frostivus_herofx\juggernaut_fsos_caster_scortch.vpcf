<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 1
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 0.350000
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderModels_0,
		&C_OP_RenderDeferredLight_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_FadeInSimple_0,
		&C_OP_FadeOutSimple_0,
		&C_OP_RemapScalarOnceTimed_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_RandomSequence_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_PositionOffset_0,
		&C_INIT_PositionPlaceOnGround_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomRadius_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderModels C_OP_RenderModels_0
{
	string m_hOverrideMaterial = ""
	string m_EconSlotName = ""
	string m_Notes = ""
	int m_nBodyGroupField = 13
	int m_nAnimationField = 9
	string m_ActivityName = ""
	bool m_bOrientZ = true
	float m_flAnimationRate = 60.000000
	ModelReference_t[] m_ModelList = 
	[
		ModelReference_t
		{
			string m_model = "models/particle/crater.vmdl"
		}
	]
	bool m_bAnimated = true
}

C_OP_RenderDeferredLight C_OP_RenderDeferredLight_0
{
	string m_Notes = ""
	float m_flRadiusScale = 300.000000
	float m_flAlphaScale = 10.000000
	int(4) m_ColorScale = ( 255, 114, 0, 255 )
	string m_hTexture = "materials\\models\\particle\\crater_color.vtex"
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	string m_Notes = ""
	float m_fDrag = 0.050000
	float m_flOpEndFadeInTime = 5.000000
	float m_flOpStartFadeInTime = 4.000000
	float(3) m_Gravity = ( 0.000000, 0.000000, -10.000000 )
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_FadeInSimple C_OP_FadeInSimple_0
{
	string m_Notes = ""
	float m_flFadeInTime = 0.100000
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	string m_Notes = ""
	float m_flFadeOutTime = 0.450000
}

C_OP_RemapScalarOnceTimed C_OP_RemapScalarOnceTimed_0
{
	string m_Notes = ""
	float m_flOutputMax = 2.000000
	float m_flOutputMin = 2.000000
	int m_nFieldOutput = 9
	float m_flInputMin = 1.000000
	int m_nFieldInput = 9
	float m_flRemapTime = 0.235000
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	string m_Notes = ""
	float m_fLifetimeMin = 3.000000
	float m_fLifetimeMax = 3.000000
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	string m_Notes = ""
	int m_nSequenceMax = 1
	int m_nSequenceMin = 1
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	string m_Notes = ""
}

C_INIT_PositionOffset C_INIT_PositionOffset_0
{
	string m_Notes = ""
	float(3) m_OffsetMin = ( 0.000000, 0.000000, 128.000000 )
	float(3) m_OffsetMax = ( 0.000000, 0.000000, 128.000000 )
}

C_INIT_PositionPlaceOnGround C_INIT_PositionPlaceOnGround_0
{
	string m_Notes = ""
	float m_flOffset = 4.000000
	bool m_bSetNormal = true
	float m_flMaxTraceLength = 512.000000
	string m_CollisionGroupName = "DEBRIS"
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
	int m_nFieldOutput = 12
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	string m_Notes = ""
	float m_flRadiusMin = 0.300000
	float m_flRadiusMax = 0.450000
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
	float m_flStartTime = 0.150000
	int m_nParticlesToEmit = 1
}