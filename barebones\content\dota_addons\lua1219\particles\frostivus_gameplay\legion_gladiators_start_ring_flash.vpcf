<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 100
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	int(4) m_ConstantColor = ( 255, 245, 129, 255 )
	int m_nConstantSequenceNumber = 4
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_FadeOutSimple_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_RampScalarLinearSimple_0,
		&C_OP_AlphaDecay_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RingWave_0,
		&C_INIT_RandomRadius_0,
		&C_INIT_PositionPlaceOnGround_0,
		&C_INIT_RandomLifeTime_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_InstantaneousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	int m_bAdditive = 1
	string m_hTexture = "materials\\particle\\particle_flares\\aircraft_white.vtex"
	int m_nOrientationType = 2
	string m_Notes = ""
}

C_OP_FadeOutSimple C_OP_FadeOutSimple_0
{
	float m_flFadeOutTime = 0.500000
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flEndScale = 1.500000
	string m_Notes = ""
}

C_OP_RampScalarLinearSimple C_OP_RampScalarLinearSimple_0
{
	float m_flEndTime = 999999.000000
	float m_Rate = -2.000000
	int m_nField = 7
	string m_Notes = ""
	int m_nOpEndCapState = 1
}

C_OP_AlphaDecay C_OP_AlphaDecay_0
{
	float m_flMinAlpha = 0.001000
	string m_Notes = ""
}

C_INIT_RingWave C_INIT_RingWave_0
{
	float m_flParticlesPerOrbit = 100.000000
	bool m_bEvenDistribution = true
	float m_flInitialRadius = 625.000000
	string m_Notes = ""
	int m_nControlPointNumber = 7
}

C_INIT_RandomRadius C_INIT_RandomRadius_0
{
	float m_flRadiusMax = 50.000000
	float m_flRadiusMin = 50.000000
	string m_Notes = ""
}

C_INIT_PositionPlaceOnGround C_INIT_PositionPlaceOnGround_0
{
	bool m_bIncludeWater = true
	float m_flOffset = 5.000000
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 15.000000
	float m_fLifetimeMin = 15.000000
	string m_Notes = ""
}

C_OP_InstantaneousEmitter C_OP_InstantaneousEmitter_0
{
	string m_Notes = ""
}