-- This is the primary barebones gamemode script and should be used to assist in initializing your game mode
-- 这是主要的 barebones 游戏模式脚本，用于协助初始化你的游戏模式
BAREBONES_VERSION = "2.0.18" -- 定义 barebones 框架的版本号

-- Selection library (by Noya) provides player selection inspection and management from server lua
-- 加载选择库 (由 Noya 编写)，用于从服务器 Lua 端检查和管理玩家选择
require('libraries/selection')

-- settings.lua is where you can specify many different properties for your game mode and is one of the core barebones files.
-- 加载 settings.lua 文件，你可以在此文件中指定游戏模式的许多不同属性，是 barebones 的核心文件之一
require('settings')
-- events.lua is where you can specify the actions to be taken when any event occurs and is one of the core barebones files.
-- 加载 events.lua 文件，你可以在此文件中指定当任何事件发生时要采取的行动，是 barebones 的核心文件之一
require('events')
-- filters.lua
-- 加载 filters.lua 文件，用于定义游戏内的各种行为过滤器
require('filters')

if USE_CUSTOM_ROSHAN then
	require('components/roshan/init') -- 加载自定义 Roshan 的初始化脚本
end

if USE_CUSTOM_TORMENTOR then
	require('components/tormentor/init') -- 加载自定义 Tormentor 的初始化脚本
end

--[[
  This function should be used to set up Async precache calls at the beginning of the gameplay.
  此函数应用于在游戏开始时设置异步预缓存调用。

  In this function, place all of your PrecacheItemByNameAsync and PrecacheUnitByNameAsync.  These calls will be made
  after all players have loaded in, but before they have selected their heroes. PrecacheItemByNameAsync can also
  be used to precache dynamically-added datadriven abilities instead of items.  PrecacheUnitByNameAsync will
  precache the precache{} block statement of the unit and all precache{} block statements for every Ability#
  defined on the unit.
  在此函数中，放置所有的 PrecacheItemByNameAsync (异步按名称预缓存物品/技能) 和 PrecacheUnitByNameAsync (异步按名称预缓存单位)。
  这些调用将在所有玩家加载完成之后，但在他们选择英雄之前进行。PrecacheItemByNameAsync 也可以用于预缓存动态添加的数据驱动技能，而不仅仅是物品。
  PrecacheUnitByNameAsync 将预缓存单位定义中的 precache{} 块语句，以及该单位上定义的每个 Ability# 的 precache{} 块语句。

  This function should only be called once.  If you want to/need to precache more items/abilities/units at a later
  time, you can call the functions individually (for example if you want to precache units in a new wave of
  holdout).
  此函数应该只被调用一次。如果你想或需要在稍后的时间预缓存更多的物品/技能/单位，你可以单独调用这些函数
  (例如，如果你想在新的守卫波次中预缓存单位)。

  This function should generally only be used if the Precache() function in addon_game_mode.lua is not working.
  通常只有在 addon_game_mode.lua 中的 Precache() 函数不起作用时才应使用此函数。
]]
function barebones:PostLoadPrecache()
	DebugPrint("[BAREBONES] Performing Post-Load precache.") -- 输出调试信息：执行加载后预缓存
	--PrecacheItemByNameAsync("item_example_item", function(...) end) -- 示例：异步预缓存名为 "item_example_item" 的物品
	--PrecacheItemByNameAsync("example_ability", function(...) end) -- 示例：异步预缓存名为 "example_ability" 的技能

	--PrecacheUnitByNameAsync("npc_dota_hero_viper", function(...) end) -- 示例：异步预缓存英雄 "npc_dota_hero_viper"
	--PrecacheUnitByNameAsync("npc_dota_hero_enigma", function(...) end) -- 示例：异步预缓存英雄 "npc_dota_hero_enigma"
end

--[[
  This function is called once and only once after all players have loaded into the game, right as the hero selection time begins.
  此函数在所有玩家加载进入游戏后，英雄选择时间开始时，仅被调用一次。
  It can be used to initialize non-hero player state or adjust the hero selection (i.e. force random etc)
  它可以用来初始化非英雄玩家状态或调整英雄选择（例如强制随机等）。
]]
function barebones:OnAllPlayersLoaded()
	DebugPrint("[BAREBONES] All Players have loaded into the game.") -- 输出调试信息：所有玩家已加载进入游戏
end

--[[
  This function is called once and only once when the game completely begins (about 0:00 on the clock).  At this point,
  gold will begin to go up in ticks if configured, creeps will spawn, towers will become damageable etc.  This function
  is useful for starting any game logic timers/thinkers, beginning the first round, etc.
  此函数在游戏完全开始时（大约在游戏时间 0:00），仅被调用一次。此时，如果已配置，金钱将开始按时间增加，
  小兵将开始刷新，防御塔将变得可被攻击等。此函数对于启动任何游戏逻辑计时器/思考器，开始第一回合等非常有用。
]]
function barebones:OnGameInProgress()
	DebugPrint("[BAREBONES] The game has officially begun.") -- 输出调试信息：游戏已正式开始

	-- If the day/night is not changed at 00:00, the following line is needed:
	-- 如果在 00:00 时昼夜没有改变，则需要下面这行代码（设置一个稍微偏离纯黑夜/白天的值，以触发初始状态）：
	GameRules:SetTimeOfDay(0.251) -- 设置初始游戏时间为接近白天开始的时刻
end

-- This function initializes the game mode and is called before anyone loads into the game
-- 此函数用于初始化游戏模式，在任何玩家加载进入游戏之前被调用
-- It can be used to pre-initialize any values/tables that will be needed later
-- 它可以用来预先初始化稍后将需要的任何值/表
function barebones:InitGameMode()
	DebugPrint("[BAREBONES] Starting to load Game Rules.") -- 输出调试信息：开始加载游戏规则

	-- Setup rules -- 设置游戏规则
	GameRules:SetSameHeroSelectionEnabled(ALLOW_SAME_HERO_SELECTION) -- 设置是否允许选择相同英雄 (值来自 settings.lua)
	GameRules:SetUseUniversalShopMode(UNIVERSAL_SHOP_MODE) -- 设置是否使用通用商店模式 (值来自 settings.lua)
	GameRules:SetHeroRespawnEnabled(ENABLE_HERO_RESPAWN) -- 设置是否启用英雄重生 (值来自 settings.lua)

	GameRules:SetHeroSelectionTime(HERO_SELECTION_TIME) -- 设置英雄选择时间 (值来自 settings.lua) -- 注意：当 addoninfo.txt 中的 "EnablePickRules" 为 "1" 时，此设置会被忽略！
	GameRules:SetHeroSelectPenaltyTime(HERO_SELECTION_PENALTY_TIME) -- 设置英雄选择惩罚时间（超时未选随机） (值来自 settings.lua)

	GameRules:SetPreGameTime(PRE_GAME_TIME) -- 设置游戏开始前时间 (值来自 settings.lua)
	GameRules:SetPostGameTime(POST_GAME_TIME) -- 设置游戏结束后时间 (值来自 settings.lua)
	GameRules:SetShowcaseTime(SHOWCASE_TIME) -- 设置英雄展示时间 (值来自 settings.lua)
	GameRules:SetStrategyTime(STRATEGY_TIME) -- 设置策略时间 (值来自 settings.lua)

	GameRules:SetTreeRegrowTime(TREE_REGROW_TIME) -- 设置树木再生时间 (值来自 settings.lua)

	-- 如果在 settings.lua 中启用了自定义英雄等级
	if USE_CUSTOM_HERO_LEVELS then
		GameRules:SetUseCustomHeroXPValues(true) -- 启用自定义英雄经验值
	end

	--GameRules:SetGoldPerTick(GOLD_PER_TICK) -- 设置每次跳钱的金钱数量 (不起作用; 最后测试时间: 2020年2月24日)
	--GameRules:SetGoldTickTime(GOLD_TICK_TIME) -- 设置跳钱的时间间隔 (不起作用; 最后测试时间: 2020年2月24日)
	GameRules:SetStartingGold(NORMAL_START_GOLD) -- 设置初始金钱 (值来自 settings.lua)

	-- 如果在 settings.lua 中启用了自定义英雄金钱奖励
	if USE_CUSTOM_HERO_GOLD_BOUNTY then
		GameRules:SetUseBaseGoldBountyOnHeroes(false) -- 设置不使用英雄的基础金钱奖励（如果为 true，英雄将使用类似小兵金钱奖励的默认基础金钱奖励，而不是 DOTA 特定的公式）
	end

	GameRules:SetHeroMinimapIconScale(MINIMAP_ICON_SIZE) -- 设置小地图上英雄图标的大小 (值来自 settings.lua)
	GameRules:SetCreepMinimapIconScale(MINIMAP_CREEP_ICON_SIZE) -- 设置小地图上小兵图标的大小 (值来自 settings.lua)
	GameRules:SetRuneMinimapIconScale(MINIMAP_RUNE_ICON_SIZE) -- 设置小地图上神符图标的大小 (值来自 settings.lua)
	GameRules:SetFirstBloodActive(ENABLE_FIRST_BLOOD) -- 设置是否启用第一滴血奖励 (值来自 settings.lua)
	GameRules:SetHideKillMessageHeaders(HIDE_KILL_BANNERS) -- 设置是否隐藏击杀横幅 (值来自 settings.lua)
	GameRules:LockCustomGameSetupTeamAssignment(LOCK_TEAMS) -- 设置是否锁定队伍分配 (值来自 settings.lua)
	
	-- TO TEST: -- 以下为待测试的游戏规则设置
	--GameRules:SetAllowOutpostBonuses(true) -- 允许前哨站加成？
	--GameRules:SetEnableAlternateHeroGrids(false) -- 禁用备用英雄网格 (DOTA+ 等)，当你有自定义英雄时很有用
	--GameRules:SetSuggestItemsEnabled(false) -- 禁用推荐出装
	-- HERO BLACK LIST -- 英雄黑名单
	--GameRules:SetHideBlacklistedHeroes(true) -- true 为隐藏黑名单英雄，false 为在英雄选择时置灰
	--GameRules:AddHeroIDToBlacklist(int) -- 将英雄 ID 添加到黑名单
	--GameRules:AddHeroToBlacklist(string) -- 将英雄名称添加到黑名单
	--GameRules:ClearHeroBlacklist() -- 清空英雄黑名单
	--GameRules:RemoveHeroFromBlacklist(string) -- 从黑名单移除英雄名称
	--GameRules:RemoveHeroIDFromBlacklist(int) -- 从黑名单移除英雄 ID
	-- ITEM WHITE LIST -- 物品白名单
	--GameRules:SetWhiteListEnabled(true) -- 启用物品白名单
	--GameRules:AddItemToWhiteList(string) -- 将物品添加到白名单
	--GameRules:IsItemInWhiteList(string) -- 检查物品是否在白名单中
	--GameRules:RemoveItemFromWhiteList(string) -- 从白名单移除物品

	-- This is multi-team configuration stuff -- 这是多队伍配置相关设置
	-- 如果在 settings.lua 中启用了自动分配每队玩家数
	if USE_AUTOMATIC_PLAYERS_PER_TEAM then
		local num = math.floor(10 / MAX_NUMBER_OF_TEAMS) -- 计算每队平均玩家数 (基于最多10个玩家和最大队伍数)
		local count = 0
		-- 遍历 settings.lua 中定义的 TEAM_COLORS 表
		for team, number in pairs(TEAM_COLORS) do
			-- 如果当前队伍索引超出了最大队伍数
			if count >= MAX_NUMBER_OF_TEAMS then
				GameRules:SetCustomGameTeamMaxPlayers(team, 0) -- 设置该队伍最大玩家数为 0 (禁用)
			else
				GameRules:SetCustomGameTeamMaxPlayers(team, num) -- 设置该队伍最大玩家数为计算出的平均值
			end
			count = count + 1
		end
	else -- 如果不使用自动分配
		local count = 0
		-- 遍历 settings.lua 中定义的 CUSTOM_TEAM_PLAYER_COUNT 表
		for team, number in pairs(CUSTOM_TEAM_PLAYER_COUNT) do
			-- 如果当前队伍索引超出了最大队伍数
			if count >= MAX_NUMBER_OF_TEAMS then
				GameRules:SetCustomGameTeamMaxPlayers(team, 0) -- 设置该队伍最大玩家数为 0 (禁用)
			else
				GameRules:SetCustomGameTeamMaxPlayers(team, number) -- 使用 settings.lua 中为该队伍指定的玩家数
			end
			count = count + 1
		end
	end

	-- 如果在 settings.lua 中启用了自定义队伍颜色
	if USE_CUSTOM_TEAM_COLORS then
		-- 遍历 settings.lua 中定义的 TEAM_COLORS 表
		for team, color in pairs(TEAM_COLORS) do
			SetTeamCustomHealthbarColor(team, color[1], color[2], color[3]) -- 设置队伍的自定义血条颜色 (RGB)
		end
	end

	DebugPrint("[BAREBONES] Done with setting Game Rules.") -- 输出调试信息：完成游戏规则设置

	-- Event Hooks / Listeners -- 事件钩子 / 监听器
	DebugPrint("[BAREBONES] Setting Event Hooks / Listeners.") -- 输出调试信息：设置事件钩子/监听器
	-- 监听 dota_player_gained_level 事件 (玩家升级)，触发 barebones:OnPlayerLevelUp 函数 (定义在 events.lua)
	ListenToGameEvent('dota_player_gained_level', Dynamic_Wrap(barebones, 'OnPlayerLevelUp'), self)
	-- 监听 dota_player_learned_ability 事件 (玩家学习技能)，触发 barebones:OnPlayerLearnedAbility 函数 (定义在 events.lua)
	ListenToGameEvent('dota_player_learned_ability', Dynamic_Wrap(barebones, 'OnPlayerLearnedAbility'), self)
	-- 监听 entity_killed 事件 (实体被击杀)，触发 barebones:OnEntityKilled 函数 (定义在 events.lua)
	ListenToGameEvent('entity_killed', Dynamic_Wrap(barebones, 'OnEntityKilled'), self)
	-- 监听 player_connect_full 事件 (玩家完全连接)，触发 barebones:OnConnectFull 函数 (定义在 events.lua)
	ListenToGameEvent('player_connect_full', Dynamic_Wrap(barebones, 'OnConnectFull'), self)
	-- 监听 player_disconnect 事件 (玩家断开连接)，触发 barebones:OnDisconnect 函数 (定义在 events.lua)
	ListenToGameEvent('player_disconnect', Dynamic_Wrap(barebones, 'OnDisconnect'), self)
	-- 监听 dota_item_picked_up 事件 (物品被拾取)，触发 barebones:OnItemPickedUp 函数 (定义在 events.lua)
	ListenToGameEvent('dota_item_picked_up', Dynamic_Wrap(barebones, 'OnItemPickedUp'), self)
	-- 监听 last_hit 事件 (完成补刀)，触发 barebones:OnLastHit 函数 (定义在 events.lua)
	ListenToGameEvent('last_hit', Dynamic_Wrap(barebones, 'OnLastHit'), self)
	-- 监听 dota_rune_activated_server 事件 (神符被激活 - 服务器端)，触发 barebones:OnRuneActivated 函数 (定义在 events.lua)
	ListenToGameEvent('dota_rune_activated_server', Dynamic_Wrap(barebones, 'OnRuneActivated'), self)
	-- 监听 tree_cut 事件 (树木被砍伐)，触发 barebones:OnTreeCut 函数 (定义在 events.lua)
	ListenToGameEvent('tree_cut', Dynamic_Wrap(barebones, 'OnTreeCut'), self)

	-- 监听 dota_player_used_ability 事件 (玩家使用技能)，触发 barebones:OnAbilityUsed 函数 (定义在 events.lua)
	ListenToGameEvent('dota_player_used_ability', Dynamic_Wrap(barebones, 'OnAbilityUsed'), self)
	-- 监听 game_rules_state_change 事件 (游戏规则状态改变)，触发 barebones:OnGameRulesStateChange 函数 (定义在 events.lua)
	ListenToGameEvent('game_rules_state_change', Dynamic_Wrap(barebones, 'OnGameRulesStateChange'), self)
	-- 监听 npc_spawned 事件 (NPC 生成)，触发 barebones:OnNPCSpawned 函数 (定义在 events.lua)
	ListenToGameEvent('npc_spawned', Dynamic_Wrap(barebones, 'OnNPCSpawned'), self)
	-- 监听 dota_player_pick_hero 事件 (玩家选择英雄)，触发 barebones:OnPlayerPickHero 函数 (定义在 events.lua)
	ListenToGameEvent('dota_player_pick_hero', Dynamic_Wrap(barebones, 'OnPlayerPickHero'), self)
	-- 监听 player_reconnected 事件 (玩家重新连接)，触发 barebones:OnPlayerReconnect 函数 (定义在 events.lua)
	ListenToGameEvent("player_reconnected", Dynamic_Wrap(barebones, 'OnPlayerReconnect'), self)
	-- 监听 player_chat 事件 (玩家聊天)，触发 barebones:OnPlayerChat 函数 (定义在 events.lua)
	ListenToGameEvent("player_chat", Dynamic_Wrap(barebones, 'OnPlayerChat'), self)

	-- 监听 dota_tower_kill 事件 (防御塔被摧毁)，触发 barebones:OnTowerKill 函数 (定义在 events.lua)
	ListenToGameEvent("dota_tower_kill", Dynamic_Wrap(barebones, 'OnTowerKill'), self)
	-- 监听 dota_player_selected_custom_team 事件 (玩家选择自定义队伍)，触发 barebones:OnPlayerSelectedCustomTeam 函数 (定义在 events.lua)
	ListenToGameEvent("dota_player_selected_custom_team", Dynamic_Wrap(barebones, 'OnPlayerSelectedCustomTeam'), self)
	-- 监听 dota_npc_goal_reached 事件 (NPC 到达目标点)，触发 barebones:OnNPCGoalReached 函数 (定义在 events.lua)
	ListenToGameEvent("dota_npc_goal_reached", Dynamic_Wrap(barebones, 'OnNPCGoalReached'), self)

	-- Change random seed for math.random function -- 为 math.random 函数更改随机种子
	local timeTxt = string.gsub(string.gsub(GetSystemTime(), ':', ''), '0','') -- 获取系统时间，移除冒号和0
	local timeNumber = tonumber(timeTxt) -- 将处理后的时间字符串转换为数字
	if timeNumber then
		math.randomseed(timeNumber) -- 使用时间数字作为随机种子 (推荐使用引擎提供的 RandomInt 或 RandomFloat 代替 math.random)
	end

	DebugPrint("[BAREBONES] Setting Filters.") -- 输出调试信息：设置过滤器

	local gamemode = GameRules:GetGameModeEntity() -- 获取游戏模式实体

	-- Setting the Order filter -- 设置指令过滤器
	-- 当发出指令时，调用 barebones:OrderFilter 函数 (定义在 filters.lua)
	gamemode:SetExecuteOrderFilter(Dynamic_Wrap(barebones, "OrderFilter"), self)

	-- Setting the Damage filter -- 设置伤害过滤器
	-- 当造成伤害时，调用 barebones:DamageFilter 函数 (定义在 filters.lua)
	gamemode:SetDamageFilter(Dynamic_Wrap(barebones, "DamageFilter"), self)

	-- Setting the Modifier filter -- 设置修饰器 (Modifier) 获得过滤器
	-- 当获得修饰器时，调用 barebones:ModifierFilter 函数 (定义在 filters.lua)
	gamemode:SetModifierGainedFilter(Dynamic_Wrap(barebones, "ModifierFilter"), self)

	-- Setting the Experience filter -- 设置经验过滤器
	-- 当修改经验值时，调用 barebones:ExperienceFilter 函数 (定义在 filters.lua)
	gamemode:SetModifyExperienceFilter(Dynamic_Wrap(barebones, "ExperienceFilter"), self)

	-- Setting the Tracking Projectile filter -- 设置追踪投射物过滤器
	-- 当创建追踪投射物时，调用 barebones:ProjectileFilter 函数 (定义在 filters.lua)
	gamemode:SetTrackingProjectileFilter(Dynamic_Wrap(barebones, "ProjectileFilter"), self)

	-- Setting the bounty rune pickup filter -- 设置赏金神符拾取过滤器
	-- 当拾取赏金神符时，调用 barebones:BountyRuneFilter 函数 (定义在 filters.lua)
	gamemode:SetBountyRunePickupFilter(Dynamic_Wrap(barebones, "BountyRuneFilter"), self)

	-- Setting the Healing filter -- 设置治疗过滤器
	-- 当进行治疗时，调用 barebones:HealingFilter 函数 (定义在 filters.lua)
	gamemode:SetHealingFilter(Dynamic_Wrap(barebones, "HealingFilter"), self)

	-- Setting the Gold Filter -- 设置金钱过滤器
	-- 当修改金钱时，调用 barebones:GoldFilter 函数 (定义在 filters.lua)
	gamemode:SetModifyGoldFilter(Dynamic_Wrap(barebones, "GoldFilter"), self)

	-- Setting the Inventory filter -- 设置物品栏过滤器
	-- 当物品被添加到物品栏时，调用 barebones:InventoryFilter 函数 (定义在 filters.lua)
	gamemode:SetItemAddedToInventoryFilter(Dynamic_Wrap(barebones, "InventoryFilter"), self)

	DebugPrint("[BAREBONES] Done with setting Filters.") -- 输出调试信息：完成过滤器设置

	-- Global Lua Modifiers -- 链接全局 Lua 修饰器 (Modifier)
	-- 链接自定义的无敌修饰器
	LinkLuaModifier("modifier_custom_invulnerable", "modifiers/modifier_custom_invulnerable.lua", LUA_MODIFIER_MOTION_NONE)
	-- 链接自定义的被动金钱修饰器示例
	LinkLuaModifier("modifier_custom_passive_gold", "modifiers/modifier_custom_passive_gold_example.lua", LUA_MODIFIER_MOTION_NONE)

	print("[BAREBONES] initialized.") -- 输出信息：barebones 已初始化
	DebugPrint("[BAREBONES] Done loading the game mode!\n\n") -- 输出调试信息：完成加载游戏模式！

	-- Increase/decrease maximum item limit per hero -- 增加/减少每个英雄的最大物品限制
	Convars:SetInt('dota_max_physical_items_purchase_limit', 64) -- 设置物理物品（背包+储藏处）购买上限为 64
end

-- This function is called as the first player loads and sets up the game mode parameters
-- 此函数在第一个玩家加载时被调用，并设置游戏模式参数
function barebones:CaptureGameMode()
	local gamemode = GameRules:GetGameModeEntity() -- 获取游戏模式实体

	-- Set GameMode parameters -- 设置游戏模式参数
	gamemode:SetRecommendedItemsDisabled(RECOMMENDED_BUILDS_DISABLED) -- 设置是否禁用推荐出装 (值来自 settings.lua)
	gamemode:SetCameraDistanceOverride(CAMERA_DISTANCE_OVERRIDE) -- 设置镜头距离覆盖 (值来自 settings.lua)
	gamemode:SetBuybackEnabled(BUYBACK_ENABLED) -- 设置是否启用买活 (值来自 settings.lua)
	gamemode:SetCustomBuybackCostEnabled(CUSTOM_BUYBACK_COST_ENABLED) -- 设置是否启用自定义买活花费 (值来自 settings.lua)
	gamemode:SetCustomBuybackCooldownEnabled(CUSTOM_BUYBACK_COOLDOWN_ENABLED) -- 设置是否启用自定义买活冷却 (值来自 settings.lua)
	gamemode:SetTopBarTeamValuesOverride(USE_CUSTOM_TOP_BAR_VALUES) -- 设置是否覆盖顶部栏队伍数值 (可能无效，但保留) (值来自 settings.lua)
	gamemode:SetTopBarTeamValuesVisible(TOP_BAR_VISIBLE) -- 设置顶部栏队伍数值是否可见 (值来自 settings.lua)

	-- 如果在 settings.lua 中启用了自定义经验值
	if USE_CUSTOM_XP_VALUES then
		gamemode:SetUseCustomHeroLevels(true) -- 启用自定义英雄等级
		gamemode:SetCustomXPRequiredToReachNextLevel(XP_PER_LEVEL_TABLE) -- 设置达到下一级所需的自定义经验值表 (值来自 settings.lua)
	-- 否则，如果设置了与默认不同的最大等级
	elseif MAX_LEVEL ~= 30 then
		gamemode:SetCustomHeroMaxLevel(MAX_LEVEL) -- 设置自定义英雄最大等级 (值来自 settings.lua) -- 如果使用了 SetCustomXPRequiredToReachNextLevel 则不需要此项
	end

	gamemode:SetBotThinkingEnabled(USE_STANDARD_DOTA_BOT_THINKING) -- 设置是否启用标准 Dota 机器人思考逻辑 (值来自 settings.lua)
	gamemode:SetTowerBackdoorProtectionEnabled(ENABLE_TOWER_BACKDOOR_PROTECTION) -- 设置是否启用防御塔后门保护 (值来自 settings.lua)

	gamemode:SetFogOfWarDisabled(DISABLE_FOG_OF_WAR_ENTIRELY) -- 设置是否完全禁用战争迷雾 (值来自 settings.lua)
	gamemode:SetGoldSoundDisabled(DISABLE_GOLD_SOUNDS) -- 设置是否禁用金钱音效 (值来自 settings.lua)
	--gamemode:SetRemoveIllusionsOnDeath(REMOVE_ILLUSIONS_ON_DEATH) -- 设置死亡时是否移除幻象 (上次尝试时无效) (值来自 settings.lua)

	gamemode:SetAlwaysShowPlayerInventory(SHOW_ONLY_PLAYER_INVENTORY) -- 设置是否总是显示玩家物品栏 (而非观察者或其他) (值来自 settings.lua)
	--gamemode:SetAlwaysShowPlayerNames(true) -- 设置总是显示玩家名称 (当你需要隐藏真实英雄名称时使用)
	gamemode:SetAnnouncerDisabled(DISABLE_ANNOUNCER) -- 设置是否禁用播音员 (值来自 settings.lua)

	-- 如果在 settings.lua 中设置了强制选择的英雄
	if FORCE_PICKED_HERO then -- FORCE_PICKED_HERO 必须是一个存在的英雄的字符串名称，否则会报错
		gamemode:SetCustomGameForceHero(FORCE_PICKED_HERO) -- 设置自定义游戏强制选择英雄 (值来自 settings.lua) -- 注意：当 addoninfo.txt 中的 "EnablePickRules" 为 "1" 时，此设置无效！
	else -- 如果没有设置强制选择英雄
		gamemode:SetDraftingHeroPickSelectTimeOverride(HERO_SELECTION_TIME) -- 设置选人阶段英雄选择时间覆盖 (值来自 settings.lua)
		gamemode:SetDraftingBanningTimeOverride(0) -- 默认设置禁用阶段时间为 0
		-- 如果在 settings.lua 中启用了禁用阶段
		if ENABLE_BANNING_PHASE then
			gamemode:SetDraftingBanningTimeOverride(BANNING_PHASE_TIME) -- 设置禁用阶段时间 (值来自 settings.lua)
			GameRules:SetCustomGameBansPerTeam(5) -- 设置每个队伍的禁用名额为 5 (可以修改)
		end
	end

	--gamemode:SetFixedRespawnTime(FIXED_RESPAWN_TIME) -- 设置固定重生时间 (FIXED_RESPAWN_TIME 应为浮点数) (值来自 settings.lua)
	gamemode:SetFountainConstantManaRegen(FOUNTAIN_CONSTANT_MANA_REGEN) -- 设置泉水固定魔法恢复 (值来自 settings.lua)
	gamemode:SetFountainPercentageHealthRegen(FOUNTAIN_PERCENTAGE_HEALTH_REGEN) -- 设置泉水百分比生命恢复 (值来自 settings.lua)
	gamemode:SetFountainPercentageManaRegen(FOUNTAIN_PERCENTAGE_MANA_REGEN) -- 设置泉水百分比魔法恢复 (值来自 settings.lua)
	gamemode:SetLoseGoldOnDeath(LOSE_GOLD_ON_DEATH) -- 设置死亡时是否损失金钱 (值来自 settings.lua)
	gamemode:SetMaximumAttackSpeed(MAXIMUM_ATTACK_SPEED) -- 设置最大攻击速度 (值来自 settings.lua)
	gamemode:SetMinimumAttackSpeed(MINIMUM_ATTACK_SPEED) -- 设置最小攻击速度 (值来自 settings.lua)
	gamemode:SetStashPurchasingDisabled(DISABLE_STASH_PURCHASING) -- 设置是否禁用储藏处购买 (值来自 settings.lua)

	-- 如果在 settings.lua 中使用默认的神符系统
	if USE_DEFAULT_RUNE_SYSTEM then
		gamemode:SetUseDefaultDOTARuneSpawnLogic(true) -- 使用默认 Dota 神符刷新逻辑
	else -- 如果使用自定义神符系统
		-- Valve 破坏了一些神符，RuneSpawnFilter 上次尝试时也无效
		-- 遍历 settings.lua 中定义的 ENABLED_RUNES 表
		for rune, spawn in pairs(ENABLED_RUNES) do
			gamemode:SetRuneEnabled(rune, spawn) -- 根据表中的设置启用或禁用特定神符
		end
		gamemode:SetBountyRuneSpawnInterval(BOUNTY_RUNE_SPAWN_INTERVAL) -- 设置赏金神符刷新间隔 (值来自 settings.lua)
		gamemode:SetPowerRuneSpawnInterval(POWER_RUNE_SPAWN_INTERVAL) -- 设置能量神符刷新间隔 (值来自 settings.lua)
	end

	gamemode:SetUnseenFogOfWarEnabled(USE_UNSEEN_FOG_OF_WAR) -- 设置是否启用未探索区域的战争迷雾 (完全黑色) (值来自 settings.lua)
	gamemode:SetDaynightCycleDisabled(DISABLE_DAY_NIGHT_CYCLE) -- 设置是否禁用昼夜循环 (值来自 settings.lua)
	gamemode:SetKillingSpreeAnnouncerDisabled(DISABLE_KILLING_SPREE_ANNOUNCER) -- 设置是否禁用连杀播报 (值来自 settings.lua)
	gamemode:SetStickyItemDisabled(DISABLE_STICKY_ITEM) -- 设置是否禁用粘滞物品（信使上的 TP 等） (值来自 settings.lua)
	gamemode:SetPauseEnabled(ENABLE_PAUSING) -- 设置是否启用暂停 (值来自 settings.lua)
	gamemode:SetCustomScanCooldown(CUSTOM_SCAN_COOLDOWN) -- 设置自定义扫描冷却时间 (值来自 settings.lua)
	gamemode:SetCustomGlyphCooldown(CUSTOM_GLYPH_COOLDOWN) -- 设置自定义防御符文（圣坛）冷却时间 (值来自 settings.lua)
	gamemode:DisableHudFlip(FORCE_MINIMAP_ON_THE_LEFT) -- 禁用 HUD 翻转（强制小地图在左侧）(值来自 settings.lua)

	gamemode:SetFreeCourierModeEnabled(true) -- 启用免费信使模式（没有这个，被动 GPM 不起作用，感谢 Valve）
	--gamemode:SetUseTurboCouriers(true) -- 使用加速模式信使？
	--gamemode:SetGiveFreeTPOnDeath(false) -- 禁用死亡时获得免费 TP 卷轴
	--gamemode:SetTPScrollSlotItemOverride(itemname) -- 将 TP 卷轴槽位替换为其他物品
	--gamemode:SetSelectionGoldPenaltyEnabled(false) -- 禁用英雄选择金钱惩罚

	-- TO TEST: -- 以下为待测试的游戏模式参数
	--gamemode:SetAllowNeutralItemDrops(false) -- 禁用中立物品掉落？
	--gamemode:SetCanSellAnywhere(true) -- 全局商店？（可以在任何地方出售）
	--gamemode:SetFriendlyBuildingMoveToEnabled(true) -- 也许启用右键点击友方建筑移动而没有无敌警告？
	--gamemode:SetKillableTombstones(true) -- 死亡时掉落墓碑还是无用？
	--gamemode:SetNeutralItemHideUndiscoveredEnabled(true) -- 未发现的中立物品在储藏处隐藏？
	--gamemode:SetNeutralStashTeamViewOnlyEnabled(true) -- 无法查看所有中立物品标签？只能看到已找到的？
	--gamemode:SetRandomHeroBonusItemGrantDisabled(false) -- 启用随机英雄时获得仙灵之火和芒果？
end
