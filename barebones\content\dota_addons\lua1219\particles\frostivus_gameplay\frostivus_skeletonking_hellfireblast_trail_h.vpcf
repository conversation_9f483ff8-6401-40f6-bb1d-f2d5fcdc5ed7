<!-- schema text {7e125a45-3d83-4043-b292-9e24f8ef27b4} generic {198980d8-3a93-4919-b4c6-dd1fb07a3a4b} -->
CParticleSystemDefinition CParticleSystemDefinition_0
{
	bool m_bPreventNameBasedLookup = false
	int m_nMaxParticles = 512
	string m_pszSnapshotName = ""
	string m_hLowViolenceDef = ""
	string m_hReferenceReplacement = ""
	string m_hFallback = ""
	float m_flConstantRadius = 15.000000
	int(4) m_ConstantColor = ( 0, 0, 0, 255 )
	CParticleOperator*[] m_Renderers = 
	[
		&C_OP_RenderSprites_0
	]
	CParticleOperator*[] m_Operators = 
	[
		&C_OP_BasicMovement_0,
		&C_OP_Decay_0,
		&C_OP_Noise_0,
		&C_OP_InterpolateRadius_0,
		&C_OP_VectorNoise_0
	]
	CParticleOperator*[] m_Initializers = 
	[
		&C_INIT_RandomLifeTime_0,
		&C_INIT_CreateWithinSphere_0,
		&C_INIT_InitialVelocityNoise_0,
		&C_INIT_CreationNoise_0,
		&C_INIT_RandomRotation_0,
		&C_INIT_RandomYaw_0,
		&C_INIT_RandomYawFlip_0,
		&C_INIT_RemapParticleCountToScalar_0,
		&C_INIT_RandomSequence_0
	]
	CParticleOperator*[] m_Emitters = 
	[
		&C_OP_ContinuousEmitter_0
	]
	CParticleOperator*[] m_ForceGenerators = 
	[
		
	]
	CParticleOperator*[] m_Constraints = 
	[
		
	]
	ParticleChildrenInfo_t[] m_Children = 
	[
		
	]
}

C_OP_RenderSprites C_OP_RenderSprites_0
{
	string m_hTexture = "materials\\particle\\impact\\fleks3.vtex"
	float m_flAnimationRate = 1.500000
	string m_Notes = ""
}

C_OP_BasicMovement C_OP_BasicMovement_0
{
	float(3) m_Gravity = ( 0.000000, 0.000000, -100.000000 )
	string m_Notes = ""
}

C_OP_Decay C_OP_Decay_0
{
	string m_Notes = ""
}

C_OP_Noise C_OP_Noise_0
{
	float m_fl4NoiseScale = 0.001310
	int m_nFieldOutput = 4
	float m_flOutputMax = 130.000000
	bool m_bAdditive = true
	string m_Notes = ""
}

C_OP_InterpolateRadius C_OP_InterpolateRadius_0
{
	float m_flBias = 0.250000
	float m_flEndScale = 0.000000
	string m_Notes = ""
}

C_OP_VectorNoise C_OP_VectorNoise_0
{
	bool m_bAdditive = true
	float(3) m_vecOutputMax = ( 4.000000, 4.000000, 4.000000 )
	float(3) m_vecOutputMin = ( -4.000000, -4.000000, -4.000000 )
	int m_nFieldOutput = 0
	float m_fl4NoiseScale = 0.500000
	string m_Notes = ""
}

C_INIT_RandomLifeTime C_INIT_RandomLifeTime_0
{
	float m_fLifetimeMax = 2.000000
	float m_fLifetimeMin = 0.450000
	string m_Notes = ""
}

C_INIT_CreateWithinSphere C_INIT_CreateWithinSphere_0
{
	int m_nControlPointNumber = 3
	float m_fRadiusMax = 16.000000
	float m_fSpeedMax = 64.000000
	float(3) m_LocalCoordinateSystemSpeedMin = ( 50.000000, 0.000000, 0.000000 )
	float(3) m_LocalCoordinateSystemSpeedMax = ( 40.000000, 0.000000, 0.000000 )
	string m_Notes = ""
}

C_INIT_InitialVelocityNoise C_INIT_InitialVelocityNoise_0
{
	float(3) m_vecOutputMax = ( 24.000000, 24.000000, 24.000000 )
	float(3) m_vecOutputMin = ( -24.000000, -24.000000, -24.000000 )
	bool m_bLocalSpace = true
	string m_Notes = ""
}

C_INIT_CreationNoise C_INIT_CreationNoise_0
{
	float m_flOutputMax = 4.000000
	float m_flOutputMin = 2.000000
	float m_flNoiseScale = 2.000000
	string m_Notes = ""
}

C_INIT_RandomRotation C_INIT_RandomRotation_0
{
	string m_Notes = ""
}

C_INIT_RandomYaw C_INIT_RandomYaw_0
{
	float m_flDegreesMax = 4.000000
	float m_flDegreesMin = -4.000000
	string m_Notes = ""
}

C_INIT_RandomYawFlip C_INIT_RandomYawFlip_0
{
	string m_Notes = ""
}

C_INIT_RemapParticleCountToScalar C_INIT_RemapParticleCountToScalar_0
{
	int m_nInputMax = 4
	bool m_bScaleInitialRange = true
	bool m_bActiveRange = true
	string m_Notes = ""
}

C_INIT_RandomSequence C_INIT_RandomSequence_0
{
	int m_nSequenceMax = 63
	string m_Notes = ""
}

C_OP_ContinuousEmitter C_OP_ContinuousEmitter_0
{
	float m_flEmitRate = 256.000000
	string m_Notes = ""
}